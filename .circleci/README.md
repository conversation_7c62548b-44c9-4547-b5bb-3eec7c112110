## Circle CI/CD automation

The script loads all its environment variables from the [backend/.circleci](https://vault.dotenv.org/ui/ui1/project/NGCKYo/environment/A6COdKW.) dotenv vault.

It uses the "<EMAIL>" gmail account to authenticate the user to the vault.

The GCP user "circleci-user@ENVIRONMENT" service account is expected to have permissions that enable it:

- to connect to the database and run migrations
- to docker build and deploy images to artifact registry
- to deploy cloudrun functions (for now)
