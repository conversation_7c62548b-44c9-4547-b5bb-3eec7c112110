version: 2.1
orbs:
  gcp-cli: circleci/gcp-cli@3.3.0

commands:
  setup_credentials:
    parameters:
      env:
        type: string
        default: ''
    steps:
      - run: npx dotenv-vault new ${DOTENV_VAULT}
      - run: npx dotenv-vault login ${DOTENV_ME} -y
      - run: npx dotenv-vault pull << parameters.env >> -m ${DOTENV_ME}
      - run: set -a && source .env.<< parameters.env >> && echo ${GOOGLE_APPLICATION_CREDENTIALS_B64} | base64 -d >> ci-service-account.json
      - run: echo 'export GCLOUD_SERVICE_KEY=$(cat ci-service-account.json)' >> "$BASH_ENV"
      - run: echo 'source .env.<< parameters.env >>' >> "$BASH_ENV"

  setup_sqlproxy:
    steps:
      - run:
          name: Download sqlproxy
          command: |
            curl -o ./cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.13.0/cloud-sql-proxy.linux.amd64
            chmod +x cloud-sql-proxy

  install_deps:
    steps:
      - restore_cache:
          keys:
            - v5-cache-node-modules-full-

      - run: npm install --prefix=$HOME/.local --global pnpm@8.15.7
      - run: pnpm install --frozen-lockfile

      - save_cache:
          paths:
            - ./node_modules
          key: v5-cache-node-modules-full-{{ checksum "pnpm-lock.yaml" }}

jobs:
  migrate_database:
    parameters:
      env:
        type: string
        default: ''
      cloudsql_instance_name:
        type: string
        default: ''
    working_directory: ~/comissionamento-backend

    docker:
      - image: cimg/node:23.8

    steps:
      - checkout
      - setup_credentials:
          env: << parameters.env >>
      - gcp-cli/setup:
          version: 413.0.0

      - install_deps
      - setup_sqlproxy

      - run:
          name: Set Environment Variable of << parameters.env >> Database
          command: |
            echo 'export DATABASE_HOST=localhost' >> $BASH_ENV
            echo 'export DATABASE_PORT=3306' >> $BASH_ENV
            echo 'export DATABASE_USER=circleci-user' >> $BASH_ENV
            echo 'export DATABASE_DB=comissionamento' >> $BASH_ENV
            echo 'export DOCKER_IMAGE_NAME=bff-backend' >> "$BASH_ENV"
            source $BASH_ENV

      - run:
          name: Run migrations of << parameters.env >> Database
          no_output_timeout: 30m
          command: |
            ./cloud-sql-proxy --auto-iam-authn --port 3306 -c ci-service-account.json << parameters.cloudsql_instance_name >> & pnpm run db:migrate

  test:
    working_directory: ~/comissionamento-backend
    resource_class: xlarge

    docker:
      - image: cimg/gcp:2022.11
      - image: cimg/mysql:8.0.33
        command:
          [
            '--max_allowed_packet',
            '256M',
            '--max_connections',
            '700',
            '--transaction_isolation',
            'READ-COMMITTED',
          ]
        environment:
          MYSQL_ROOT_PASSWORD: 'j@JI#J!2j392j0J)@!)j201'
          MYSQL_DATABASE: comissionamento

      - image: us-east1-docker.pkg.dev/splitc-global/docker-images/matcher-api@sha256:9a9aa243e54c21c75a84add70aaf182cceb8be00bedc92e972767793ab87daa5
        auth:
          username: _json_key # default username when using a JSON key file to authenticate
          password: $GCLOUD_SERVICE_KEY # JSON service account you created, do not encode to base64
        command:
        environment:
          GIN_MODE: release
          DB_ADDRESS: '127.0.0.1'
          PRETTY_LOG: true
          DB_USER: root
          DB_PASS: 'j@JI#J!2j392j0J)@!)j201'
          DB_TLS_ENABLED: false
          LOG_LEVEL: debug
          APP_PORT: 8089
          WAIT_FOR_DB: true

    parallelism: 4
    steps:
      - checkout

      - run:
          name: Set Environment Variable
          command: |
            echo 'export DATABASE_HOST=localhost' >> $BASH_ENV
            echo 'export DATABASE_PORT=3306' >> $BASH_ENV
            echo 'export DATABASE_USER=root' >> $BASH_ENV
            echo 'export DATABASE_PWD="j@JI#J!2j392j0J)@!)j201"' >> $BASH_ENV
            echo 'export DATABASE_DB=comissionamento' >> $BASH_ENV
            source $BASH_ENV

      - run:
          name: Waiting for MySQL to be ready
          command: |
            for i in `seq 1 10`;
            do
              nc -z 127.0.0.1 3306 && echo Success && exit 0
              echo -n .
              sleep 1
            done
            echo Failed waiting for MySQL && exit 1

      - install_deps

      - run:
          name: Create database
          command: pnpm run predb:migrate

      - run:
          name: Run migrations
          command: pnpm run db:migrate

      - run:
          name: Run integration tests
          command: |
            set -e
            TEST_FILES=$(circleci tests glob "**/*.*spec.ts" | circleci tests split --split-by=timings)
            NODE_ENV='test' NODE_OPTIONS='--max-old-space-size=8192' pnpm run test --coverage $TEST_FILES

      - store_test_results:
          path: test-results

  test_build:
    parameters:
      env:
        type: string
        default: ''
    working_directory: ~/comissionamento-backend

    docker:
      - image: cimg/node:23.8

    steps:
      - checkout
      - setup_remote_docker:
          docker_layer_caching: true
      - setup_credentials:
          env: << parameters.env >>
      - run:
          name: 'build test image'
          command: |
            URL_REGISTRY="us-east1-docker.pkg.dev/splitc-global/docker-images"
            DOCKER_IMAGE_URL_BASE="$URL_REGISTRY/bff-backend:${CIRCLE_SHA1}"
            docker build --ssh default . --tag $DOCKER_IMAGE_URL_BASE

  gcp_cloudrun_deploy:
    parameters:
      env:
        type: string
        default: ''
    working_directory: ~/comissionamento-backend
    docker:
      - image: cimg/gcp:2022.11

    steps:
      - checkout
      - setup_credentials:
          env: << parameters.env >>
      - gcp-cli/setup:
          version: 413.0.0

      - setup_remote_docker:
          docker_layer_caching: true
      - run:
          name: 'build and push to registry'
          command: |
            URL_REGISTRY="us-east1-docker.pkg.dev/splitc-global/docker-images"
            DOCKER_IMAGE_URL_BASE="$URL_REGISTRY/bff-backend"

            BASE_IMAGE_NAME="$DOCKER_IMAGE_URL_BASE:${CIRCLE_SHA1}"
            ENVIRONMENT_IMAGE_NAME="$DOCKER_IMAGE_URL_BASE:<< parameters.env >>-<< pipeline.number >>"

            docker build --ssh default . --tag $BASE_IMAGE_NAME
            docker tag $BASE_IMAGE_NAME $ENVIRONMENT_IMAGE_NAME

            gcloud auth configure-docker us-east1-docker.pkg.dev
            docker push $BASE_IMAGE_NAME
            docker push $ENVIRONMENT_IMAGE_NAME

      # Deploy
      - run: >
          gcloud run deploy rules-applier-sync
          --platform=managed
          --image us-east1-docker.pkg.dev/splitc-global/docker-images/bff-backend:${CIRCLE_SHA1}
          --update-env-vars OUTBOUND_WEBHOOK_PROCESSOR_ON='false'
          --region=${GOOGLE_COMPUTE_REGION}

  temp_backend_deploy:
    parameters:
      env:
        type: string
        default: ''
    working_directory: ~/comissionamento-backend

    docker:
      - image: cimg/gcp:2022.11

    steps:
      - checkout
      - setup_credentials:
          env: << parameters.env >>
      - setup_remote_docker:
          docker_layer_caching: true
      - gcp-cli/setup:
          version: 413.0.0

      # Deploy
      - run:
          name: Copying bff-api.yaml and closure-backup.yaml from rules-applier-sync
          command: |
            gcloud run services describe rules-applier-sync \
            --platform managed \
            --region southamerica-east1 \
            --format export > base-service.yaml

      - run:
          name: Build the image and push to the repository
          command: |
            URL_REGISTRY="us-east1-docker.pkg.dev/splitc-global/docker-images"
            DOCKER_IMAGE_URL_BASE="$URL_REGISTRY/bff-backend"
            export DOCKER_IMAGE_URL="$DOCKER_IMAGE_URL_BASE:${CIRCLE_SHA1}"

            docker build --ssh default . --tag $DOCKER_IMAGE_URL
            gcloud auth configure-docker us-east1-docker.pkg.dev
            docker push ${DOCKER_IMAGE_URL}

            curl -L https://github.com/mikefarah/yq/releases/download/v4.2.0/yq_linux_amd64 --output /usr/local/bin/yq && chmod +x /usr/local/bin/yq

            yq e -i '.metadata.annotations.["client.knative.dev/user-image"] = "'"${DOCKER_IMAGE_URL}"'"' base-service.yaml
            yq e -i '.spec.template.metadata.annotations.["client.knative.dev/user-image"] = "'"${DOCKER_IMAGE_URL}"'"' base-service.yaml
            yq e -i '.spec.template.spec.containers[0].image = "'"${DOCKER_IMAGE_URL}"'"' base-service.yaml
            yq -i eval 'del(.spec.traffic[1].revisionName)' base-service.yaml

      - run:
          name: Deploy bff-api
          command: |
            export BRANCH_HASH=$(echo -n ${CIRCLE_BRANCH} | sha256sum | head -c 8)
            RND_NUMBER=$(shuf -i 1-100000 -n 1)

            cp base-service.yaml bff-api.yaml

            yq e -i '.metadata.name = "temp-backend-'"${BRANCH_HASH}"'"' bff-api.yaml
            yq e -i '.spec.template.metadata.name = "temp-backend-'"${BRANCH_HASH}"'-'"${RND_NUMBER}"-revision'"' bff-api.yaml

            script -q -c "gcloud run services replace bff-api.yaml --platform managed --region southamerica-east1" >> log.txt

            gcloud run services add-iam-policy-binding "temp-backend-${BRANCH_HASH}" \
            --member="allUsers" \
            --platform managed --region southamerica-east1 \
            --role="roles/run.invoker"

            gcloud run services update "temp-backend-${BRANCH_HASH}" \
            --platform managed --region southamerica-east1 \
            --update-env-vars OUTBOUND_WEBHOOK_PROCESSOR_ON='false'

      - run:
          name: Deploy closure-backup
          command: |
            modified_files=$(git diff-tree --name-only -r $CIRCLE_SHA1)
            files_to_check=$(cat src/closure.backup/closure.backup.module.ts |  grep -oP "from '\K[^@']*" | sed "s/\./src/i" | sed "s/$/.ts/")
            files_to_check+='
            src/closure.backup/closure.backup.module.ts'

            deploy_closure_backup="false"

            for arquivo in $modified_files; do
              if echo "$files_to_check" | grep -q "^$arquivo$"; then
                deploy_closure_backup="true"
              fi
            done

            if [ "$deploy_closure_backup" = "true" ]; then
              export BRANCH_HASH=$(echo -n ${CIRCLE_BRANCH} | sha256sum | head -c 8)
              RND_NUMBER=$(shuf -i 1-100000 -n 1)

              cp base-service.yaml closure-backup.yaml

              yq e -i '.metadata.name = "temp-closure-backup-'"${BRANCH_HASH}"'"' closure-backup.yaml
              yq e -i '.spec.template.metadata.name = "temp-closure-backup-'"${BRANCH_HASH}"'-'"${RND_NUMBER}"-revision'"' closure-backup.yaml

              yq e -i '.spec.template.spec.containers[0].command = ["node", "closure.backup/main.js"]' closure-backup.yaml

              script -q -c "gcloud run services replace closure-backup.yaml --platform managed --region southamerica-east1" >> temp.txt
              cat temp.txt
              cat temp.txt >> log.txt

              gcloud run services add-iam-policy-binding "temp-closure-backup-${BRANCH_HASH}" \
              --member="allUsers" \
              --platform managed --region southamerica-east1 \
              --role="roles/run.invoker"
            else
              echo "closure-backup service hasn't changed, skipping deployment"
            fi

      - run:
          name: Post Stats to GitHub PR
          command: |
            pr_response=$(curl --location --request GET "https://api.github.com/repos/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pulls?head=$CIRCLE_PROJECT_USERNAME:$CIRCLE_BRANCH&state=open" \
            -u ${GITHUB_USER}:${GITHUB_KEY})

            if [ $(echo $pr_response | jq length) -eq 0 ]; then
              echo "No PR found to update"
            else
              pr_comment_url=$(echo $pr_response | jq -r ".[]._links.comments.href")
            fi

            sed -i.bak 's/\r$//' log.txt

            export COMMENT=$(cat log.txt | grep URL)

            curl -i --location --request POST "$pr_comment_url" \
            -u ${GITHUB_USER}:${GITHUB_KEY} \
            --header 'Content-Type: application/json' \
            -d '{ "body": "'"$COMMENT"'" }'

workflows:
  version: 2.1

  test_workflow:
    jobs:
      - test_build:
          filters:
            branches:
              ignore: /release\/.*|master/
          env: staging

      - test:
          context:
            - gcp-ctx-stg
          filters:
            branches:
              ignore: /release\/.*|master/

  temp_backend:
    jobs:
      - temp_backend_deploy:
          filters:
            branches:
              ignore: /release\/.*|master/
          env: staging

  build_staging:
    jobs:
      - migrate_database:
          filters:
            branches:
              only: master
          env: staging
          cloudsql_instance_name: splitc-staging:southamerica-east1:comissionamento-staging-v5

      - gcp_cloudrun_deploy:
          filters:
            branches:
              only: master
          requires:
            - migrate_database
          env: staging

  build_production:
    jobs:
      - migrate_database:
          name: 'migrate_prod_database'
          filters:
            branches:
              ignore: /.*/
            tags:
              only: /.*/
          env: production
          cloudsql_instance_name: splitc-production:southamerica-east1:comissionamento-production-v4

      - migrate_database:
          name: 'migrate_internal_database'
          filters:
            branches:
              ignore: /.*/
            tags:
              only: /.*/
          env: internal
          cloudsql_instance_name: splitc-internal:southamerica-east1:comissionamento-internal

      - gcp_cloudrun_deploy:
          filters:
            branches:
              ignore: /.*/
            tags:
              only: /.*/
          requires:
            - migrate_prod_database
            - migrate_internal_database
          env: production
#
