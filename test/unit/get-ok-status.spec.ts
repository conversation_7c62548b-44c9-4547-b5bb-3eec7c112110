import { getOkStatus } from '../../src/outbound.webhook/utils/get-ok-status.util';

describe('getOkStatus', () => {
  it.each([
    { statusCode: 500, wasFetchSuccessful: true, successfulStatusCodes: ['xxx'], expected: true },
    { statusCode: 200, wasFetchSuccessful: true, successfulStatusCodes: undefined, expected: true },
    { statusCode: 301, wasFetchSuccessful: true, successfulStatusCodes: undefined, expected: true },
    {
      statusCode: 404,
      wasFetchSuccessful: false,
      successfulStatusCodes: undefined,
      expected: false,
    },
    {
      statusCode: 503,
      wasFetchSuccessful: false,
      successfulStatusCodes: undefined,
      expected: false,
    },

    {
      statusCode: 200,
      wasFetchSuccessful: false,
      successfulStatusCodes: ['2xx', '404'],
      expected: true,
    },
    {
      statusCode: 204,
      wasFetchSuccessful: false,
      successfulStatusCodes: ['2xx', '404'],
      expected: true,
    },
    {
      statusCode: 404,
      wasFetchSuccessful: false,
      successfulStatusCodes: ['2xx', '404'],
      expected: true,
    },
    {
      statusCode: 500,
      wasFetchSuccessful: false,
      successfulStatusCodes: ['2xx', '404'],
      expected: false,
    },

    { statusCode: 201, wasFetchSuccessful: false, successfulStatusCodes: ['20x'], expected: true },
    { statusCode: 202, wasFetchSuccessful: false, successfulStatusCodes: ['20x'], expected: true },
    { statusCode: 203, wasFetchSuccessful: false, successfulStatusCodes: ['20x'], expected: true },
    { statusCode: 210, wasFetchSuccessful: false, successfulStatusCodes: ['20x'], expected: false },

    {
      statusCode: 201,
      wasFetchSuccessful: false,
      successfulStatusCodes: ['201', '202', '203'],
      expected: true,
    },
    {
      statusCode: 202,
      wasFetchSuccessful: false,
      successfulStatusCodes: ['201', '202', '203'],
      expected: true,
    },
    {
      statusCode: 203,
      wasFetchSuccessful: false,
      successfulStatusCodes: ['201', '202', '203'],
      expected: true,
    },
    {
      statusCode: 204,
      wasFetchSuccessful: false,
      successfulStatusCodes: ['201', '202', '203'],
      expected: false,
    },
  ])(
    'should return $expected when statusCode=$statusCode and successfulStatusCodes=$successfulStatusCodes',
    ({ statusCode, wasFetchSuccessful, successfulStatusCodes, expected }) => {
      const result = getOkStatus(statusCode, wasFetchSuccessful, successfulStatusCodes);
      expect(result).toBe(expected);
    },
  );
});
