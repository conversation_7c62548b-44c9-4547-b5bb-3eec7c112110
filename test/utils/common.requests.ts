import { payrollConfigUpdateDto } from '@app/controller/payroll/dto/payroll.config.update.dto';
import { ProcessPayroll } from '@app/controller/payroll/schema';
import { WEBHOOKS_DEFAULT_ROUTE } from '@app/domain/payment.domain';

import { UpsertPermissionProfileParams } from '@app/dto/backoffice/admin.profiles.dto';
import { ConsultOidcProvidersRequestDTO } from '@app/dto/consult.oidc.providers.dto';
import { CopyPlanRequestDTO } from '@app/dto/copy.plan.request.dto';
import { ImplicitLoginRequestDTO, LoginByOidcRequestDTO } from '@app/dto/login.request.dto';
import { PUBSUB_FROM_HEADER } from '@app/interceptors/pubsub.middleware';
import { AtributosEscritorio } from '@app/models/atributosescritorio';
import { BalanceEventAttributes, Balance_Event } from '@app/models/balance_event';
import { Comissao } from '@app/models/comissao';
import { Company } from '@app/models/company';
import { Creditor_Payment_Info } from '@app/models/creditor_payment_info';
import { Creditor_Teams } from '@app/models/creditor_teams';
import { Customer_Assets } from '@app/models/customer_assets';
import { Fechamento } from '@app/models/fechamento';
import { File } from '@app/models/file';
import { Financial_Institution } from '@app/models/financial_institution';

import { Payment, PaymentAttributes } from '@app/models/payment';
import { Payment_Group } from '@app/models/payment_group';
import { Payment_Log } from '@app/models/payment_log';
import { Teams } from '@app/models/teams';
import { StarkEvent } from '@app/services/baas/stark/types';
import { REFRESH_TOKEN_COOKIE_KEY } from '@app/shared/constants/cookie';
import { tryParse } from '@app/utils/date.utils';
import { addDecimalSeparator } from '@app/utils/number.utils';
import { HttpStatus, INestApplication } from '@nestjs/common';
import {
  AccountType,
  AdjustmentApprovalRequest,
  BackendSignRequest,
  BulkUpdateCreditorPaymentInfoDto,
  BulkUpsertUserCheckResponse,
  BulkUpsertUserPermissionCheckResponse,
  ChangePasswordRequest,
  CloseMonthRequest,
  CloseMonthSuccessResponse,
  ClosureEventTypes,
  ClosureMustUpdateEvent,
  ClosureVariableType,
  CommissionResponse,
  CompanyConfig,
  CopyPermissionProfileRequest,
  CreateAdjustmentRequest,
  CreateCreditorPaymentInfoDto,
  CreateDocumentResponse,
  CreateExtractionConfigRequest,
  CreatePacketsRequest,
  CreatePayrollRequestPayload,
  CreateWebhookRequest,
  CustomerData,
  DataSourceRequest,
  DocumentApproval,
  DocumentPaymentStatus,
  DocumentRequest,
  DocumentSignatureHookEvent,
  EvaluateDocumentApprovalRequest,
  GetAllCreditorsEarningsResponse,
  GetCreditorEarnings,
  GetCurrentPayrollFilters,
  GetDocumentsFilterBy,
  GetDocumentsSchema,
  GetInquiriesFiltersRequest,
  GetInquiryCreditors,
  GetPaginatedDocumentResponse,
  GetPaymentInfoResponse,
  GetPayrollGroupingsResponse,
  GoalRequest,
  GoalsPreferencesRequest,
  InsertCommissionRequest,
  ListPaymentRequestsFilters,
  OAuthFinishDto,
  OAuthGetResponse,
  OAuthStartDto,
  OAuthStartResponse,
  OcrContext,
  OfficePreferenceType,
  OfficeType,
  OfficeVariableRequest,
  PacketTypeEnum,
  PatchPacketRequest,
  PayerOptionRequest,
  PaymentGroupRequest,
  PaymentStatus,
  PaymentType,
  PaymentTypes,
  PayrollGraphType,
  PayrollPatchConfig,
  PayrollPostConfig,
  PayrollSettingsResponse,
  PermissionData,
  PermissionProfileRequest,
  PermissionProfileResponse,
  PermissionProfilesPriorityRequest,
  PermissionsRequest,
  PlanGroupPermissionRequest,
  PreferencesType,
  RedirectResponse,
  ReplacementGroups,
  RulesPreference,
  SimplePayrollConfigResponse,
  TeamsPreferenceResponse,
  TeamsResponse,
  TesterWebhookRequest,
  TriggerRequest,
  UpdateCreditorPaymentInfoDto,
  UpdateDocumentRequest,
  UpdateInquiryStatusRequest,
  UpdateOcrValidation,
  UpdateWebhookRequest,
  User,
  UserBulkUpsertQueryRequest,
  UserBulkUpsertRequest,
  UserBulkUpsertRequestFields,
  UserCreateRequest,
  UserPermissionBulkUpsertQueryRequest,
  UserPermissionBulkUpsertRequest,
  UsersClaims,
  ValidationConfig,
  VariableRequest,
  WhoIsResponse,
  isClosureCommissionBackupEvent,
  UpdateWorkflowInputs,
  UserManagementUpsertDto,
  ObjectWorkflowUpdateStatusResponse,
  ObjectWorkflowType,
  UserManagementDiffResponse,
  PaymentCheckoutRequest,
  CreatePaymentResponse,
  ObjectWorkflowStatusFilters,
  WorkflowStatusUpdateUnion,
  AiChatTokenResponse,
  PostPayrollGroupBody,
  ChatTokenRequestSchemaDto,
  PatchPayrollGroupBody,
  GetPayrollGroupResponse,
} from 'shared-types';
import request from 'supertest';
import { v4 as uuid } from 'uuid';
import { PayrollNotifyDto } from '../../src/controller/payroll/dto/payroll.notify.dto';
import { Credor } from '../../src/models/credor';
import { Escritorio } from '../../src/models/escritorio';
import { GoalMetricAttributes, Goal_Metric } from '../../src/models/goal_metric';
import { uploadFile } from './massa.utils';
import { vi } from 'vitest';
import { WEBHOOKS_DEFAULT_ROUTE_V2 } from '@app/services/baas/stark/starkbank.baas.service';

vi.mock('../../src/domain/preferences/preferences.domain');

function binaryParser(res, callback) {
  res.setEncoding('binary');
  res.data = '';
  res.on('data', function (chunk) {
    res.data += chunk;
  });
  res.on('end', function () {
    callback(null, Buffer.from(res.data, 'binary'));
  });
}

export type SupertestResponse<T = any> = Omit<request.Response, 'body'> & { body: T };

export interface GetAllCreditorsEarningsV2Params {
  app: any;
  office: Escritorio;
  closureDate: string;
  expect?: HttpStatus;
  userClaims?: any;
  query?: {
    pageSize?: number;
    from?: number;
    to?: number;
  };
}

export const makeSupertest = (app: INestApplication) => {
  return request(app.getHttpServer());
};

export type SupertestAgent = ReturnType<typeof makeSupertest>;

const handleResponse = <T = any>({
  path,
  response,
  expectedHttpStatus,
  method,
}: {
  path: string;
  response: request.Response;
  expectedHttpStatus: HttpStatus;
  method?: string;
}): SupertestResponse<T> => {
  try {
    if (expectedHttpStatus !== response.status) {
      throw new Error(
        `Expected status ${expectedHttpStatus}. Got ${
          response.status
        }. Endpoint: ${path}. Response: ${JSON.stringify(response.body)}`,
      );
    }
    return response;
  } catch (err) {
    throw new Error(
      `error on request ${method} ${path}.\n ${err.message}. \n ${JSON.stringify(response.body)}`,
    );
  }
};

export const docsReadme = async (app: any) => {
  return (
    await request(app.getHttpServer())
      .get('/docs/readme')
      .set({ Authorization: 'Bearer 123' })
      .expect(HttpStatus.OK)
  ).body as RedirectResponse;
};

export const getCommissionsEndpoint = async (
  app: any,
  office: Escritorio,
  date: string,
  origin: string = 'comissoes_xp',
) => {
  return (
    await request(app.getHttpServer())
      .get(`/offices/${office.id_escritorio}/closures/${date}/${origin}/commissions`)
      .set({ Authorization: 'Bearer 123' })
      .expect(HttpStatus.OK)
  ).body as CommissionResponse[];
};

export const getClosureOriginsEndpoint = async (
  app: any,
  office: Escritorio,
  date: string,
  commissionType: number,
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  return request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/closures/${date}/origins`)
    .query({ commission_type: commissionType })
    .set({ Authorization: 'Bearer 123' })
    .expect(expectedHttpStatus);
};

export const deleteClosureEndpoint = async (
  app: any,
  office: Escritorio,
  date: string,
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  return request(app.getHttpServer())
    .delete(`/offices/${office.id_escritorio}/closures/${date}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectedHttpStatus);
};

export const deleteClosureCommissionsEndpoint = async (
  app: any,
  office: Escritorio,
  date: string,
  { commission_type, origin }: { commission_type?: number; origin?: string },
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  return request(app.getHttpServer())
    .delete(`/offices/${office.id_escritorio}/closures/${date}/commissions`)
    .query({ commission_type, origin })
    .set({ Authorization: 'Bearer 123' })
    .expect(expectedHttpStatus);
};

export const putCommissionAdjustmentsEndpoint = async (
  app: any,
  office: Escritorio,
  date: string,
  payload: any,
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
  agent?: SupertestAgent,
): Promise<CloseMonthSuccessResponse> => {
  const path = `/offices/${office.id_escritorio}/preferences/${date}/${PreferencesType.ADJUSTMENTS}`;

  const ag = agent ?? request(app.getHttpServer());
  const response = await ag.put(path).set({ Authorization: 'Bearer 123' }).send(payload);

  try {
    if (expectedHttpStatus !== response.status) {
      throw new Error(`Expected status ${expectedHttpStatus}. Got ${response.status}`);
    }
    return response.body;
  } catch (err) {
    throw new Error(
      `error on request ${path}.\n ${err.message}. \n ${JSON.stringify(response.body)}`,
    );
  }
};

export const putCommissionReplacementsEndpoint = async (
  app: any,
  office: Escritorio,
  date: string,
  payload: ReplacementGroups[],
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${office.id_escritorio}/preferences/${date}/${PreferencesType.REPLACEMENTS}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(payload);

  return handleResponse({ method: 'PUT', path, response, expectedHttpStatus });
};

export const getClosurePreference = async (
  app: any,
  {
    office,
    closureDate,
    expectedHttpStatus = HttpStatus.OK,
    type,
    query,
  }: {
    office: Escritorio;
    closureDate: string;
    expectedHttpStatus?: HttpStatus;
    type: PreferencesType;
    query?: { version: string; versionDate: string };
  },
) => {
  const path = `/offices/${office.id_escritorio}/preferences/${closureDate}/${type}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).query(query).set(headers);

  return handleResponse({ method: 'GET', path, response, expectedHttpStatus });
};

export const putTaxesEndpoint = async (
  app: any,
  office: Escritorio,
  date: string,
  taxesToSend: any,
  categoriesToIgnore: string[] = [],
  expectStatus: HttpStatus = HttpStatus.OK,
  agent?: SupertestAgent,
) => {
  let ignore = '';
  if (categoriesToIgnore && categoriesToIgnore.length > 0) ignore = categoriesToIgnore.join(',');
  ignore = ignore ? `?categorias_excluidas=${ignore.toString()}` : '';

  const ag = agent ?? request(app.getHttpServer());

  return ag
    .put(`/offices/${office.id_escritorio}/preferences/${date}/${PreferencesType.TAX}${ignore}`)
    .set({ Authorization: 'Bearer 123' })
    .send(taxesToSend)
    .expect(expectStatus);
};

export const getClosureEndpoint = async (
  app: any,
  office: Escritorio,
  date: string,
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  return request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/closures/${date}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
};

export const createUserEndpoint = async (
  app: any,
  office: Escritorio,
  payload: UserCreateRequest,
  expectStatus: HttpStatus,
): Promise<any> => {
  return request(app.getHttpServer())
    .post(`/user/office/${office.id_escritorio}`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const createOfficeEndpoint = async (
  app: any,
  officeName: string,
  payload: { inherit_profile_ids: number[]; office_type: OfficeType; from_office?: number },
  expectStatus: HttpStatus = HttpStatus.OK,
): Promise<any> => {
  return request(app.getHttpServer())
    .put(`/office/${officeName}`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const getManagedProfiles = async (
  app: any,
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  let path = `/backoffice/profiles`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);
  return handleResponse({ path, response, expectedHttpStatus });
};

export const createCreditorIfDoesntExist = async (
  officeId: number,
  externalCreditorId: string,
): Promise<Credor> => {
  const existing = await Credor.findOne({
    where: { escritorio_id_escritorio: officeId, id_credor_externo: externalCreditorId },
  });
  if (existing) return existing;
  return Credor.create({
    escritorio_id_escritorio: officeId,
    id_credor_externo: externalCreditorId,
    ativo: 1,
  });
};

export const updateUserEndpoint = async (
  app: any,
  office: Escritorio,
  externalCreditorId: string,
  payload: any,
  expectStatus: HttpStatus = HttpStatus.OK,
): Promise<any> => {
  return request(app.getHttpServer())
    .patch(`/user/office/${office.id_escritorio}/${encodeURIComponent(externalCreditorId)}`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const autoProcessEndpoint = async (
  app: INestApplication<any>,
  office: Escritorio,
  expectStatus: HttpStatus = HttpStatus.OK,
): Promise<any> => {
  return request(app.getHttpServer())
    .post(`/offices/${office.id_escritorio}/user_management/process`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
};


export const bulkUpsertUserEndpoint = async (
  app: any,
  office: Escritorio,
  payload: UserBulkUpsertRequest[],
  expectStatus: HttpStatus,
  fieldsToUpdate?: UserBulkUpsertRequestFields,
): Promise<any> => {
  return request(app.getHttpServer())
    .put(`/user/office/${office.id_escritorio}`)
    .set({ Authorization: 'Bearer 123' })
    .send({
      fields_to_update: fieldsToUpdate
        ? fieldsToUpdate
        : {
            name: true,
            email: true,
            active: true,
          },
      payload,
    })
    .expect(expectStatus);
};

export const bulkUpsertUserPermissionEndpoint = async (
  app: any,
  office: Escritorio,
  payload: UserPermissionBulkUpsertRequest[],
  expectStatus: HttpStatus,
): Promise<any> => {
  return request(app.getHttpServer())
    .put(`/user/office/${office.id_escritorio}/permissions`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const upsertManagedProfileEndpoint = async <T extends PermissionProfileResponse = any>(
  app: any,
  payload: UpsertPermissionProfileParams,
  expectedHttpStatus: HttpStatus,
): Promise<SupertestResponse<T>> => {
  const path = `/backoffice/profile`;
  const response = await request(app.getHttpServer())
    .put(path)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectedHttpStatus);
  return handleResponse<T>({ path, response, expectedHttpStatus });
};

export const bulkUpsertUserQueryEndpoint = async (
  app: any,
  office: Escritorio,
  payload: UserBulkUpsertQueryRequest[],
  expectStatus: HttpStatus,
): Promise<UserBulkUpsertRequest[]> => {
  return (
    await request(app.getHttpServer())
      .post(`/user/office/${office.id_escritorio}/query`)
      .send(payload)
      .expect(expectStatus)
  ).body;
};

export const bulkUpsertUserPermissionQueryEndpoint = async (
  app: any,
  office: Escritorio,
  payload: UserPermissionBulkUpsertQueryRequest[],
  expectStatus: HttpStatus,
): Promise<UserPermissionBulkUpsertRequest[]> => {
  return (
    await request(app.getHttpServer())
      .post(`/user/office/${office.id_escritorio}/permissions/query`)
      .send(payload)
      .expect(expectStatus)
  ).body;
};

export const bulkUpsertUserConfigEndpoint = async (
  app: any,
  office: Escritorio,
  type: 'user' | 'permissions',
  expectStatus: HttpStatus,
): Promise<(UserBulkUpsertQueryRequest | UserPermissionBulkUpsertQueryRequest)[]> => {
  const url =
    type === 'user'
      ? `/user/office/${office.id_escritorio}/query`
      : `/user/office/${office.id_escritorio}/permissions/query`;

  return (await request(app.getHttpServer()).get(url).expect(expectStatus)).body;
};

export const bulkUpsertUserCheckEndpoint = async (
  app: any,
  office: Escritorio,
  payload: UserBulkUpsertRequest[],
  expectStatus: HttpStatus,
  fieldsToUpdate?: UserBulkUpsertRequestFields,
): Promise<BulkUpsertUserCheckResponse[]> => {
  return (
    await request(app.getHttpServer())
      .post(`/user/office/${office.id_escritorio}/check`)
      .send({
        fields_to_update: fieldsToUpdate
          ? fieldsToUpdate
          : {
              name: true,
              email: true,
              active: true,
            },
        payload,
      })
      .expect(expectStatus)
  ).body;
};

export const bulkUpsertUserPermissionCheckEndpoint = async (
  app: any,
  office: Escritorio,
  payload: UserPermissionBulkUpsertRequest[],
  expectStatus: HttpStatus,
): Promise<BulkUpsertUserPermissionCheckResponse[]> => {
  return (
    await request(app.getHttpServer())
      .post(`/user/office/${office.id_escritorio}/permissions/check`)
      .send(payload)
      .expect(expectStatus)
  ).body;
};

export const userManagementMakeUpsert = async (
  app: any,
  office: Escritorio,
  payload: UserManagementUpsertDto,
  expectStatus: HttpStatus,
): Promise<void> => {
  return (
    await request(app.getHttpServer())
      .put(`/offices/${office.id_escritorio}/user_management/upsert`)
      .send(payload)
      .expect(expectStatus)
  ).body;
};

export const userManagementUpsertDiffEndpoint = async (
  app: any,
  office: Escritorio,
  payload: UserManagementUpsertDto,
  expectStatus: HttpStatus,
): Promise<UserManagementDiffResponse> => {
  return (
    await request(app.getHttpServer())
      .post(`/offices/${office.id_escritorio}/user_management/diff`)
      .send(payload)
      .expect(expectStatus)
  ).body;
};

export const getSingleCreditorEndpoint = async (
  app: any,
  office: Escritorio,
  externalCreditorId: string,
  expectStatus: HttpStatus,
): Promise<any> => {
  return request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/creditors/${encodeURIComponent(externalCreditorId)}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
};

export const getUsersEndpoint = async (
  app: any,
  office: Escritorio,
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  let path = `/office/${office.id_escritorio}/users`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getLoggedUserEndpoint = async (
  app: any,
  jwt: string,
  opts: {
    originIp?: string;
    expectedHttpStatus?: HttpStatus;
    headers?: Record<string, string>;
  } = {},
) => {
  let path = `/user`;
  const headers: any = { ...opts.headers, Authorization: `Bearer ${jwt}` };
  if (opts?.originIp) headers['X-forwarded-for'] = opts.originIp;

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({
    path,
    response,
    expectedHttpStatus: opts?.expectedHttpStatus ?? HttpStatus.OK,
  });
};

export const getUserEndpoint = async (
  app: any,
  office: Escritorio,
  externalCreditorId: string,
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
): Promise<SupertestResponse<User>> => {
  let path = `/office/${office.id_escritorio}/users/${externalCreditorId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deleteCreditorsEndpoint = async (
  app: any,
  office: Escritorio,
  creditorToDelete: string,
  body: { merge_commissions_to?: string } = null,
  expect: HttpStatus = HttpStatus.OK,
  updatePreferences: boolean = false,
) => {
  expect = expect ?? HttpStatus.OK;

  let path = `/offices/${office.id_escritorio}/creditors/${creditorToDelete}`;
  if (updatePreferences) path += `?update_preferences=${updatePreferences}`;

  const response = await request(app.getHttpServer())
    .delete(path)
    .send(body)
    .set({ Authorization: 'Bearer 123' });

  return handleResponse({ path, method: 'DELETE', response, expectedHttpStatus: expect });
};

export const queryCreditorsEndpoint = async (
  app: any,
  office: Escritorio,
  queryParams: any,
  expect: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${office.id_escritorio}/creditors`;
  const response = await request(app.getHttpServer())
    .get(path)
    .query(queryParams)
    .set({ Authorization: 'Bearer 123' });

  return handleResponse({ path, response, expectedHttpStatus: expect });
};

export const getOfficeTeamsEndpoint = async (
  app: any,
  office: Escritorio,
  expectStatus: HttpStatus = HttpStatus.OK,
): Promise<SupertestResponse<TeamsResponse[]>> => {
  return request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/teams`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
};

export const getSiblingDivisionsEndpoint = async (
  app: any,
  {
    office,
    commissionId,
    externalCreditorId,
  }: { office: Escritorio; commissionId: number; externalCreditorId?: string },
  expect: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${office.id_escritorio}/commissions/${commissionId}/divisions`;
  const response = await request(app.getHttpServer())
    .get(path)
    .query({ external_creditor_id: externalCreditorId })
    .set({ Authorization: 'Bearer 123' });

  return handleResponse({ path, response, expectedHttpStatus: expect });
};

export const getManagedProfileEndpoint = async <T extends PermissionProfileResponse = any>(
  app: any,
  profileId: number,
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/backoffice/profile/${profileId}`;
  const response = await request(app.getHttpServer())
    .get(path)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectedHttpStatus);
  return handleResponse<T>({ path, response, expectedHttpStatus });
};

export const preferencesSheetEndpoint = async (
  app: any,
  { office, sheetPath },
  expect?: HttpStatus,
  userClaims?: any,
) => {
  expect = expect ?? HttpStatus.OK;
  return request(app.getHttpServer())
    .get(`/office/${office.id_escritorio}/preferences/rules/from-sheet`)
    .set({ Authorization: 'Bearer 123' })
    .query({ sheet_path: sheetPath })
    .expect(expect);
};

type ApplyRulesType = {
  office: Escritorio;
  data: string;
  rules: RulesPreference;
  applyFuture?: boolean;
};

export const rulesv2 = async (
  app: any,
  { office, data, rules, applyFuture = false }: ApplyRulesType,
  expect?: HttpStatus,
) => {
  expect = expect ?? HttpStatus.OK;
  return applyRules(app, { office, data, rules, applyFuture }, expect);
};

export const applyRules = async (
  app: any,
  { office, data, rules, applyFuture = false }: ApplyRulesType,
  expect?: HttpStatus,
  agent?: SupertestAgent,
) => {
  expect = expect ?? HttpStatus.OK;
  let path = `/offices/${office.id_escritorio}/preferences/${data}/rules`;
  if (applyFuture) {
    path = `${path}?modifyFutureClosures=true`;
  }

  const ag = agent ?? request(app.getHttpServer());

  const response = await ag.put(path).set({ Authorization: 'Bearer 123' }).send(rules);

  return handleResponse({ method: 'PUT', path, response, expectedHttpStatus: expect });
};

export const legacyGetCreditorsEarnings = async (
  app: any,
  { office, creditorId, date },
  expect?: HttpStatus,
) => {
  expect = expect ?? HttpStatus.OK;

  const path = `/offices/${office.id_escritorio}/creditors/${encodeURIComponent(
    creditorId,
  )}/commissions`;

  const response = await request(app.getHttpServer())
    .get(path)
    .query({ closureDate: date })
    .set({ Authorization: 'Bearer 123' });

  try {
    if (expect !== response.status) {
      throw new Error(`Expected status ${expect}. Got ${response.status}. endpoint: ${path}`);
    }

    return { body: response.body.results };
  } catch (err) {
    throw new Error(`error on request ${path}.\n ${err.message}. \n ${response.body}`);
  }
};

export const getCreditorEarnings = async (
  app: any,
  { office, creditorId, date },
  expect?: HttpStatus,
) => {
  expect = expect ?? HttpStatus.OK;

  const path = `/offices/${office.id_escritorio}/creditors/${encodeURIComponent(
    creditorId,
  )}/commissions`;

  const response = await request(app.getHttpServer())
    .get(path)
    .query({ closureDate: date })
    .set({ Authorization: 'Bearer 123' });

  try {
    if (expect !== response.status)
      throw new Error(`Expected status ${expect}. Got ${response.status}`);

    return { body: response.body.results };
  } catch (err) {
    throw new Error(`error on request ${path}.\n ${err.message}. \n ${response.body}`);
  }
};

export const getOwnCommissions = async (
  app: any,
  { office, creditorId, date },
  expect?: HttpStatus,
) => {
  expect = expect ?? HttpStatus.OK;

  const path = `/offices/${office.id_escritorio}/creditors/${encodeURIComponent(
    creditorId,
  )}/commissions/owned`;

  const response = await request(app.getHttpServer())
    .get(path)
    .query({ closureDate: date })
    .set({ Authorization: 'Bearer 123' });

  try {
    if (expect !== response.status)
      throw new Error(`Expected status ${expect}. Got ${response.status}`);

    return { body: response.body.results };
  } catch (err) {
    throw new Error(`error on request ${path}.\n ${err.message}. \n ${response.body}`);
  }
};

interface GetPagedCreditorEarningsParams {
  office: Escritorio;
  creditorId: string;
  date: string;
  pageSize?: number;
  from?: number;
  to?: number;
}

export const getPagedCreditorEarnings = async (
  app: INestApplication,
  { office, creditorId, date, pageSize, from, to }: GetPagedCreditorEarningsParams,
  expect = HttpStatus.OK,
) => {
  const path = `/offices/${office.id_escritorio}/creditors/${creditorId}/commissions`;

  const response = await request(app.getHttpServer())
    .get(path)
    .query({ closureDate: date, pageSize, from, to })
    .set({ Authorization: 'Bearer 123' });

  return handleResponse<GetCreditorEarnings>({ path, response, expectedHttpStatus: expect });
};

export const getAllCreditorsEarnings = async ({
  app,
  office,
  closureDate,
  expect,
  userClaims,
  cursor = 0,
  pageSize = 100000,
}: {
  app: any;
  office: Escritorio;
  closureDate: string;
  cursor?: number;
  pageSize?: number;
  expect?: HttpStatus;
  userClaims?: any;
}) => {
  expect = expect ?? HttpStatus.OK;
  const path = `/offices/${office.id_escritorio}/closures/${closureDate}/all-creditors-earnings?pageSize=${pageSize}&cursor=${cursor}`;

  const response = await request(app.getHttpServer())
    .get(path)
    .set({ Authorization: 'Bearer 123' });

  return handleResponse({ path, response, expectedHttpStatus: expect });
};

export const getAllCreditorsEarningsV2 = async <T = GetAllCreditorsEarningsResponse>({
  app,
  office,
  closureDate,
  expect,
  userClaims,
  query: { from, to, pageSize } = {},
}: GetAllCreditorsEarningsV2Params) => {
  const path = `/offices/${office.id_escritorio}/closures/${closureDate}/all-creditors-earnings/v2`;

  const query = new URLSearchParams();
  query.set('pageSize', String(pageSize ?? 100000));

  if (from) {
    query.set('from', from ? String(from) : '');
    query.set('to', to ? String(to) : '');
  }

  const response = await request(app.getHttpServer())
    .get(`${path}?${query.toString()}`)
    .set({ Authorization: 'Bearer 123' });

  return handleResponse<T>({ path, response, expectedHttpStatus: expect ?? HttpStatus.OK });
};

export const getRawCommissions = async ({
  closureDate,
  externalCreditorId,
  officeId,
}: {
  closureDate: string;
  externalCreditorId?: string;
  officeId: number;
}): Promise<Comissao[]> => {
  const closure = await Fechamento.findOne({
    where: { escritorio_id_escritorio: officeId, data_fechamento: closureDate },
  });

  if (externalCreditorId) {
    const creditor = await Credor.findOne({
      where: { escritorio_id_escritorio: officeId, id_credor_externo: externalCreditorId },
    });
    return Comissao.findAll({
      where: {
        fechamento_id_fechamento: closure.id_fechamento,
        credor_id_credor: creditor?.id_credor,
      },
    });
  }

  return Comissao.findAll({
    where: {
      fechamento_id_fechamento: closure.id_fechamento,
    },
  });
};

export const getCreditor = async (
  externalCreditorId: string,
  officeId: number,
): Promise<Credor> => {
  return Credor.findOne({
    where: { escritorio_id_escritorio: officeId, id_credor_externo: externalCreditorId },
  });
};

export const salaryPreference = async (
  app: any,
  { office, date, preferences },
  expect?: HttpStatus,
  agent?: SupertestAgent,
) => {
  expect = expect ?? HttpStatus.OK;
  const path = `/offices/${office.id_escritorio}/preferences/${date}/${PreferencesType.SALARY}`;

  const ag = agent ?? request(app.getHttpServer());

  const response = await ag.put(path).send(preferences).set({ Authorization: 'Bearer 123' });

  return handleResponse({ path, response, expectedHttpStatus: expect });
};

export const paycheckEndpoint = async (
  app: any,
  { office, date, externalCreditorId },
  expect: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${office.id_escritorio}/creditor/${encodeURIComponent(
    externalCreditorId,
  )}/paycheck`;
  const response = await request(app.getHttpServer())
    .get(path)
    .query({ closure_date: date })
    .set({ Authorization: 'Bearer 123' });
  return handleResponse({ path, response, expectedHttpStatus: expect });
};

export const payrollEndpoint = async (
  app: any,
  { office, closureDate },
  expect?: HttpStatus,
  userClaims?: any,
) => {
  expect = expect ?? HttpStatus.OK;
  return request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/closures/${closureDate}/payroll`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expect);
};

export const getPayroll = async (app: any, { office, closureDate }, expect?: HttpStatus) => {
  expect = expect ?? HttpStatus.OK;

  return request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/closures/${closureDate}/payroll`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expect);
};

export const officeRoaEndpoint = async (
  app: any,
  { office },
  expect?: HttpStatus,
  userClaims?: any,
) => {
  expect = expect ?? HttpStatus.OK;
  return request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/roa`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expect);
};

export const putListPreference = async (
  app: any,
  {
    office,
    closureDate,
    listName,
    listValues,
  }: { office: Escritorio; closureDate: string; listName: string; listValues: string[] },
  expect?: HttpStatus,
) => {
  expect = expect ?? HttpStatus.OK;
  return request(app.getHttpServer())
    .patch(
      `/offices/${office.id_escritorio}/preferences/${closureDate}/${PreferencesType.LIST}/${listName}`,
    )
    .set({ Authorization: 'Bearer 123' })
    .send(listValues);
};

export const getSingleList = async (
  app: any,
  {
    office,
    closureDate,
    listName,
    query,
  }: {
    office: number;
    closureDate: string;
    listName: string;
    query?: { version: string; versionDate: string };
  },
  expect: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${office}/preferences/${closureDate}/lists/${listName}`;

  const response = await request(app.getHttpServer())
    .get(path)
    .query(query)
    .set({ Authorization: 'Bearer 123' });

  return handleResponse({ path, response, expectedHttpStatus: expect });
};

export const putMultipleLists = async (
  app: any,
  { office, closureDate, payload },
  expect: HttpStatus = HttpStatus.OK,
) => {
  return request(app.getHttpServer())
    .patch(`/offices/${office.id_escritorio}/preferences/${closureDate}/${PreferencesType.LIST}`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expect);
};

export const getClosureFiles = async (
  app: any,
  { office, closureDate },
  expect: HttpStatus = HttpStatus.OK,
) => {
  return request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/closures/${closureDate}/sheets`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expect);
};

export const getAumFiles = async (
  app: any,
  { closureDate },
  expect: HttpStatus = HttpStatus.OK,
) => {
  const path = `/assets/${closureDate}/file`;
  try {
    return request(app.getHttpServer())
      .get(path)
      .set({ Authorization: 'Bearer 123' })
      .expect(expect);
  } catch (err) {
    throw new Error(`error on request ${path}.\n\n ${err.message}`);
  }
};

export const processAumFile = async (
  app: any,
  referenceDate: string,
  fileId: number,
  origin: string,
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/assets/${referenceDate}/file/${fileId}`;

  const response = await request(app.getHttpServer())
    .put(path)
    .set({ Authorization: 'Bearer 123' })
    .send({ origin });

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deleteAumFile = async (
  app: any,
  { closureDate, sheetIds },
  expect: HttpStatus = HttpStatus.OK,
) => {
  const path = `/assets/${closureDate}/file/${sheetIds}`;
  try {
    return request(app.getHttpServer())
      .delete(path)
      .set({ Authorization: 'Bearer 123' })
      .expect(expect);
  } catch (err) {
    throw new Error(`error on request ${path}.\n\n ${err.message}`);
  }
};

export const getFile = ({ officeId }) => {
  return File.findAll({ where: { office_id: officeId } });
};

export const getCustomerAssetsByFile = async (
  app: any,
  { sheetIds },
): Promise<Customer_Assets[]> => {
  return Customer_Assets.findAll({
    where: {
      customer_assets_sheet_id: sheetIds,
    },
  });
};

export const getCompanies = async (app: any, officeId, expect: HttpStatus = HttpStatus.OK) => {
  const path = `/offices/${officeId}/companies`;
  try {
    return request(app.getHttpServer())
      .get(path)
      .set({ Authorization: 'Bearer 123' })
      .expect(expect);
  } catch (err) {
    throw new Error(`error on request ${path}.\n\n ${err.message}`);
  }
};

export const integrationPutCommissionEndpoint = async (
  app: any,
  {
    officeClientId,
    body,
    expectedHttpStatus,
  }: { officeClientId?: string; body?: any; expectedHttpStatus: number } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/closures/commissions`;

  let headers: any = { Authorization: 'Bearer 123' };
  headers = !officeClientId ? headers : { ...headers, 'x-office-id': officeClientId };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const integrationWebhookEndpoint = async (
  app: any,
  {
    officeClientId,
    body,
    expectedHttpStatus,
  }: { officeClientId?: string; body?: any; expectedHttpStatus: number } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/commissions/webhook`;

  let headers: any = { Authorization: 'Bearer 123' };
  headers = !officeClientId ? headers : { ...headers, 'x-office-id': officeClientId };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const integrationWebhookClientIdEndpoint = async (
  app: any,
  {
    officeClientId,
    body,
    expectedHttpStatus,
  }: { officeClientId?: string; body?: any; expectedHttpStatus: number } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/commissions/${officeClientId}/webhook`;

  const response = await request(app.getHttpServer()).post(path).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const ssoEndpoint = async (
  app: any,
  { body, additional_data }: { body: any; additional_data: string },
  expect: HttpStatus = HttpStatus.OK,
) => {
  const path = `/login/sso`;

  const response = await request(app.getHttpServer())
    .post(path)
    .query({ additional_data })
    .send(body);

  return handleResponse({ method: 'POST', path, response, expectedHttpStatus: expect });
};

// TODO: Change for endpoint calls when we have'em
export const setupTeamWithCreditors = async ({
  teamName,
  officeId,
  creditors,
}: {
  teamName: string;
  officeId: number;
  creditors: string[];
}) => {
  const team = await Teams.create({ office_id: officeId, name: teamName });

  const putCreditorOnTeamsPromises = creditors.map(async external_creditor_id => {
    const creditor =
      (await Credor.findOne({
        where: { escritorio_id_escritorio: officeId, id_credor_externo: external_creditor_id },
      })) ?? (await createCreditorIfDoesntExist(officeId, external_creditor_id));
    return Creditor_Teams.create({
      creditor_id: creditor.id_credor,
      team_id: team.id,
    });
  });

  await Promise.all(putCreditorOnTeamsPromises);
};

export const createCompany = async ({
  officeId,
  companyName,
  companyIdentifier,
  workspaceId,
  pixKey,
  pixQrCode,
  refundPixKey,
  config = { type: PaymentTypes.STARK, bill_transfer_taxes: false },
}: {
  officeId: number;
  companyName: string;
  companyIdentifier?: string;
  workspaceId?: string;
  pixKey?: string;
  pixQrCode?: string;
  refundPixKey?: string;
  config?: CompanyConfig;
}) => {
  return Company.create({
    office_id: officeId,
    company_identifier: companyIdentifier ?? null,
    name: companyName,
    workspace_id: workspaceId ?? null,
    pix_key: pixKey ?? null,
    pix_qr_code: pixQrCode ?? null,
    refund_pix_key: refundPixKey ?? null,
    config: config ?? null,
  });
};

export const createPaymentGroup = async ({
  officeId,
  companyId,
  creditorId,
  origin,
}: {
  officeId: number;
  companyId: number;
  creditorId?: number;
  origin?: string;
}) => {
  return Payment_Group.create({
    office_id: officeId,
    company_id: companyId,
    creditor_id: creditorId,
    origin,
  });
};

export const createOfficeClaim = async (
  officeId: number,
  claim: { key: string; value: string },
) => {
  return AtributosEscritorio.create({
    escritorio_id_escritorio: officeId,
    atributo: claim.key,
    valor: claim.value,
  });
};

export const getSingleBalanceStatusEndpoint = async (
  app: any,
  {
    officeId,
    closureDate,
    externalCreditorId,
    expectedHttpStatus,
  }: {
    officeId?: number;
    closureDate?: string;
    externalCreditorId?: string;
    expectedHttpStatus: number;
  } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/offices/${officeId}/closures/${closureDate}/creditors/${encodeURIComponent(
    externalCreditorId,
  )}/balance-status`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const putBalanceStatusEventEndpoint = async (
  app: any,
  {
    officeId,
    closureDate,
    externalCreditorId,
    expectedHttpStatus,
    body,
  }: {
    officeId?: number;
    closureDate?: string;
    externalCreditorId?: string;
    body?: any;
    expectedHttpStatus: number;
  } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/offices/${officeId}/closures/${closureDate}/creditors/${encodeURIComponent(
    externalCreditorId,
  )}/balance-status`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getCreditorBalanceEvent = async (
  closureId: number,
  creditorId: number,
): Promise<BalanceEventAttributes[]> => {
  const creditorsEvents = await Balance_Event.findAll({
    where: { closure_id: closureId, creditor_id: creditorId },
    order: [
      ['updated_at', 'DESC'],
      ['id', 'DESC'],
    ],
  });

  return creditorsEvents.map(creditorEvent => ({
    ...(creditorEvent.get() as BalanceEventAttributes),
    balance: Number(creditorEvent.balance),
  }));
};

export const getBalanceHistoryEndpoint = async (
  app: any,
  {
    officeId,
    closureDate,
    externalCreditorId,
    expectedHttpStatus,
  }: {
    officeId?: number;
    closureDate?: string;
    externalCreditorId?: string;
    expectedHttpStatus: number;
  } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/offices/${officeId}/closures/${closureDate}/creditors/${encodeURIComponent(
    externalCreditorId,
  )}/balance-status/history`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getBalanceStatusForAllCreditors = async (
  app: any,
  {
    officeId,
    closureDate,
    expectedHttpStatus,
  }: {
    officeId?: number;
    closureDate?: string;
    expectedHttpStatus: number;
  } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/offices/${officeId}/closures/${closureDate}/balance-status`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const createCreditorPaymentInfo = async ({
  active,
  documentNumber,
  branch,
  account,
  bankCode,
  creditorId,
  name,
  accountType,
  pixKey,
  createFinancialInstitution,
}: {
  active: boolean;
  documentNumber: string;
  branch?: string;
  account?: string;
  bankCode?: string;
  creditorId: number;
  name: string;
  accountType: AccountType;
  pixKey?: string;
  createFinancialInstitution?: boolean;
}): Promise<Creditor_Payment_Info> => {
  try {
    let financialInstitution: Financial_Institution;

    if (bankCode) {
      financialInstitution = await Financial_Institution.findOne({
        where: { bank_code: bankCode },
      });
    }

    if (!financialInstitution && createFinancialInstitution) {
      financialInstitution = await Financial_Institution.create({
        name: 'Banco',
        bank_code: bankCode,
        ispb_code: '123',
      });
    }

    return Creditor_Payment_Info.create({
      active,
      document_number: documentNumber,
      branch,
      account,
      financial_institution_id: financialInstitution?.id,
      creditor_id: creditorId,
      name,
      account_type: accountType,
      pix_key: pixKey,
    });
  } catch (err) {
    console.log('\n\nerr:', err);
    throw err;
  }
};

export const createTransfers = async (
  app: any,
  {
    officeId,
    closureDate,
    body,
    responseType,
    expectedHttpStatus,
  }: {
    officeId?: number;
    closureDate?: string;
    body?: any;
    responseType?: string;
    expectedHttpStatus: number;
  } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/offices/${officeId}/closures/${closureDate}/payment`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer())
    .post(path)
    .responseType(responseType)
    .set(headers)
    .send(body);

  return handleResponse({ method: 'POST', path, response, expectedHttpStatus });
};

export const receiveBankingEvent = async (
  app: any,
  {
    body,
    expectedHttpStatus,
    digitalSignature,
  }: {
    digitalSignature?: string;
    body?: any;
    expectedHttpStatus: number;
  } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = WEBHOOKS_DEFAULT_ROUTE;
  const headers: any = { Authorization: 'Bearer 123' };
  if (digitalSignature) headers['digital-signature'] = digitalSignature;
  headers['content-type'] = 'application/json';

  const response = await request(app.getHttpServer()).post(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getPaymentLogs = async (paymentId: number): Promise<Payment_Log[]> => {
  return Payment_Log.findAll({ where: { payment_id: paymentId } });
};

export const getCompanyBalance = async (
  app: any,
  {
    officeId,
    companyId,
    expectedHttpStatus,
  }: {
    officeId?: number;
    companyId?: number;
    expectedHttpStatus: number;
  } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/offices/${officeId}/companies/${companyId}/balance`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const createPaymentFromStarkLog = async ({
  company,
  closureDate,
  state,
  paymentEvent,
  payingUser,
  paidCreditorId,
  paymentType = PaymentType.CREDITOR_PAYMENT,
}: {
  company: Company;
  closureDate?: string;
  state: PaymentStatus;
  paymentEvent: StarkEvent;
  payingUser: number;
  paidCreditorId: number;
  paymentType?: PaymentType;
}): Promise<Payment> => {
  return createPayment({
    company,
    closureDate,
    state,
    paymentIdentifier: paymentEvent.event.log.transfer.externalId,
    externalPaymentId: state === PaymentStatus.PENDING ? null : paymentEvent.event.log.transfer.id,
    amount: addDecimalSeparator(paymentEvent.event.log.transfer.amount),
    payingUser,
    paidCreditorId,
    paymentType,
  });
};

export const createPayment = async ({
  company,
  closureDate = null,
  state,
  paymentIdentifier,
  externalPaymentId,
  amount,
  payingUser,
  paidCreditorId = null,
  paymentType = PaymentType.CREDITOR_PAYMENT,
}: {
  company: Company;
  closureDate?: string;
  state: PaymentStatus;
  paymentIdentifier: string;
  externalPaymentId: string;
  amount: number;
  payingUser: number;
  paidCreditorId?: number;
  paymentType?: PaymentType;
}): Promise<Payment> => {
  const closure = closureDate
    ? await Fechamento.findOne({
        where: { escritorio_id_escritorio: company.office_id, data_fechamento: closureDate },
      })
    : null;

  const payment: Partial<PaymentAttributes> = {
    amount,
    closure_id: closure?.id_fechamento ?? null,
    paying_company_id: company.id,
    payment_identifier: paymentIdentifier,
    external_payment_id: externalPaymentId,
    type: paymentType,
    user_id: payingUser,
    payment_status: state,
    receiving_creditor_id: paidCreditorId,
  };
  return Payment.create(payment);
};

export const getPayments = async (
  app: any,
  {
    officeId,
    closureDate,
    expectedHttpStatus,
  }: {
    officeId?: number;
    closureDate?: string;
    expectedHttpStatus: number;
  } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/offices/${officeId}/closures/${closureDate}/payment`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const putGoalsEndpoint = async (
  app: any,
  {
    officeId,
    closureDate,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/goals`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send();

  return handleResponse({ path, response, expectedHttpStatus });
};

export const createGoalMetric = async (
  goalMetricData: Partial<GoalMetricAttributes>,
): Promise<Goal_Metric> => {
  return Goal_Metric.create({
    closure_id: goalMetricData.closure_id,
    commission_id: goalMetricData.commission_id,
    creditor_id: goalMetricData.creditor_id,
    metric_name: goalMetricData.metric_name,
    tab_sync_id: goalMetricData.tab_sync_id,
    metric_value: goalMetricData.metric_value,
  });
};

export const getMetrics = async (creditorId: number): Promise<Goal_Metric[]> => {
  return Goal_Metric.findAll({ where: { creditor_id: creditorId } });
};

export const getCreditorGoals = async (
  app: any,
  {
    officeId,
    closureDate,
    externalCreditorId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    externalCreditorId: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/${
    PreferencesType.GOALS
  }/creditors/${encodeURIComponent(externalCreditorId)}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const sheetPreview = async (
  app: any,
  {
    body,
    expectedHttpStatus,
  }: {
    expectedHttpStatus: number;
    body?: CloseMonthRequest;
  } = {
    expectedHttpStatus: 200,
  },
) => {
  const path = `/file/preview`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const insertTrigger = async (
  app: any,
  {
    officeId,
    closureDate,
    body,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    closureDate: string;
    body: TriggerRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/triggers`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const putTeams = async (
  app: any,
  {
    officeId,
    closureDate,
    body,
    expectedHttpStatus = HttpStatus.CREATED,
    updateCurrent,
  }: {
    officeId: number;
    closureDate: string;
    body: TeamsPreferenceResponse[];
    expectedHttpStatus?: number;
    updateCurrent?: boolean;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/teams`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer())
    .put(path)
    .query({ update_current: updateCurrent })
    .set(headers)
    .send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getTeams = async (
  app: any,
  {
    officeId,
    closureDate,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    closureDate: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/teams`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getCreditorTeams = async (
  app: any,
  {
    officeId,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/creditor/teams`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const updateTrigger = async (
  app: any,
  {
    officeId,
    closureDate,
    body,
    triggerId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    body: TriggerRequest;
    triggerId: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/triggers/${triggerId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).patch(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const insertGoal = async (
  app: any,
  {
    officeId,
    closureDate,
    body,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    closureDate: string;
    body: GoalRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/goals`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const updateGoal = async (
  app: any,
  {
    officeId,
    closureDate,
    body,
    goalId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    body: GoalRequest;
    goalId: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/goals/${goalId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).patch(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const updateGoals = async (
  app: any,
  {
    officeId,
    closureDate,
    body,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    body: GoalsPreferencesRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/goals`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).patch(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deleteManagedProfileEndpoint = async (
  app: any,
  permission_profile_id: number,
  expectedHttpStatus = HttpStatus.OK,
) => {
  const path = `/backoffice/profile/${permission_profile_id}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).delete(path).set(headers).send();

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deleteGoal = async (
  app: any,
  {
    officeId,
    closureDate,
    goalId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    goalId: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/goals/${goalId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).delete(path).set(headers).send();

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deleteClosure = async (
  app: any,
  {
    officeId,
    closureDate,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/escritorios/${officeId}/fechamentos/${closureDate}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).delete(path).set(headers).send();

  return handleResponse({ path, response, expectedHttpStatus });
};

export const createAdjustmentRequest = async (
  app: any,
  {
    officeId,
    closureDate,
    adjustmentRequests,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    adjustmentRequests: CreateAdjustmentRequest[];
    closureDate: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/${PreferencesType.ADJUSTMENT_REQUESTS}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer())
    .patch(path)
    .set(headers)
    .send(adjustmentRequests);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getAllAdjustmentRequests = async (
  app: any,
  {
    officeId,
    closureDate,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/${PreferencesType.ADJUSTMENT_REQUESTS}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getAdjustmentRequests = async (
  app: any,
  {
    officeId,
    closureDate,
    externalCreditorId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    externalCreditorId: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/${
    PreferencesType.ADJUSTMENT_REQUESTS
  }/creditors/${encodeURIComponent(externalCreditorId)}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const pullAdjustmentRequests = async (
  app: any,
  {
    officeId,
    closureDate,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    payload: AdjustmentApprovalRequest[];
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/${PreferencesType.ADJUSTMENT_REQUESTS}/from-request`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const processMetricSheet = async (
  app: any,
  {
    officeId,
    closureDate,
    sheetId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    sheetId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/closures/${closureDate}/metrics/from-sheet/${sheetId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const uploadAndProcessMetricSheet = async (
  app: any,
  {
    officeId,
    closureDate,
    fileName,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    fileName: string;
    expectedHttpStatus?: number;
  },
) => {
  const fileResponse = await uploadFile(app, `test_resources/${fileName}`);
  await processMetricSheet(app, {
    officeId,
    closureDate,
    sheetId: Number(fileResponse.body.fileId),
    expectedHttpStatus,
  });
  return fileResponse.body.fileId;
};

export const syncFile = async (
  app: any,
  {
    officeId,
    payload,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    payload: DataSourceRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/data-source`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const saveOfficePreferences = async (
  app: any,
  {
    officeId,
    preferenceType,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    preferenceType: OfficePreferenceType;
    payload: OfficeVariableRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${preferenceType}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).patch(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getOfficePreferences = async (
  app: any,
  {
    officeId,
    preferenceType,
    tabId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    preferenceType: OfficePreferenceType;
    tabId: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${preferenceType}/tab/${tabId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers).send();

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deleteOfficePreferenceTab = async (
  app: any,
  {
    officeId,
    preferenceType,
    tabId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    preferenceType: OfficePreferenceType;
    tabId: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${preferenceType}/tab/${tabId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).delete(path).set(headers).send();

  return handleResponse({ path, method: 'DELETE', response, expectedHttpStatus });
};

export const getOfficePreferenceTabs = async (
  app: any,
  {
    officeId,
    preferenceType,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    preferenceType: OfficePreferenceType;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${preferenceType}/tab`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers).send();

  return handleResponse({ path, response, expectedHttpStatus });
};

export const copyVariables = async (
  app: any,
  {
    officeId,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/variables`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send({});

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getTriggers = async (
  app: any,
  {
    officeId,
    closureDate,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/triggers`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getGoals = async (
  app: any,
  {
    officeId,
    closureDate,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/goals`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const putAdjustments = async (
  app: any,
  office: Escritorio,
  date: string,
  payload: InsertCommissionRequest[],
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${office.id_escritorio}/preferences/${date}/${PreferencesType.ADJUSTMENTS}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(payload);

  return handleResponse({ method: 'PUT', path, response, expectedHttpStatus });
};

export const getCreditorNetAmount = async (
  app: any,
  {
    office,
    closureDate,
    externalCreditorId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    externalCreditorId: string;
    closureDate: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/closures/${closureDate}/${encodeURIComponent(
    externalCreditorId,
  )}/earned-amount`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getClosures = async (
  app: any,
  {
    office,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/closures`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getWorksheets = async (
  app: any,
  {
    office,
    url,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    url: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/data-source/worksheets?url=${url}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const sheetDbRedirect = async (
  app: any,
  {
    office,
    uri,
    method,
    body,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    uri: string;
    method: string;
    body?: any;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/sheet-db/${uri}`;
  const headers: any = { Authorization: 'Bearer 123' };

  let response = null;

  if (method === 'get') {
    response = await request(app.getHttpServer()).get(path).set(headers);
    return handleResponse({ path, response, expectedHttpStatus });
  }

  response = await request(app.getHttpServer())
    [method](path)
    .set(headers)
    .send(body ?? {});

  return handleResponse({ path, response, expectedHttpStatus });
};

export const createSyncConfig = async (
  app: any,
  {
    office,
    syncConfig,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    syncConfig: object;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/tab-sync/config`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(syncConfig);

  return handleResponse({ path, method: 'PUT', response, expectedHttpStatus });
};

export const getAllSyncConfigs = async (
  app: any,
  {
    office,
    tabId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    tabId?: string;
    expectedHttpStatus?: number;
  },
) => {
  let path = `/offices/${office.id_escritorio}/tab-sync/config`;
  if (tabId) path = `${path}?tab_id=${tabId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getSingleSyncConfig = async (
  app: any,
  {
    office,
    syncConfigId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    syncConfigId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/tab-sync/config/${syncConfigId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, method: 'GET', response, expectedHttpStatus });
};

export const changeTabSyncConfig = async (
  app: any,
  {
    office,
    syncConfig,
    syncConfigId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    syncConfigId: string;
    syncConfig: object;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/tab-sync/config/${syncConfigId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).patch(path).set(headers).send(syncConfig);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deleteSyncConfig = async (
  app: any,
  {
    office,
    syncConfigId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    syncConfigId: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/tab-sync/config/${syncConfigId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).delete(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const tabEvents = async (
  app: any,
  {
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    payload: any;
    expectStatus?: number;
  },
) => {
  const path = `/tab/events`;
  const response = await request(app.getHttpServer())
    .post(path)
    .set({ Authorization: 'Bearer 123', from: PUBSUB_FROM_HEADER })
    .send(payload)
    .expect(expectStatus);
  return handleResponse({ path, method: 'POST', response, expectedHttpStatus: expectStatus });
};

export const payoutPeriodEvents = async (
  app: any,
  {
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    payload: any;
    expectStatus?: number;
  },
) => {
  const path = `/payout_period/events`;
  const response = await request(app.getHttpServer())
    .post(path)
    .set({ Authorization: 'Bearer 123', from: PUBSUB_FROM_HEADER })
    .send(payload)
    .expect(expectStatus);
  return handleResponse({ path, method: 'POST', response, expectedHttpStatus: expectStatus });
};
const toBase64 = (data: any) => {
  return Buffer.from(JSON.stringify(data)).toString('base64');
};

export const mountRequest = async (
  app: any,
  path: string,
  data: any,
  expectStatus = HttpStatus.OK,
) => {
  return events(app, {
    path,
    payload: {
      message: {
        data: toBase64(data),
        attributes: {
          correlationId: 'req-2',
        },
      },
    },
    expectStatus,
  });
};

export const events = async (
  app: any,
  {
    path,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    path: string;
    payload: any;
    expectStatus?: number;
  },
) => {
  const response = await request(app.getHttpServer())
    .post(path)
    .set({ Authorization: 'Bearer 123', from: PUBSUB_FROM_HEADER })
    .send(payload)
    .expect(expectStatus);
  return handleResponse({ path, method: 'POST', response, expectedHttpStatus: expectStatus });
};

export const closureUpdateEvent = async (
  app,
  officeId: number,
  closureDate: string,
  type?: ClosureEventTypes,
  expectStatus?: HttpStatus,
) => {
  const payload: ClosureMustUpdateEvent = {
    office_id: String(officeId),
    closure_date: closureDate,
  };

  if (type) payload.type = type;
  if (isClosureCommissionBackupEvent(payload)) payload.job_id = uuid();

  return mountRequest(app, '/events/closure', payload, expectStatus); // Req
};

export const createDocument = async (
  app: any,
  {
    officeId,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    officeId: any;
    payload: DocumentRequest[];
    expectStatus?: number;
  },
): Promise<SupertestResponse<CreateDocumentResponse[]>> => {
  const path = `/offices/${officeId}/document`;
  const response = await request(app.getHttpServer())
    .post(path)
    .set({ Authorization: 'Bearer 123' })
    .send(payload);

  return handleResponse({ path, method: 'POST', response, expectedHttpStatus: expectStatus });
};

export interface GetDocumentsCommonRequestProps extends GetDocumentsSchema {
  officeId: any;
  creditors?: string[];
  filter_date_by?: GetDocumentsFilterBy;
  date?: string;
  start_date?: string;
  end_date?: string;
  download?: boolean;
  expectStatus?: number;
  payment_status?: DocumentPaymentStatus;
  approval?: DocumentApproval;
}

export const getDocuments = async (
  app: any,
  {
    officeId,
    creditors,
    download,
    expectStatus = HttpStatus.OK,

    ...queryParams
  }: GetDocumentsCommonRequestProps,
): Promise<SupertestResponse<GetPaginatedDocumentResponse>> => {
  const path = `/offices/${officeId}/document`;
  const creditor_id = creditors ?? undefined;

  const req = request(app.getHttpServer())
    .get(path)
    .query({
      creditor_id,
      download,
      ...queryParams,
    })
    .set({ Authorization: 'Bearer 123' });

  if (download) req.responseType('buffer');

  return handleResponse({
    path,
    response: await req,
    method: 'GET',
    expectedHttpStatus: expectStatus,
  });
};

export const getSingleDocument = async (
  app: any,
  {
    officeId,
    documentId,
    download = false,
    expectStatus = HttpStatus.OK,
  }: {
    officeId: any;
    documentId: number;
    download?: boolean;
    expectStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/document/${documentId}`;

  const req = request(app.getHttpServer())
    .get(path)
    .query({ download })
    .set({ Authorization: 'Bearer 123' });

  if (download) req.responseType('buffer');

  return handleResponse({
    path,
    response: await req,
    method: 'GET',
    expectedHttpStatus: expectStatus,
  });
};

export const getDocumentsCount = async (
  app: any,
  {
    officeId,
    creditors,
    date,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: any;
    creditors?: string[];
    date?: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/documents/count`;
  const creditor_id = creditors ?? undefined;

  const response = await request(app.getHttpServer())
    .get(path)
    .query({ creditor_id, date })
    .set({ Authorization: 'Bearer 123' })
    .expect(expectedHttpStatus);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deleteDocuments = async (
  app: any,
  {
    officeId,
    document_ids,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: any;
    document_ids?: string[];
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/document`;
  const response = await request(app.getHttpServer())
    .delete(path)
    .query({ document_ids })
    .set({ Authorization: 'Bearer 123' });

  return handleResponse({ path, response, expectedHttpStatus });
};

export const evaluateApproval = async (
  app: any,
  {
    officeId,
    document_id,
    body,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: any;
    document_id: number;
    body: EvaluateDocumentApprovalRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/document/approval/${document_id}`;
  const response = await request(app.getHttpServer())
    .patch(path)
    .set({ Authorization: 'Bearer 123' })
    .send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const updateDocument = async (
  app: any,
  {
    officeId,
    document_id,
    body,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: any;
    document_id: number;
    body: UpdateDocumentRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/document/${document_id}`;
  const response = await request(app.getHttpServer())
    .patch(path)
    .set({ Authorization: 'Bearer 123' })
    .send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getClosureVariables = async (
  app: any,
  {
    officeId,
    closureDate,
    expectStatus = HttpStatus.OK,
    type,
    teams,
    all_variables,
  }: {
    officeId: number;
    closureDate: string;
    type?: ClosureVariableType;
    teams?: string[];
    all_variables?: boolean;
    expectStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/${PreferencesType.VARIABLES}`;
  const req = request(app.getHttpServer())
    .get(path)
    .query({ type, teams, all_variables })
    .set({ Authorization: 'Bearer 123' });
  return handleResponse({
    path,
    response: await req,
    method: 'GET',
    expectedHttpStatus: expectStatus,
  });
};

export const putClosureVariables = async (
  app: any,
  {
    officeId,
    closureDate,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    payload: VariableRequest[];
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/${PreferencesType.VARIABLES}`;
  const res = await request(app.getHttpServer())
    .put(path)
    .set({ Authorization: 'Bearer 123' })
    .send(payload);

  return handleResponse({ path, response: res, method: 'PUT', expectedHttpStatus });
};

export const sheetDbSync = async (
  app: any,
  {
    office,

    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    office: Escritorio;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/sheet-db/sync`;
  const headers: any = { Authorization: 'Bearer 123' };

  let response = null;

  response = await request(app.getHttpServer()).post(path).set(headers).send({});

  return handleResponse({ path, response, expectedHttpStatus });
};

export const updateClaims = async (
  app: any,
  {
    officeId,
    externalCreditorId,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    externalCreditorId: string;
    payload: PermissionData[];
    expectedHttpStatus?: number;
  },
) => {
  const path = `/office/${officeId}/users/${externalCreditorId}/claims`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getBackupVersions = async (
  app: any,
  {
    officeId,
    closureDate,
    type,
    expectedHttpStatus = HttpStatus.OK,
    agent,
  }: {
    officeId: number;
    closureDate: string;
    type: PreferencesType;
    expectedHttpStatus?: number;
    agent?: SupertestAgent;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/${type}/versions`;
  const headers: any = { Authorization: 'Bearer 123' };

  const ag = agent ?? request(app.getHttpServer());
  const response = await ag.get(path).set(headers).timeout(10000).expect(expectedHttpStatus);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getBackupVersion = async (
  app: any,
  {
    officeId,
    closureDate,
    versionId,
    type,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    closureDate: string;
    versionId: string;
    type: PreferencesType;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/${type}/version/${versionId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getClosureDatesHasBackup = async (
  app: any,
  {
    officeId,
    type,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    type: PreferencesType;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${type}/dates`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const backofficeCopyClosures = async (
  app: any,
  {
    officeId,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    payload: {
      dates_to_copy: string[];
      office_to_copy: string;
    };
    expectedHttpStatus?: number;
  },
) => {
  const path = `/backoffice/copy-closures?office_id=${officeId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const consultOidcProviders = async (
  app: INestApplication,
  {
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    payload: ConsultOidcProvidersRequestDTO;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/login/oidc/providers`;

  const response = await request(app.getHttpServer()).get(path).query(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const authByOidc = async (
  app: INestApplication,
  {
    body,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    body: LoginByOidcRequestDTO;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/login/auth`;

  const response = await request(app.getHttpServer())
    .post(path)
    .set({ Accept: 'application/json' })
    .send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const implicitAuthByOidc = async (
  app: INestApplication,
  {
    body,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    body: ImplicitLoginRequestDTO;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/login/auth/implicit`;

  const response = await request(app.getHttpServer())
    .post(path)
    .set({ Accept: 'application/json' })
    .send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const refreshToken = async (
  app: INestApplication,
  {
    refreshToken,
    authorization = '',
    expectedHttpStatus = HttpStatus.OK,
  }: {
    refreshToken?: string;
    authorization?: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/login/oidc/refresh-token`;

  const response = await request(app.getHttpServer())
    .post(path)
    .set('Cookie', [!!refreshToken && `${REFRESH_TOKEN_COOKIE_KEY}=${refreshToken}`])
    .set({ Authorization: authorization });

  return handleResponse({ path, response, expectedHttpStatus });
};

export const customDashboard = async (
  app: any,
  {
    index,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    index: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/dashboards/custom/${index}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const dashboardsRequest = async (
  app: any,
  {
    type,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    type: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/dashboards/${type}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export async function getWorkflowStatusInfo(
  app: INestApplication,
  {
    officeId,
    expectedStatus = HttpStatus.OK,
    filters,
  }: {
    officeId: number;
    expectedStatus: number;
    filters?: ObjectWorkflowStatusFilters;
  },
) {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/object/workflow/status/info`)
    .set({ Authorization: 'Bearer 123' })
    .query(filters)
    .expect(expectedStatus);
}

export const createPermissionProfile = async <T = any>(
  app: any,
  {
    officeId,
    payload,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    payload: PermissionProfileRequest & { inherit_profile_id?: number | null };
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/permission-profile`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(payload);

  return handleResponse<T>({ path, response, expectedHttpStatus });
};

export const getPermissionProfiles = async <T = any>(
  app: any,
  {
    officeId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/permission-profile`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse<T>({ path, response, expectedHttpStatus });
};

export const getPermissionProfile = async <T = any>(
  app: any,
  {
    officeId,
    permissionProfileId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    permissionProfileId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/permission-profile/${permissionProfileId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse<T>({ path, response, expectedHttpStatus });
};

export const putPermissionProfile = async (
  app: any,
  {
    officeId,
    permissionProfileId,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    permissionProfileId: number;
    payload: PermissionProfileRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/permission-profile/${permissionProfileId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const changeProfilePriorities = async (
  app: any,
  {
    officeId,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    payload: PermissionProfilesPriorityRequest[];
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/permission-profile/priority`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).patch(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deletePermissionProfile = async (
  app: any,
  {
    officeId,
    permissionProfileId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    permissionProfileId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/permission-profile/${permissionProfileId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).delete(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const copyPermissionProfile = async (
  app: any,
  {
    officeId,
    permissionProfileId,
    payload,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    permissionProfileId: number;
    payload: CopyPermissionProfileRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/permission-profile/${permissionProfileId}/copy`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const updatePermissions = async (
  app: any,
  {
    officeId,
    permissionProfileId,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    permissionProfileId: number;
    payload: PermissionsRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/permission-profile/${permissionProfileId}/permissions`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getAllPermissions = async (
  app: any,
  {
    officeId,
    permissionProfileId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    permissionProfileId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/permission-profile/${permissionProfileId}/permissions`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const backofficeChangeCreditorOffice = async (
  app: any,
  {
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    payload: { new_office: number };
    expectedHttpStatus?: number;
  },
) => {
  const path = `/user/change-office`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).patch(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const putOfficeClaims = async (
  app: any,
  {
    pathParams,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    pathParams: { officeId: number };
    payload: PermissionData[];
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${pathParams.officeId}/permissions`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getOfficeClaims = async (
  app: any,
  {
    pathParams,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    pathParams: { officeId: number };
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${pathParams.officeId}/permissions`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse<PermissionData[]>({ path, response, expectedHttpStatus });
};

export const updateOidcProviders = async (app: any, expectedHttpStatus = HttpStatus.OK) => {
  const path = `/backoffice/oidc`;
  let headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const createCompanyRequest = async (
  app: any,
  {
    officeId,
    payload,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    payload: any;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/company`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const updateCompany = async (
  app: any,
  {
    officeId,
    companyId,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    companyId: number;
    payload: any;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/company/${companyId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deleteCompanyRequest = async (
  app: any,
  {
    officeId,
    companyId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    companyId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/company/${companyId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).delete(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const upsertCustomers = async (
  app: INestApplication,
  {
    officeId,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    payload: CustomerData[];
    expectedHttpStatus?: HttpStatus;
  },
) => {
  const path = `/offices/${officeId}/customers`;
  let headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).send(payload).set(headers);

  return handleResponse({ path, response, expectedHttpStatus, method: 'POST' });
};

export const getOnboarding = async (
  app: any,
  invitationToken: string,
  expectStatus: HttpStatus,
): Promise<any> => {
  return request(app.getHttpServer())
    .get(`/onboarding/token/${invitationToken}/me`)
    .expect(expectStatus);
};

export const postOnboarding = async (
  app: any,
  office: Escritorio,
  invitationToken: string,
  payload: ChangePasswordRequest,
  expectStatus: HttpStatus,
): Promise<any> => {
  return request(app.getHttpServer())
    .post(`/onboarding/office/${office.id_escritorio}/token/${invitationToken}`)
    .send(payload)
    .expect(expectStatus);
};

export const getBanks = async (
  app: INestApplication,
  {
    expectedHttpStatus = HttpStatus.OK,
  }: {
    expectedHttpStatus?: HttpStatus;
  },
) => {
  const path = `/banks`;
  let headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus, method: 'GET' });
};

export const getCreditorPaymentInfo = async (
  app: any,
  office: Escritorio,
  expectStatus: HttpStatus,
  active?: boolean,
) => {
  const query = active ? `?active=true` : '';
  return (
    await request(app.getHttpServer())
      .get(`/offices/${office.id_escritorio}/creditor-payment-info${query}`)
      .set({ Authorization: 'Bearer 123' })
      .expect(expectStatus)
  ).body as GetPaymentInfoResponse[];
};

export const createPaymentGroupRequest = async (
  app: any,
  {
    officeId,
    payload,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    payload: PaymentGroupRequest;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/payment/group`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const bulkCreatePaymentGroupRequest = async (
  app: any,
  {
    officeId,
    payload,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    payload: PaymentGroupRequest[];
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/payment/group/bulk`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(payload);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const deletePaymentGroupRequest = async (
  app: any,
  {
    officeId,
    paymentGroupId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    paymentGroupId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/payment/group/${paymentGroupId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).delete(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getAllPaymentGroupRequest = async (
  app: any,
  {
    officeId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/payment/group`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getOnePaymentGroupRequest = async (
  app: any,
  {
    officeId,
    paymentGroupId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    paymentGroupId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/payment/group/${paymentGroupId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const postCreditorPaymentInfo = async ({
  app,
  office,
  payload,
  createFinancialInstitution,
  expectStatus,
}: {
  app: any;
  office: Escritorio;
  payload: CreateCreditorPaymentInfoDto;
  createFinancialInstitution?: boolean;
  expectStatus: HttpStatus;
}) => {
  const headers: any = { Authorization: 'Bearer 123' };
  if (payload.bank_code && createFinancialInstitution) {
    const financialInstitution = await Financial_Institution.findOne({
      where: { bank_code: payload.bank_code },
    });
    if (!financialInstitution) {
      await Financial_Institution.create({
        name: 'Banco',
        bank_code: payload.bank_code,
        ispb_code: '123',
      });
    }
  }

  return request(app.getHttpServer())
    .post(`/offices/${office.id_escritorio}/creditor-payment-info`)
    .set(headers)
    .send(payload)
    .expect(expectStatus);
};

export const bulkPostCreditorPaymentInfo = async ({
  app,
  office,
  payload,
  createFinancialInstitution,
  expectStatus,
}: {
  app: any;
  office: Escritorio;
  payload: CreateCreditorPaymentInfoDto[];
  createFinancialInstitution?: boolean;
  expectStatus: HttpStatus;
}) => {
  if (createFinancialInstitution) {
    await Promise.all(
      payload.map(async item => {
        if (item.bank_code) {
          const financialInstitution = await Financial_Institution.findOne({
            where: { bank_code: item.bank_code },
          });
          if (!financialInstitution) {
            await Financial_Institution.create({
              name: 'Banco',
              bank_code: item.bank_code,
              ispb_code: '123',
            });
          }
        }
      }),
    );
  }

  return request(app.getHttpServer())
    .post(`/offices/${office.id_escritorio}/creditor-payment-info/bulk`)
    .send(payload)
    .expect(expectStatus);
};

export const postActivateCreditorPaymentInfo = async ({
  app,
  office,
  id,
  expectStatus,
}: {
  app: any;
  office: Escritorio;
  id: number;
  expectStatus: HttpStatus;
}) => {
  return request(app.getHttpServer())
    .post(`/offices/${office.id_escritorio}/creditor-payment-info/activate/${id}`)
    .expect(expectStatus);
};

export const patchCreditorPaymentInfo = async ({
  app,
  office,
  id,
  payload,
  expectStatus,
}: {
  app: any;
  office: Escritorio;
  id: number;
  payload: UpdateCreditorPaymentInfoDto;
  expectStatus: HttpStatus;
}) => {
  return request(app.getHttpServer())
    .patch(`/offices/${office.id_escritorio}/creditor-payment-info/${id}`)
    .send(payload)
    .expect(expectStatus);
};

export const bulkPatchCreditorPaymentInfo = async ({
  app,
  office,
  payload,
  expectStatus,
}: {
  app: any;
  office: Escritorio;
  payload: BulkUpdateCreditorPaymentInfoDto;
  expectStatus: HttpStatus;
}) => {
  return request(app.getHttpServer())
    .patch(`/offices/${office.id_escritorio}/creditor-payment-info/bulk`)
    .send(payload)
    .expect(expectStatus);
};

export const deleteCreditorPaymentInfo = async ({
  app,
  office,
  id,
  expectStatus,
}: {
  app: any;
  office: Escritorio;
  id: number;
  expectStatus: HttpStatus;
}) => {
  return request(app.getHttpServer())
    .delete(`/offices/${office.id_escritorio}/creditor-payment-info/${id}`)
    .expect(expectStatus);
};

export const restoreRulesBackup = async (
  app: any,
  {
    officeId,
    versionId,
    closureDate,
    versionClosureDate,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    versionId: string;
    closureDate: string;
    versionClosureDate: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/rules/version`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer())
    .post(path)
    .send({ version_closure_date: versionClosureDate, version_id: versionId })
    .set(headers);

  return handleResponse({ path, response, expectedHttpStatus });
};

/**
 * @deprecated this route is deprecated and should not be used.
 */
export const getOAuthStart = async (
  app: any,
  office: Escritorio,
  expectStatus: HttpStatus,
): Promise<OAuthGetResponse> => {
  return (await request(app.getHttpServer()).get('/oauth').expect(expectStatus))
    .body as OAuthGetResponse;
};

/**
 * @deprecated this route is deprecated and should not be used.
 */
export const postOAuthStart = async <TResponse extends OAuthStartResponse>(
  app: any,
  office: Escritorio,
  payload: OAuthStartDto,
  expectStatus: HttpStatus,
): Promise<TResponse> => {
  return (
    await request(app.getHttpServer()).post(`/oauth/start`).send(payload).expect(expectStatus)
  ).body as TResponse;
};
/**
 * @deprecated this route is deprecated and should not be used.
 */
export const postOAuthFinish = async (
  app: any,
  office: Escritorio,
  payload: OAuthFinishDto,
  expectStatus: HttpStatus,
) => {
  return request(app.getHttpServer()).post(`/oauth/finish`).send(payload).expect(expectStatus);
};

export const createClosure = async (officeId: number, closureDate: string): Promise<Fechamento> => {
  return Fechamento.create({
    data_fechamento: tryParse(closureDate),
    escritorio_id_escritorio: officeId,
    liberado: false,
    locked: false,
  });
};

export const removePlanLock = async (
  app: any,
  officeId: number,
  planId: number,
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/sheet-db/lock`;
  return request(app.getHttpServer())
    .delete(path)
    .query({ commission_plan_id: planId })
    .expect(expectStatus);
};

export const getMissingCreditorsBeforeApplyRules = async (
  app: any,
  { office, data, rules, applyFuture = false }: ApplyRulesType,
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${office.id_escritorio}/preferences/${data}/rules/missing-creditors`;
  return request(app.getHttpServer()).post(path).send(rules).expect(expectStatus);
};

export const getMissingCreditorsBeforeApplyAdjustments = async (
  app: any,
  {
    officeId,
    closureDate,
    payload,
  }: { officeId: number; closureDate: string; payload: InsertCommissionRequest[] },
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/preferences/${closureDate}/adjustments/missing-creditors`;
  return request(app.getHttpServer()).post(path).send(payload).expect(expectStatus);
};

type GetCurrentPayrollProps = {
  officeId: number;
  payrollId: number;
  filters?: GetCurrentPayrollFilters;
};

export const getCurrentPayroll = async <T>(
  app: any,
  { officeId, payrollId, filters }: GetCurrentPayrollProps,
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/v2/${payrollId}`;

  const response = await request(app.getHttpServer()).get(path).query(filters);

  return handleResponse<T>({ path, response, expectedHttpStatus: expectStatus, method: 'GET' });
};

export const exportCurrentPayroll = async <T>(
  app: any,
  { officeId, payrollId }: GetCurrentPayrollProps,
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/v2/${payrollId}/export`;

  const response = await request(app.getHttpServer()).get(path).buffer().parse(binaryParser);

  return response;
};

export const getPayrollGroupings = async (
  app: any,
  { officeId, payrollId, filters }: GetCurrentPayrollProps,
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/v2/${payrollId}/groupings`;

  const response = await request(app.getHttpServer()).get(path).query(filters);

  return handleResponse<GetPayrollGroupingsResponse>({
    path,
    response,
    expectedHttpStatus: expectStatus,
    method: 'GET',
  });
};

export const processPayroll = async <T>(
  app: any,
  {
    officeId,
    payout_date,
    payload,
    payroll_group,
  }: { officeId: number; payout_date: string; payload: ProcessPayroll; payroll_group?: string },
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/${payout_date}/process?${payroll_group ? `payroll_group=${payroll_group}` : ''}`;

  const response = await request(app.getHttpServer()).patch(path).send(payload);

  return handleResponse<T>({ path, response, expectedHttpStatus: expectStatus, method: 'PATCH' });
};

export const getPayrollReport = async <T>(
  app: any,
  { officeId, queryFilter }: { officeId: number; queryFilter: string },
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/report?${queryFilter}`;
  const response = await request(app.getHttpServer()).get(path);
  return handleResponse<T>({ path, response, expectedHttpStatus: expectStatus, method: 'GET' });
};

export const getAvailablePayments = async <T>(
  app: any,
  { officeId, creditorId }: { officeId: number; creditorId?: string[] },
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/request/available`;
  const headers = {
    Authorization: 'Bearer 123',
  };

  const response = await request(app.getHttpServer())
    .get(path)
    .set(headers)
    .query({ creditors: creditorId });

  return handleResponse<T>({ path, response, expectedHttpStatus: expectStatus, method: 'GET' });
};

export const getAvailablePaymentsCreditors = async <T>(
  app: any,
  { officeId }: { officeId: number },
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/request/available/creditors`;
  const headers = {
    Authorization: 'Bearer 123',
  };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse<T>({ path, response, expectedHttpStatus: expectStatus, method: 'GET' });
};

export const getPayrollReportCreditors = async <T>(
  app: any,
  { officeId, queryFilter }: { officeId: number; queryFilter: string },
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/report/creditors?${queryFilter}`;
  const response = await request(app.getHttpServer()).get(path);
  return handleResponse<T>({ path, response, expectedHttpStatus: expectStatus, method: 'GET' });
};

export const updateWorkflowStatus = async (
  app: any,
  {
    officeId,
    payload,
    query,
  }: {
    officeId: number;
    payload: WorkflowStatusUpdateUnion;
    query?: ObjectWorkflowStatusFilters;
  },
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/object/workflow/status`;
  const queryWithDefaultValues = {
    ...query,
    type: query?.type ?? ObjectWorkflowType.PAYROLL_DATA,
  };

  const response = await request(app.getHttpServer())
    .patch(path)
    .query(queryWithDefaultValues)
    .send(payload);

  return handleResponse<ObjectWorkflowUpdateStatusResponse>({
    path,
    response,
    expectedHttpStatus: expectStatus,
    method: 'PATCH',
  });
};

export const getPayrollOnTheFly = async <T>(
  app: any,
  { officeId, payout_date }: { officeId: number; payout_date: string },
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/${payout_date}`;

  const response = await request(app.getHttpServer()).get(path);

  return handleResponse<T>({ path, response, expectedHttpStatus: expectStatus, method: 'GET' });
};

export const getTabSyncsCountPerTabs = async (
  app: any,
  {
    office,
    tab_ids,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    tab_ids: number[];
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/tab-sync/amount`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).query({ tab_ids }).set(headers);

  return handleResponse({ path, method: 'GET', response, expectedHttpStatus });
};

export const getPayrollConfig = async (
  app: any,
  {
    office,
    payroll_id,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    payroll_id: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/payroll/${payroll_id}/config`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, method: 'GET', response, expectedHttpStatus });
};

export const getPayrollConfigById = async (
  app: any,
  {
    office,
    payroll_config_id,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    payroll_config_id: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/payroll/config/office_configs/${payroll_config_id}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse<PayrollSettingsResponse>({
    path,
    method: 'GET',
    response,
    expectedHttpStatus,
  });
};

export const postPayrollConfig = async (
  app: any,
  {
    office,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    payload: PayrollPostConfig;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/payroll/config`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).post(path).set(headers).send(payload);

  return handleResponse<SimplePayrollConfigResponse>({ path, response, expectedHttpStatus });
};

export const getInquiryNotifications = async <T>(
  app: any,
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = '/inquiry/notification';
  const response = await request(app.getHttpServer()).get(path);
  return handleResponse<T>({ path, response, expectedHttpStatus: expectStatus, method: 'GET' });
};

export const updateInquiryStatus = async (
  app: any,
  {
    office,
    inquiryId,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    inquiryId: number;
    payload: UpdateInquiryStatusRequest;
    expectStatus?: HttpStatus;
  },
) => {
  const path = `/offices/${office.id_escritorio}/inquiry/${inquiryId}/status`;
  const response = await request(app.getHttpServer())
    .patch(path)
    .send(payload)
    .expect(expectStatus);
  return response.body as {};
};

export const getInquiryCreditors = async (
  app: any,
  {
    office,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    payload: UpdateInquiryStatusRequest;
    expectStatus?: HttpStatus;
  },
): Promise<GetInquiryCreditors> => {
  const path = `/offices/${office.id_escritorio}/inquiry/creditors`;
  const response = await request(app.getHttpServer()).get(path).query(payload).expect(expectStatus);

  return response.body;
};

export const getInquiries = async (
  app: any,
  {
    office,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    payload: GetInquiriesFiltersRequest;
    expectStatus?: HttpStatus;
  },
): Promise<GetInquiryCreditors> => {
  const path = `/offices/${office.id_escritorio}/inquiry/all`;
  const response = await request(app.getHttpServer()).get(path).query(payload).expect(expectStatus);

  return response.body;
};

export const payrollPaymentCheckout = async <T>(
  app: any,
  { officeId, payload }: { officeId: number; payload: PaymentCheckoutRequest },
  expectStatus: HttpStatus = HttpStatus.CREATED,
  expectedContentType?: string,
  responseType?: 'blob',
) => {
  const path = `/offices/${officeId}/payroll/checkout`;
  const response = await request(app.getHttpServer())
    .post(path)
    .send(payload)
    .responseType(responseType)
    .expect('Content-Type', expectedContentType ?? 'application/json; charset=utf-8');

  return handleResponse<T>({ path, response, expectedHttpStatus: expectStatus, method: 'POST' });
};

export const createPaymentOptionEndpoint = async (
  app: INestApplication,
  office: Escritorio,
  payload: PayerOptionRequest,
  expectStatus: HttpStatus = HttpStatus.CREATED,
): Promise<any> => {
  return request(app.getHttpServer())
    .post(`/offices/${office.id_escritorio}/payment-option`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const getPaymentOptionsEndpoint = async (
  app: any,
  {
    office,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/payment-options`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, method: 'GET', response, expectedHttpStatus });
};

export const getPaymentOptionEndpoint = async (
  app: any,
  {
    office,
    paymentOptionId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    paymentOptionId: number;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/offices/${office.id_escritorio}/payment-option/${paymentOptionId}`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).get(path).set(headers);

  return handleResponse({ path, method: 'GET', response, expectedHttpStatus });
};

export const deletePaymentOptionRequest = async (
  app: INestApplication,
  {
    office,
    paymentOptionId,
  }: {
    office: Escritorio;
    paymentOptionId: number;
  },
) => {
  return request(app.getHttpServer())
    .delete(`/offices/${office.id_escritorio}/payment-option/${paymentOptionId}`)
    .set({ Authorization: 'Bearer' });
};

export const updatePaymentOptionRequest = async (
  app: INestApplication,
  {
    office,
    paymentOptionId,
    payload,
  }: {
    office: Escritorio;
    paymentOptionId: number;
    payload: PayerOptionRequest;
  },
) => {
  return request(app.getHttpServer())
    .put(`/offices/${office.id_escritorio}/payment-option/${paymentOptionId}`)
    .set({ Authorization: 'Bearer' })
    .send(payload);
};

export const getPayrollGroupBySlug = async (
  app: INestApplication,
  {
    officeId,
    slug,
    expectStatus = HttpStatus.OK,
  }: {
    officeId: number;
    slug: string;
    expectStatus?: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/payroll/groups/${slug}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
};

export const patchPayrollGroup = async (
  app: INestApplication,
  {
    officeId,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    officeId: number;
    payload: PatchPayrollGroupBody;
    expectStatus?: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .patch(`/offices/${officeId}/payroll/group/${payload.slug}`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const postPayrollGroup = async (
  app: INestApplication,
  {
    officeId,
    payload,
    expectStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    payload: PostPayrollGroupBody;
    expectStatus?: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .post(`/offices/${officeId}/payroll/group`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const getPayrollGroups = async (
  app: INestApplication,
  {
    officeId,
    expectStatus = HttpStatus.OK,
  }: {
    officeId: number;
    expectStatus?: HttpStatus;
  },
) => {
  const path = `/offices/${officeId}/payroll/groups`;
  const response = await request(app.getHttpServer())
    .get(path)
    .set({ Authorization: 'Bearer 123' });

  return handleResponse<GetPayrollGroupResponse>({
    path,
    method: 'GET',
    response,
    expectedHttpStatus: expectStatus,
  });
};

export const patchPayrollRequest = async (
  app: INestApplication,
  {
    office,
    payload,
    payroll_id,
    expectStatus,
  }: {
    office: Escritorio;
    payroll_id: number;
    payload: { visible: boolean };
    expectStatus: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .patch(`/offices/${office.id_escritorio}/payroll/${payroll_id}`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const getSamlSPMetadata = async (
  app: INestApplication,
  expected: HttpStatus = HttpStatus.OK,
) => {
  const path = '/login/saml/metadata';
  const response = await request(app.getHttpServer())
    .get(path)
    .set({ Authorization: 'Bearer 123' });

  return handleResponse({
    path,
    method: 'GET',
    response,
    expectedHttpStatus: expected,
  });
};

export const getSamlLoginConfig = async (
  app: INestApplication,
  oidcId: number,
  expected: HttpStatus = HttpStatus.OK,
) => {
  const path = `/login/saml/request/${oidcId}`;
  const response = await request(app.getHttpServer())
    .get(path)
    .set({ Authorization: 'Bearer 123' });

  return handleResponse({ path, method: 'GET', response, expectedHttpStatus: expected });
};

export const postSamlCallback = async (
  app: INestApplication,
  body: {
    SAMLResponse: string;
    RelayState: string;
  },
  expected: HttpStatus = HttpStatus.OK,
) => {
  const path = `/login/saml/callback`;
  const response = await request(app.getHttpServer()).post(path).send(body);

  return handleResponse({ path, method: 'POST', response, expectedHttpStatus: expected });
};

export const putPlanGroupPermissionsEndpoint = async (
  app: any,
  {
    expectStatus = HttpStatus.OK,
    office,
    payload,
    groupId,
  }: {
    office: Escritorio;
    expectStatus?: HttpStatus;
    payload: PlanGroupPermissionRequest;
    groupId: number;
  },
) => {
  return request(app.getHttpServer())
    .put(`/offices/${office.id_escritorio}/permission-profile/plan-group/${groupId}/permissions`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const getPlanGroupPermissionsEndpoint = async (
  app: any,
  {
    office,
    groupId,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    groupId: number;
    expectStatus?: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/permission-profile/plan-group/${groupId}/permissions`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
};
export const getPayrollConfigs = async (
  app: INestApplication,
  {
    office,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/payroll/config/all`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const getPayrollOfficeConfigs = async (
  app: INestApplication,
  {
    office,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    expectStatus?: HttpStatus;
  },
): Promise<
  Omit<request.Response, 'body'> & {
    body: SimplePayrollConfigResponse[];
  }
> =>
  request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/payroll/config/office_configs`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const validatePayrollConfig = async (
  app: INestApplication,
  {
    office,
    payroll_id,
    newConfigId,
    expectStatus = HttpStatus.CREATED,
  }: {
    office: Escritorio;
    payroll_id: number;
    newConfigId: number;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .post(`/offices/${office.id_escritorio}/payroll/${payroll_id}/config/validate`)
    .set({ Authorization: 'Bearer 123' })
    .send({ newConfigId })
    .expect(expectStatus);

export const updatePayrollConfig = async (
  app: INestApplication,
  {
    office,
    payroll_id,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    payroll_id: number;
    payload: payrollConfigUpdateDto;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .put(`/offices/${office.id_escritorio}/payroll/${payroll_id}/config`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);

export const getClosureStatus = async (
  app: INestApplication,
  {
    office,
    closureDate,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    closureDate: string;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/closures/status/${closureDate}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const sendDocumentHookEvent = async (
  app: INestApplication,
  message: DocumentSignatureHookEvent,
  expectedHttpStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = '/payroll/events/document-signature';

  const payload = {
    message: {
      data: Buffer.from(JSON.stringify(message)).toString('base64'),
    },
  };

  return request(app.getHttpServer())
    .post(path)
    .set('from', PUBSUB_FROM_HEADER)
    .send(payload)
    .expect(expectedHttpStatus);
};

export const clonePlanEndpoint = async (
  app: INestApplication,
  office: Escritorio,
  payload: CopyPlanRequestDTO,
  expectStatus: HttpStatus = HttpStatus.OK,
): Promise<any> => {
  return request(app.getHttpServer())
    .put(`/offices/${office.id_escritorio}/sheet-db/clone-plan`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);
};

export const notifyPayrollCreditors = async (
  app: INestApplication,
  {
    office,
    payload,
    expectStatus = HttpStatus.CREATED,
  }: {
    office: Escritorio;
    payload: PayrollNotifyDto;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .post(`/offices/${office.id_escritorio}/payroll/notify`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);

export const notifyCreditors = async (
  app: INestApplication,
  {
    office,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    payload: { commission_plan_id: number; period_id: number };
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .post(
      `/offices/${office.id_escritorio}/sheet-db/plan/${payload.commission_plan_id}/period/${payload.period_id}/notify`,
    )
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectStatus);

export const payrollAutoProcessing = async (app: INestApplication, expectStatus = HttpStatus.OK) =>
  request(app.getHttpServer())
    .post(`/events/payroll/auto_process`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const removeClosureLock = async (
  app: INestApplication,
  {
    office,
    closureDate,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    closureDate: string;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .delete(`/offices/${office.id_escritorio}/closures/lock/${closureDate}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const listPaymentRequests = async (
  app: INestApplication,
  officeId: number,
  filters?: ListPaymentRequestsFilters,
) => {
  const path = `/offices/${officeId}/payroll/request`;

  return request(app.getHttpServer()).get(path).set({ Authorization: 'Bearer 123' }).query(filters);
};

export const exportPaymentRequests = async (
  app: INestApplication,
  officeId: number,
  filters?: ListPaymentRequestsFilters,
) => {
  const path = `/offices/${officeId}/payroll/request/export`;

  return request(app.getHttpServer())
    .get(path)
    .set({ Authorization: 'Bearer 123' })
    .query(filters)
    .parse(binaryParser);
};

export const getPaymentRequestOwners = async (app: INestApplication, officeId: number) => {
  const path = `/offices/${officeId}/payroll/request/owners`;

  return request(app.getHttpServer()).get(path).set({ Authorization: 'Bearer 123' });
};

export const getPaymentRequestKeys = async (app: INestApplication, officeId: number) => {
  const path = `/offices/${officeId}/payroll/request/keys`;

  return request(app.getHttpServer()).get(path).set({ Authorization: 'Bearer 123' });
};

export const detailPaymentRequests = async (
  app: INestApplication,
  officeId: number,
  requestId: number,
  expectStatus?: HttpStatus.OK,
) => {
  const path = `/offices/${officeId}/payroll/request/${requestId}`;

  return request(app.getHttpServer()).get(path).set({ Authorization: 'Bearer 123' });
};

export const createPayrollRequest = async (
  app: INestApplication,
  {
    office_id,
    payload,
    expectedHttpStatus = HttpStatus.CREATED,
  }: {
    office_id: number;
    payload: CreatePayrollRequestPayload;
    expectedHttpStatus?: HttpStatus;
  },
) => {
  const path = `/offices/${office_id}/payroll/request`;
  const response = await request(app.getHttpServer())
    .post(path)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectedHttpStatus);

  return handleResponse<CreatePaymentResponse>({
    method: 'POST',
    path,
    response,
    expectedHttpStatus,
  });
};

export const cancelPaymentRequest = async (
  app: INestApplication,
  {
    office_id,
    payments,
  }: {
    office_id: number;
    payments: number[];
  },
) => {
  const path = `/offices/${office_id}/payroll/request/cancel`;
  return request(app.getHttpServer())
    .post(path)
    .set({ Authorization: 'Bearer 123' })
    .send({ payments });
};

export const whoIsRequest = async (
  app: any,
  { email }: { email: string },
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  const path = `/backoffice/whois?email=${email}`;

  const response = await request(app.getHttpServer()).get(path);

  return handleResponse<WhoIsResponse>({
    path,
    response,
    expectedHttpStatus: expectStatus,
    method: 'GET',
  });
};

export const patchPayrollConfig = async (
  app: INestApplication,
  {
    office_id,
    config_id,
    payload,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    office_id: number;
    config_id: number;
    payload: PayrollPatchConfig;
    expectedHttpStatus?: HttpStatus;
  },
) => {
  const path = `/offices/${office_id}/payroll/config/office_configs/${config_id}`;
  const response = await request(app.getHttpServer())
    .patch(path)
    .set({ Authorization: 'Bearer 123' })
    .send(payload);

  return handleResponse<PayrollSettingsResponse>({
    method: 'PATCH',
    path,
    response,
    expectedHttpStatus,
  });
};

export const getCurrentPayrollConfig = async (
  app: INestApplication,
  {
    officeId,
    graphType,
    expectStatus = HttpStatus.OK,
  }: {
    officeId: number;
    graphType: PayrollGraphType;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .get(`/offices/${officeId}/payroll/config/${graphType}/current`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const deletePayrollConfig = async (
  app: INestApplication,
  {
    office,
    configId,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    configId: number;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .delete(`/offices/${office.id_escritorio}/payroll/config/${configId}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const getPacketsToSign = async (
  app: INestApplication,
  {
    office,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .get(
      `/offices/${office.id_escritorio}/document-signer/packets/${PacketTypeEnum.POLICY_AGREEMENT}/to-sign`,
    )
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const getPackets = async (
  app: INestApplication,
  {
    office,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/document-signer/packets`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const getPacket = async (
  app: INestApplication,
  {
    office,
    packetId,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    packetId: number;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .get(`/offices/${office.id_escritorio}/document-signer/packets/${packetId}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const signPacket = async (
  app: INestApplication,
  {
    office,
    packetId,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    packetId: number;
    payload: BackendSignRequest;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .post(`/offices/${office.id_escritorio}/document-signer/packets/${packetId}/sign`)
    .send(payload)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const createPackets = async (
  app: INestApplication,
  {
    office,
    payload,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    payload: CreatePacketsRequest;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .post(`/offices/${office.id_escritorio}/document-signer/packets`)
    .send(payload)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export async function getValidationConfigByOffice(
  app: INestApplication,
  { officeId, expectedStatus = HttpStatus.OK }: { officeId: number; expectedStatus: number },
) {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/ocr/validation/config`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectedStatus);
}

export async function getObjectWorkflowContext(
  app: INestApplication,
  {
    officeId,
    objectKey,
    expectStatus = 200,
  }: { officeId: number; objectKey: string; expectStatus?: number },
) {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/object/workflow/${objectKey}/context`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
}

export async function getObjectWorkflowHistory(
  app: INestApplication,
  {
    officeId,
    objectKey,
    expectStatus = 200,
  }: { officeId: number; objectKey: string; expectStatus?: number },
) {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/object/workflow/${encodeURIComponent(objectKey)}/history`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
}

export async function getExtractionResults(
  app: INestApplication,
  {
    officeId,
    fileId,
    expectStatus = 200,
  }: { officeId: number; fileId: number; expectStatus?: number },
) {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/ocr/extraction/result/${fileId}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
}

export async function patchValidationConfig(
  app: INestApplication,
  {
    officeId,
    configId,
    payload,
  }: {
    officeId: number;
    configId: number;
    payload: UpdateOcrValidation;
  },
) {
  return request(app.getHttpServer())
    .patch(`/offices/${officeId}/ocr/validation/config/${configId}`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload);
}

export async function postValidationConfig(
  app: INestApplication,
  officeId: number,
  body: ValidationConfig[] = [],
) {
  return request(app.getHttpServer())
    .post(`/offices/${officeId}/ocr/validation/config`)
    .set({ Authorization: 'Bearer 123' })
    .send(body);
}

export async function postValidateExctractionResult(
  app: INestApplication,
  {
    officeId,
    fileId,
    validationId,
    expectStatus = 200,
    body = {},
  }: {
    officeId: number;
    fileId: number;
    validationId: number;
    expectStatus?: number;
    body?: OcrContext;
  },
) {
  return request(app.getHttpServer())
    .post(`/offices/${officeId}/ocr/validation/result/${validationId}/${fileId}`)
    .set({ Authorization: 'Bearer 123' })
    .send(body)
    .expect(expectStatus);
}

export async function postValidationTester(
  app: INestApplication,
  {
    officeId,
    fileId,
    context,
    validations,
  }: {
    officeId: number;
    fileId: number;
    context: {};
    validations: ValidationConfig[];
  },
) {
  return request(app.getHttpServer())
    .post(`/offices/${officeId}/ocr/validation/tester/${fileId}`)
    .set({ Authorization: 'Bearer 123' })
    .send({ context, validations });
}

export const upsertUserCredentials = async <T = any>(
  app: any,
  {
    officeId,
    externalCreditorId,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    externalCreditorId: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/office/${officeId}/user/${externalCreditorId}/credential`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer()).put(path).set(headers);

  return handleResponse<T>({ path, response, expectedHttpStatus });
};

export const updateUserPassword = async (
  app: any,
  {
    officeId,
    sessionId,
    password,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    officeId: number;
    sessionId: string;
    password: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/office/${officeId}/user/credential`;
  const headers: any = { Authorization: 'Bearer 123' };

  const response = await request(app.getHttpServer())
    .patch(path)
    .set(headers)
    .send({ sessionId, password });

  return handleResponse<void>({ path, response, expectedHttpStatus });
};

export const patchPackets = async (
  app: INestApplication,
  {
    office,
    payload,
    packetId,
    expectStatus = HttpStatus.OK,
  }: {
    office: Escritorio;
    payload: PatchPacketRequest;
    packetId: number;
    expectStatus?: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .patch(`/offices/${office.id_escritorio}/document-signer/packets/${packetId}`)
    .send(payload)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);

export const deletePackets = async (
  app: INestApplication,
  {
    office,
    packet_ids,
    expectedStatus,
  }: {
    office: Escritorio;
    packet_ids;
    expectedStatus: HttpStatus;
  },
) =>
  request(app.getHttpServer())
    .delete(`/offices/${office.id_escritorio}/document-signer/packets`)
    .send({ packet_ids })
    .set({ Authorization: 'Bearer 123' })
    .expect(expectedStatus);

export const outboundWebhooksListGet = async (
  app: INestApplication,
  {
    officeId,
    expectedStatus,
  }: {
    officeId: number;
    expectedStatus: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/outbound/webhooks/`)
    .set({ Authorization: 'Bearer 123' })
    .send()
    .expect(expectedStatus);
};

export const outboundWebhooksGet = async (
  app: INestApplication,
  {
    officeId,
    webhookId,
    expectedStatus,
  }: {
    officeId: number;
    webhookId: number;
    expectedStatus: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/outbound/webhooks/${webhookId}`)
    .set({ Authorization: 'Bearer 123' })
    .send()
    .expect(expectedStatus);
};

export const outboundWebhooksPost = async (
  app: INestApplication,
  {
    officeId,
    payload,
    expectedStatus,
  }: {
    officeId: number;
    payload: CreateWebhookRequest;
    expectedStatus: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .post(`/offices/${officeId}/outbound/webhooks/`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectedStatus);
};

export const outboundWebhooksPatch = async (
  app: INestApplication,
  {
    officeId,
    webhookId,
    payload,
    expectedStatus,
  }: {
    officeId: number;
    webhookId: number;
    payload: UpdateWebhookRequest;
    expectedStatus: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .patch(`/offices/${officeId}/outbound/webhooks/${webhookId}`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload)
    .expect(expectedStatus);
};

export const outboundWebhooksDelete = async (
  app: INestApplication,
  {
    officeId,
    webhookId,
    expectedStatus,
  }: { officeId: number; webhookId: number; expectedStatus: HttpStatus },
) => {
  return request(app.getHttpServer())
    .delete(`/offices/${officeId}/outbound/webhooks/${webhookId}`)
    .set({ Authorization: 'Bearer 123' })
    .send()
    .expect(expectedStatus);
};

export const outboundWebhooksTest = async (
  app: INestApplication,
  {
    officeId,
    payload,
  }: {
    officeId: number;
    payload: TesterWebhookRequest;
  },
) => {
  return request(app.getHttpServer())
    .post(`/offices/${officeId}/outbound/webhooks/tester`)
    .set({ Authorization: 'Bearer 123' })
    .send(payload);
};

export const extractNFInfo = async (
  app: INestApplication,
  {
    officeId,
    fileId,
    expectedStatus = HttpStatus.CREATED,
  }: {
    officeId: number;
    fileId: number;
    expectedStatus?: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .post(`/offices/${officeId}/ocr/extraction/result/nf/${fileId}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectedStatus);
};

export const upsertExtractionConfig = async (
  app: INestApplication,
  {
    officeId,
    payload,
    expectedStatus = HttpStatus.OK,
  }: {
    officeId: number;
    payload: CreateExtractionConfigRequest;
    expectedStatus?: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .put(`/offices/${officeId}/ocr/extraction/config`)
    .send(payload)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectedStatus);
};

export const getValidationResult = async (
  app: INestApplication,
  {
    officeId,
    fileIds,
    validationConfigId,
    expectedStatus = HttpStatus.OK,
  }: {
    officeId: number;
    fileIds?: number[] | number;
    validationConfigId: number;
    expectedStatus?: HttpStatus;
  },
) => {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/ocr/validation/result/${validationConfigId}`)
    .set({ Authorization: 'Bearer 123' })
    .query({ fileIds })
    .expect(expectedStatus);
};

export const getClosuresStatus = (
  app: INestApplication,
  officeId: number,
  expectStatus: HttpStatus = HttpStatus.OK,
) => {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/closures/status`)
    .expect(expectStatus);
};

export async function getObjectWorkflow(
  app: INestApplication,
  {
    officeId,
    objectKey,
    expectStatus = 200,
  }: { officeId: number; objectKey: string; expectStatus?: number },
) {
  return request(app.getHttpServer())
    .get(`/offices/${officeId}/object/workflow/${objectKey}`)
    .set({ Authorization: 'Bearer 123' })
    .expect(expectStatus);
}

export const updateWorkflowInputs = async (
  app: INestApplication,
  {
    office_id,
    workflowKey,
    dto,
    expectStatus = 200,
  }: {
    office_id: number;
    workflowKey: string;
    dto: UpdateWorkflowInputs;
    expectStatus?: number;
  },
) => {
  const path = `/offices/${office_id}/object/workflow/${workflowKey}/inputs`;
  return request(app.getHttpServer())
    .put(path)
    .set({ Authorization: 'Bearer 123' })
    .send(dto)
    .expect(expectStatus);
};

export const starkbankTransferEvent = async (
  app: INestApplication,
  {
    body,
    expectedHttpStatus = HttpStatus.OK,
    digitalSignature,
  }: {
    digitalSignature?: string;
    body?: StarkEvent;
    expectedHttpStatus?: number;
  },
) => {
  const path = WEBHOOKS_DEFAULT_ROUTE_V2;
  const headers: any = { Authorization: 'Bearer 123' };
  if (digitalSignature) headers['digital-signature'] = digitalSignature;
  headers['content-type'] = 'application/json';

  const response = await request(app.getHttpServer()).post(path).set(headers).send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const authByOidcOrganization = async (
  app: INestApplication,
  {
    body,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    body: LoginByOidcRequestDTO;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/login/auth/organization`;

  const response = await request(app.getHttpServer())
    .post(path)
    .set({ Accept: 'application/json' })
    .send(body);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getAllowedOfficesByUser = async (
  app: INestApplication,
  {
    token,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    token?: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/organization/user`;

  const response = await request(app.getHttpServer())
    .get(path)
    .set({ Authorization: `Bearer ${token ?? '123'}` });

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getAssistantUiToken = async (
  app: INestApplication,
  {
    body,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    body: ChatTokenRequestSchemaDto;
    expectedHttpStatus?: number;
  },
) => {
  const path = '/assistant-ui/tokens';

  const response = await request(app.getHttpServer()).post(path).send(body);

  return handleResponse<AiChatTokenResponse>({ path, response, expectedHttpStatus });
};

export const loginIntoTenant = async (
  app: INestApplication,
  {
    token,
    headers,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    token?: string;
    headers?: Record<string, string>;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/organization/login`;
  const finalHeaders: any = { ...headers, Authorization: `Bearer ${token ?? '123'}` };

  const response = await request(app.getHttpServer()).post(path).set(finalHeaders);

  return handleResponse({ path, response, expectedHttpStatus });
};

export const getUserTenants = async (
  app: INestApplication,
  {
    token,
    expectedHttpStatus = HttpStatus.OK,
  }: {
    token?: string;
    expectedHttpStatus?: number;
  },
) => {
  const path = `/user/tenants`;

  const response = await request(app.getHttpServer())
    .get(path)
    .set({ Authorization: `Bearer ${token ?? '123'}` });

  return handleResponse({ path, response, expectedHttpStatus });
};
