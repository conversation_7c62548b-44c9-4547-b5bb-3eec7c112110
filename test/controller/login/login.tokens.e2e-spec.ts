import { vi } from 'vitest';
import { Test } from '@nestjs/testing';
import { ConfigurationEnv } from '@app/config/configuration.env';
import { AppModule } from '@app/app.module';
import bodyParser from 'body-parser';
import { INestApplication, HttpStatus } from '@nestjs/common';
import {
  createFakeOrganization,
  createOidcProvider,
  createTestOffice,
} from '../../utils/massa.utils';
import {
  authByOidc,
  authByOidcOrganization,
  implicitAuthByOidc,
  refreshToken,
  ssoEndpoint,
} from '../../utils/common.requests';
import { Credor } from '@app/models/credor';
import { Logger } from 'nestjs-pino';
import { AtributosEscritorio } from '@app/models/atributosescritorio';
import { UsersClaims, OIDC_PROVIDER_PREFIX } from 'shared-types';
import {
  BoaVistaSSOConfig,
  DirecionalBoraVenderSSOConfig,
  MontCapitalSSOConfig,
  SSOIdentifierType,
  TeddySSOConfig,
} from '@app/domain/auth/sso';
import jwt from 'jsonwebtoken';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { Atributos_credor } from '@app/models/atributos_credor';
import { consultOidcProviders } from '../../utils/common.requests';
import { OidcDiscoveryData, Oidc_Provider, UserInfoGetter } from '@app/models/oidc_provider';
import { SignJWT, JWTPayload, importJWK } from 'jose';
import uuid = require('uuid');
import { LoginByOidcRequestDTO } from '@app/dto/login.request.dto';
import cookieParser from 'cookie-parser';
import { CryptoUtils } from '@app/utils/crypto.utils';
import { User_Identifier } from '@app/models/user_identifier';
import { SECRET_VAULT, SecretVault } from '@app/services/google/secret-manager.service';
import { backofficeList } from '@app/controller/backoffice/constants';
import { faker } from '@faker-js/faker';
import { normalizeCreditor } from '@app/utils/string.utils';
import { Organization } from '@app/models/organization';
import { Org_User_Identifier } from '@app/models/org_user_identifier';
import { Escritorio } from '@app/models/escritorio';
import { User } from '@app/models/user';

vi.mock('starkbank');

const MAGIC_LINK_ID = 4;

const callinkJwks = {
  keys: [
    {
      e: 'AQAB',
      n: 'td-XMCHA2zjTC5u1NOLLV5gEX-KkET55TWDFEek9bRolpRqmI_nmJy3J_D6CrvDbeCrBKaXerv5Li5MspObiDzddmIknjdbVgRkTodNFe29Bow4HAVNpO-Gvx42cpDTPuXImyubFzGAJDSAtlEZRK48d4l3_VjhRL_0QEOZ5FiX33R3IA2RT-Ay6tv0dg-4XsolzV0Boe8JXPngGyPvBaPUfr0Ev5pS8afdMVB9zyAVra87Zhm6b8ISe2U3HQBBIAduxwQFOmDynecIfSJrxgSoum_QN5TecBhjTI23N7gqucjiqvK5n78TLz7n5cEImA0k1Szh-1AuuzaXTDe8bDQ',
      alg: 'RS256',
      kid: 'EMe2AdxLLrggMl-IFngUEJ_-tGM',
      kty: 'RSA',
      use: 'sig',
      x5c: [
        'MIIC4DCCAcigAwIBAgIQds/eCnPD47NPq9CV5qQLxzANBgkqhkiG9w0BAQsFADAsMSowKAYDVQQDEyFBREZTIFNpZ25pbmcgLSBmc28uY2FsbGluay5jb20uYnIwHhcNMjMwNzE4MTIzODU4WhcNMjQwNzE3MTIzODU4WjAsMSowKAYDVQQDEyFBREZTIFNpZ25pbmcgLSBmc28uY2FsbGluay5jb20uYnIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC135cwIcDbONMLm7U04stXmARf4qQRPnlNYMUR6T1tGiWlGqYj+eYnLcn8PoKu8Nt4KsEppd6u/kuLkyyk5uIPN12YiSeN1tWBGROh00V7b0GjDgcBU2k74a/HjZykNM+5cibK5sXMYAkNIC2URlErjx3iXf9WOFEv/RAQ5nkWJffdHcgDZFP4DLq2/R2D7heyiXNXQGh7wlc+eAbI+8Fo9R+vQS/mlLxp90xUH3PIBWtrztmGbpvwhJ7ZTcdAEEgB27HBAU6YPKd5wh9ImvGBKi6b9A3lN5wGGNMjbc3uCq5yOKq8rmfvxMvPuflwQiYDSTVLOH7UC67NpdMN7xsNAgMBAAEwDQYJKoZIhvcNAQELBQADggEBACyNO9bnFZfV3lYJQmOUKjgvVrrJV+bpyHREJtBv1QYWrWN8F0EDLe9wrNyP0YLXR16KqANrRL28RN95piLvNTMc3PN5/tpguBArogJkOOtaCBwsay0i4/ZeQDsxMCHLNxsyRoAYxtQQWXHXbb+7IXJlqvXLVJOpQ5Y2lXDToUVSOJIaYVuuMFUym+9RuWshMA4BwKzvhDNfP2uJ/80kQFkOWVqUP1/XjSptFXI+5GpXMuik+En/GMsKIwP6tjfQfs4Lrkv4rmKwxqlOR+jX9QlVK7OjtOSFJ7pQRpxYIoL07EohCjMEK747S2anx/0Srf09eOgOu4TaEvy0rls2vAo=',
      ],
      x5t: 'EMe2AdxLLrggMl-IFngUEJ_-tGM',
    },
  ],
};

const JWK_MOCKED = {
  p: '1iaYjr_cOGDFxy1niUk8DzYNJdbaN-uvQC3LECsjUhFAn-bLH0Fq7OtudfG_KveLwj3q8q7yZZE0W_3OLp9011Vx6D8wYVZhlEXTerUnL4j5eYOqodHl3bUqADEIUV5TJNKpdVJJH1teh-8bNgxV98rG0aoSHvKkcjZyu_qrF1E',
  kty: 'RSA',
  q: 'wm2YpGM349W4LrijittW25LWxHMGLG3AIya73xIqfraWjYwdqqhcOZhsccyMFtEJ6VeEy_HX3Qa-8_dpgnBLF7so8aCWwMF3GkuTFFa2tOsh8gmgSXPWL7Z9Uej9b_sSCxRaX479myuDxIiS5M46YHnyGvMk9wFVJzTXyvrbwgM',
  d: 'eFQY5hmAFjizLW70Gur14oY50l3As7YPi46IlOGs3H1CdUxh7MXqCs-aWrrHojAVICp1ig5wYNNIPxGw-cfy56FQfD9N6jYqT-c7gGZ1n7NsZhpEOJ7KBDizCZnURRKhDAgZxzMcCycDplABQzwy6MSg4zzssvWHuDLnfJqRJ8UluY6PUiunpTkeFGS4LSslyRaRDqp3mnHQ1hIA-xJBXWsJsVP_y8pGer2J9ncYGwoO233vaXwaK2eXazUpQKh5SFFiuZdNG2xykJZYwKY5kiRcf2u4CH8dHcINNWpv-m25Zix5ah2QwTj7P1iv9iRcuQzAezEYqca1BLilVb1zwQ',
  e: 'AQAB',
  use: 'sig',
  kid: '1234',
  qi: 'Trr9vs_UVXT-23ZI65JaC5KxkCBSgAiyu_ykf7rO0xIpniVt3b4dDIpdPpu-SgpArP-3E6_Bxy29or0Y3jq1ylYbhSpywujjfq8kghJIqpTnj8jMdMqs9_s7kuvySlfaIaPuTUcOZ2m0HQfRxobv3mT05j1cntjwo1q2wBUQ38o',
  dp: 'JpPhW6XYaBeWyWC2jBazSRI1oCFqQjSk52zYbds3i6sr9yI-aS0BtBhdV8Xq6DSb6qv4zSsS45Ua14-LOv7ir3_m-y2W65ICFhHWHP7RRlDokClV7bzAKgZewJYUp4PUeoewmz_tiEaPavF592yVeAQBePeoOAcB9Mk2NgLcG2E',
  dq: 'QV47nblGBzHJtNubEIpiD3y0_GN3xeQ4XxkcvQkMhIG66GgdaU0lhK-8dh5BU0KAxJUKLs9mtgcYmEmsjZBXjuj8ARDu3mutMYgLMSFeWEFdrlSSMV_iuTPvumRNISMWk-cJ42rd1ReyO5d3W7oCYoFdZrYszWcDTCU2ECZ7UCU',
  n: 'oqTtu5B6vrv99nGxwxuIk7tCAxoxng_2YMfP8zan6VQv9Gue8N31rSgqNYXWD9EnQHx3WViObQ8uFahgHK9mq5NyRnmDCz8eTo0zqrrVce9GHT5TShewWmfiBb_DrUpMjRWVR4wHYfUznMzFvOPTzU-OKkpyJnhqmWefBEQ0QxKjBAAUl25XoGvAeWim0TN7r9V4YGMtCBnRjV_pR2UiP1ODjhPzzXe1_DRkbBzvMknh4-Dlqhc1CvSVd2QAtncyruDAZguTGMtgZkzv9VvJcZKJl9_qCb1515HhA7U2fjnhdBexOQslAUfX_sVHpxDPB-U7AmgyySbiOb41o_en8w',
};

const DEFAULT_MOCKED_JWKS = {
  keys: [
    {
      kty: JWK_MOCKED.kty,
      e: JWK_MOCKED.e,
      use: JWK_MOCKED.use,
      kid: JWK_MOCKED.kid,
      n: JWK_MOCKED.n,
      x5t: '',
      x5c: [],
    },
  ],
};

const setupJwt = async (payload: JWTPayload, kid = JWK_MOCKED.kid) => {
  const secret = await importJWK(JWK_MOCKED, 'RS256');
  const jwt = await new SignJWT(payload)
    .setProtectedHeader({
      alg: 'RS256',
      kid: kid,
      typ: 'JWT',
    })
    .sign(secret);

  return jwt;
};

const config = new ConfigurationEnv();

class MockSecretImpl implements SecretVault {
  async getSecret(name: string): Promise<string> {
    if (name === 'error') {
      throw new Error(name);
    }
    return name;
  }
  async createSecret(name: string, value: string): Promise<void> {}
  async updateSecret(name: string, value: string): Promise<void> {}
  async deleteSecret(name: string): Promise<void> {}
}

describe('Get tokens endpoint', () => {
  const server = setupServer();
  let app: INestApplication;
  const secretManager = new MockSecretImpl();

  beforeAll(async () => {
    server.listen({ onUnhandledRequest: 'bypass' });
    Object.defineProperty(config, 'tokenValidationEnabled', { value: false });

    const module = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .overrideProvider(SECRET_VAULT)
      .useValue(secretManager)
      .compile();
    module.useLogger(module.get(Logger));

    app = module.createNestApplication({ bodyParser: false });
    app.use(bodyParser.json({ limit: '50mb' }));
    app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
    app.use(cookieParser());
    await app.init();
  });

  describe('Consult oidc providers by email', () => {
    const setup = async () => {
      const [defaultOidcProvidersMock, notDefaultProviderMock] = await Promise.all([
        createOidcProvider({
          issuer: `${uuid()}-microsoft`,
          slug: `${uuid()}-microsoft`,
          // @ts-ignore
          discovery_data: {
            type: 'oidc',
            authorization_endpoint: 'https://google.com/authorization',
          },
          discovery_url: 'https://google.com/discovery',
          // @ts-ignore
          jwks: [],
          user_info: { email: { claim: 'email' } },
          identifier_type: 'email',
          client_id: '1234',
          secret_name: '',
          default: true,
        }),
        createOidcProvider({
          issuer: `${uuid()}-google`,
          slug: `${uuid()}-google`,
          // @ts-ignore
          discovery_data: {
            type: 'oidc',
            authorization_endpoint: 'https://google.com/authorization',
          },
          // @ts-ignore
          jwks: [],
          user_info: { email: { claim: 'email' } },
          identifier_type: 'email',
          client_id: '',
          secret_name: '',
          default: false,
        }),
      ]);
      const officeMock = await createTestOffice();
      const credorMock = await Credor.create({
        escritorio_id_escritorio: officeMock.id_escritorio,
        id_credor_externo: 'external_credor',
        ativo: true,
        email: `${uuid()}-<EMAIL>`,
      });

      return {
        defaultOidcProvidersMock,
        notDefaultProviderMock,
        credorMock,
        officeMock,
      };
    };

    it.each`
      shouldMockEmail | description
      ${false}        | ${'when user not exists'}
      ${true}         | ${'when the office has no OFFICE_LOGIN_CONFIG claim'}
    `('should return only default oidc providers $description', async ({ shouldMockEmail }) => {
      let email = `${uuid()}<EMAIL>`;
      if (shouldMockEmail) {
        const { credorMock } = await setup();
        email = credorMock.email;
      }

      const { body } = await consultOidcProviders(app, {
        payload: { email },
      });

      const defaultProviders = (
        await Oidc_Provider.findAll({
          where: { default: true },
        })
      ).filter(
        ({ opts }) =>
          !opts || !opts.allowed_email_domain || opts.allowed_email_domain.includes('gmail'),
      );

      expect(body.providers).toHaveLength(defaultProviders.length);
    });

    it('should return oidc provider belonging to the offices that the user has access to', async () => {
      const cognitoProvider = await Oidc_Provider.findOne({
        where: {
          id: config.userAndPasswordOidcId,
        },
      });

      const { officeMock, defaultOidcProvidersMock, notDefaultProviderMock, credorMock } =
        await setup();

      if (defaultOidcProvidersMock.discovery_data.type !== 'oidc') throw new Error();
      if (notDefaultProviderMock.discovery_data.type !== 'oidc') throw new Error();
      if (cognitoProvider.discovery_data.type !== 'oidc') throw new Error();

      notDefaultProviderMock.opts.office_id = officeMock.id_escritorio;
      notDefaultProviderMock.changed('opts', true);
      await notDefaultProviderMock.save();

      const { body } = await consultOidcProviders(app, {
        payload: { email: credorMock.email },
      });

      // remove all oidcs with no have gmail on allowed_email_domain
      const defaultProviders = (
        await Oidc_Provider.findAll({
          where: { default: 1 },
        })
      ).filter(
        ({ opts }) =>
          !opts ||
          !opts.allowed_email_domain ||
          !opts.allowed_email_domain.length ||
          opts.allowed_email_domain?.includes('gmail'),
      );

      expect(body.providers).toHaveLength(defaultProviders.length + 1);

      expect(body.providers.find(p => p.id === notDefaultProviderMock.id)).toBeTruthy();
    });

    it('should filter default oidc provider if exists the "allowed_email_domain" opts', async () => {
      const emailWithDomainNotAllowed = '<EMAIL>';
      const shouldntReturnForThisEmail = await createOidcProvider({
        opts: {
          allowed_email_domain: ['domainAllowed1', 'domainAllowed2'],
          user_info: {},
          identifier_type: 'email',
        },
        default: true,
      });

      const providerWithEmailAllowed = await createOidcProvider({
        opts: {
          allowed_email_domain: ['domainNotAllowed'],
          user_info: {},
          identifier_type: 'email',
        },
        default: true,
      });

      const { body } = await consultOidcProviders(app, {
        payload: { email: emailWithDomainNotAllowed },
      });

      const providerDisallowedDomain = body.providers.find(
        p => p.id === shouldntReturnForThisEmail.id,
      );

      const providerWithAllowedDomain = body.providers.find(
        p => p.id === providerWithEmailAllowed.id,
      );

      expect(providerDisallowedDomain).toBeFalsy();
      expect(providerWithAllowedDomain).toBeTruthy();
    });

    it('should return providers from organization', async () => {
      const organization = await createFakeOrganization();
      const email = `any_email@${organization.email_domain}`;

      const oidc = await createOidcProvider({
        org_id: organization.id,
        pretty_name: `login with organization type 1`,
        default: false,
      });

      const oidc2 = await createOidcProvider({
        org_id: organization.id,
        pretty_name: `login with organization type 2`,
        default: false,
      });

      const { body } = await consultOidcProviders(app, {
        payload: { email },
      });

      const defaultProviders = (
        await Oidc_Provider.findAll({
          where: { default: true },
        })
      ).filter(
        ({ opts }) =>
          !opts ||
          !opts.allowed_email_domain ||
          opts.allowed_email_domain.includes(organization.email_domain),
      );

      expect(body.providers).toHaveLength(defaultProviders.length);
      const expectedOrgProviders = [
        {
          id: oidc.id,
          type: 'oidc',
          name: oidc.pretty_name,
          slug: oidc.slug,
          org_id: organization.id,
          oauthInfo: {
            authorization_endpoint: 'https://google.com/authorization',
            client_id: expect.any(String),
            scope: 'email',
          },
        },
        {
          id: oidc2.id,
          type: 'oidc',
          name: oidc2.pretty_name,
          slug: oidc2.slug,
          org_id: organization.id,
          oauthInfo: {
            authorization_endpoint: 'https://google.com/authorization',
            client_id: expect.any(String),
            scope: 'email',
          },
        },
      ];
      expect(body.org_providers).toHaveLength(2);
      expect(body.org_providers).toEqual(expectedOrgProviders);
    });
  });

  const generateBodyMock = (data?: Partial<LoginByOidcRequestDTO>) => {
    return {
      code: 'any_code',
      idProvider: 1,
      redirect_uri: 'https://redirect.example.com',
      scope: 'scope1+scope2',
      nonce: 'any_nonce',
      ...data,
    };
  };

  const generateMock = async (
    email: string,
    opts?: { orgId?: number; userInfo?: Partial<UserInfoGetter>; jwtPayload?: Partial<JWTPayload> },
  ) => {
    const jwtPayload = {
      email,
      nonce: 'jwt_nonce',
      aud: '',
      ...opts?.jwtPayload,
    };
    const office = await createTestOffice();
    const user = await Credor.create({
      escritorio_id_escritorio: office.id_escritorio,
      id_credor_externo: uuid(),
      ativo: false,
      email: jwtPayload.email,
    });
    const oidc = await createOidcProvider({
      org_id: opts?.orgId,
      jwks: DEFAULT_MOCKED_JWKS,
      opts: {
        user_info: opts?.userInfo ?? { email: { claim: 'email' } },
        identifier_type: 'email',
      },
    });
    const jwt = await setupJwt(jwtPayload);
    return { oidc, jwt, user, office, jwtPayload };
  };

  describe('authentication by oidc', () => {
    it('returns 401 when oidc is not found', async () => {
      await authByOidc(app, {
        body: generateBodyMock({
          idProvider: -1,
        }),
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns 401 when oidc code is invalid', async () => {
      const oidc = await createOidcProvider();
      if (oidc.discovery_data.type !== 'oidc') throw new Error();
      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (req, res, ctx) => {
          return res(ctx.json({ code: 'INVALID CODE' }), ctx.status(401));
        }),
      );

      await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
        }),
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns 500 when jwks provider not has keys', async () => {
      const oidc = await createOidcProvider({ jwks: { keys: [] } });
      if (oidc.discovery_data.type !== 'oidc') throw new Error();
      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (req, res, ctx) => {
          return res(
            ctx.json({
              id_token:
                'eyJhbGciOiJSUzI1NiIsImtpZCI6IjZmOTc3N2E2ODU5MDc3OThlZjc5NDA2MmMwMGI2NWQ2NmMyNDBiMWIiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YM1htnvK0h1vMtngtST2_so0JC0othSkr8_IdKhInZjpPFI1sFysAPEEjGqtpGERvXDg17Tw0k1ccDpI7DX07alBIbcS3XqvjWTj5alZfJes6axF2cyVTC6qt5mg4u2fF86xErMo9b5tectuScLluSNd_HsU8r2lzhXxazz7sScWOpSvkbv0moM09ZTZRvx6DtPRKU-9KAuywuzsld2IA62TxCv7dsonTg6ofEr6xLj3ieu5P8M9VJ0H0glln69-P5iAlSxEUQPRcGymHgX_sSnlUBDHTAsnRuvFUK40gD_uoy7HTxrQzmDF6moPeiv2T4AMN7hLrhJwwLN8B-htFA',
            }),
            ctx.status(200),
          );
        }),
      );

      await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
        }),
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns 500 when id_token is different from oidc jwks keys`s kid', async () => {
      const oidc = await createOidcProvider({
        jwks: {
          keys: [{ kty: 'RSA', use: 'sig', kid: '1234', x5t: '2ZQ', n: '', e: 'AQAB', x5c: [] }],
        },
      });
      if (oidc.discovery_data.type !== 'oidc') throw new Error();

      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (req, res, ctx) => {
          return res(ctx.json({ id_token: await setupJwt({}, 'invalid_kid') }), ctx.status(200));
        }),
        rest.get(oidc.discovery_url, async (req, res, ctx) => {
          if (oidc.discovery_data.type !== 'oidc') throw new Error();
          return res(ctx.json({ jwks_uri: oidc.discovery_data.jwks_uri }), ctx.status(200));
        }),
        rest.get(oidc.discovery_data.jwks_uri, async (req, res, ctx) => {
          return res(
            ctx.json({
              keys: [
                { kty: 'RSA', use: 'sig', kid: '1234', x5t: '2ZQ', n: '', e: 'AQAB', x5c: [] },
              ],
            }),
            ctx.status(200),
          );
        }),
      );

      await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
        }),
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns 401 when jwt signature verification failed', async () => {
      const jwtWithKid =
        'eyJhbGciOiJIUzI1NiIsImtpZCI6IjEyMzQiLCJ0eXAiOiJKV1QifQ.**************************************************.XnuxsdeBTpYVehI_eUZlDsw2-NYpL9AT03zsr_MK4io';
      const oidc = await createOidcProvider({
        jwks: {
          keys: [{ kty: 'RSA', use: 'sig', kid: '1234', x5t: '2ZQ', n: '', e: 'AQAB', x5c: [] }],
        },
      });
      if (oidc.discovery_data.type !== 'oidc') throw new Error();
      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (req, res, ctx) => {
          return res(ctx.json({ id_token: jwtWithKid }), ctx.status(200));
        }),
      );

      await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
        }),
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns 401 when user is not active', async () => {
      const { oidc, jwt, jwtPayload } = await generateMock(
        `${uuid()}-<EMAIL>`,
      );
      if (oidc.discovery_data.type !== 'oidc') throw new Error();
      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (_, res, ctx) => {
          return res(ctx.json({ id_token: jwt }), ctx.status(200));
        }),
      );

      await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
          nonce: jwtPayload.nonce,
        }),
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns 401 when user office does not have a specific oidc provider claim', async () => {
      const { oidc, jwt, jwtPayload } = await generateMock(`${uuid()}-<EMAIL>`);
      if (oidc.discovery_data.type !== 'oidc') throw new Error();
      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (_, res, ctx) => {
          return res(ctx.json({ id_token: jwt }), ctx.status(200));
        }),
      );

      await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
          nonce: jwtPayload.nonce,
        }),
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns correct data when all validations have passed', async () => {
      const { oidc, jwt, user, office, jwtPayload } = await generateMock(
        `${uuid()}-<EMAIL>`,
      );
      if (oidc.discovery_data.type !== 'oidc') throw new Error();
      await user.update({
        ativo: true,
      });
      await user.save();
      await AtributosEscritorio.create({
        escritorio_id_escritorio: office.id_escritorio,
        atributo: UsersClaims.OFFICE_LOGIN_CONFIG,
        valor: JSON.stringify({
          allowed_methods: [`${OIDC_PROVIDER_PREFIX}${oidc.id}`],
        }),
      });
      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (_, res, ctx) => {
          return res(ctx.json({ id_token: jwt, access_token: '1234' }), ctx.status(200));
        }),
      );

      const { body } = await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
          nonce: jwtPayload.nonce,
        }),
      });
      expect(body).toEqual({
        access_token: jwt,
        usuario: {
          email: user.email,
          id_credor_externo: user.id_credor_externo,
          id_escritorio: user.escritorio_id_escritorio,
          nome_escritorio: office.nome_escritorio,
        },
      });
    });

    it('should create user and user_indentifier when oidc opts has office_id and user not exists', async () => {
      const office = await createTestOffice();
      const creditorIdParsed = Math.random().toString();
      const email = `${uuid()}-<EMAIL>`;
      const jwtPayload = {
        id: `${creditorIdParsed} indentifier`,
        nonce: 'nonce',
        sub: 'provider_id',
        email,
        aud: '',
      };
      const [jwt, oidc] = await Promise.all([
        setupJwt(jwtPayload),
        createOidcProvider({
          jwks: DEFAULT_MOCKED_JWKS,
          opts: {
            office_id: office.id_escritorio,
            identifier_type: 'office_id+creditor_id',
            user_info: {
              creditor_id: {
                claim: 'id',
                extractor: '[^0-9.,]',
              },
              email: {
                claim: 'email',
              },
              provider_id: {
                claim: 'sub',
              },
            },
            create_user: true,
          },
        }),
      ]);
      if (oidc.discovery_data.type !== 'oidc') throw new Error();

      await AtributosEscritorio.create({
        escritorio_id_escritorio: office.id_escritorio,
        atributo: UsersClaims.OFFICE_LOGIN_CONFIG,
        valor: JSON.stringify({
          allowed_methods: [`${OIDC_PROVIDER_PREFIX}${oidc.id}`],
        }),
      });
      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (_, res, ctx) => {
          return res(ctx.json({ id_token: jwt, access_token: '1234' }), ctx.status(200));
        }),
      );

      const { body } = await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
          nonce: jwtPayload.nonce,
        }),
      });
      const userIdentifier = await User_Identifier.findOne({
        where: { provider_id: jwtPayload.sub, oidc_id: oidc.id },
      });

      expect(body).toEqual({
        access_token: jwt,
        usuario: {
          email: jwtPayload.email.toLowerCase(),
          id_credor_externo: creditorIdParsed,
          id_escritorio: office.id_escritorio,
          nome_escritorio: office.nome_escritorio,
        },
      });
      expect(userIdentifier).toMatchObject({
        provider_id: jwtPayload.sub,
        oidc_id: oidc.id,
      });
    });

    it('should remove user deleted_at when trying to login with a provider that tries to create users', async () => {
      const office = await createTestOffice();
      const creditorId = uuid();
      const email = `${creditorId}-<EMAIL>`;

      const jwtPayload = {
        id: creditorId,
        nonce: 'nonce',
        sub: 'provider_id',
        email,
        aud: '',
      };
      const [jwt, oidc] = await Promise.all([
        setupJwt(jwtPayload),
        createOidcProvider({
          jwks: DEFAULT_MOCKED_JWKS,
          opts: {
            office_id: office.id_escritorio,
            identifier_type: 'office_id+creditor_id',
            user_info: {
              creditor_id: { claim: 'id' },
              email: { claim: 'email' },
              provider_id: { claim: 'sub' },
            },
            create_user: true,
          },
        }),
      ]);

      if (oidc.discovery_data.type !== 'oidc') throw new Error();

      const [credor] = await Promise.all([
        Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: creditorId,
          ativo: true,
          email,
          deleted_at: new Date().toISOString(),
        }),
        AtributosEscritorio.create({
          escritorio_id_escritorio: office.id_escritorio,
          atributo: UsersClaims.OFFICE_LOGIN_CONFIG,
          valor: JSON.stringify({
            allowed_methods: [`${OIDC_PROVIDER_PREFIX}${oidc.id}`],
          }),
        }),
      ]);

      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (_, res, ctx) => {
          return res(ctx.json({ id_token: jwt, access_token: '1234' }), ctx.status(200));
        }),
      );

      const { body } = await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
          nonce: jwtPayload.nonce,
        }),
        expectedHttpStatus: HttpStatus.OK,
      });

      await credor.reload();

      // removed deleted_at
      expect(credor.deleted_at).toBeNull();
      expect(credor.ativo).toBe(true);
      expect(body).toEqual({
        access_token: jwt,
        usuario: {
          email: jwtPayload.email,
          id_credor_externo: normalizeCreditor(creditorId),
          id_escritorio: office.id_escritorio,
          nome_escritorio: office.nome_escritorio,
        },
      });
    });

    it('should upser user and create user_indentifier when already exists and try login with oidc (user_identifier=provider_id)', async () => {
      const office = await createTestOffice();
      const creditorIdParsed = Math.random().toString();
      const email = `${uuid()}-<EMAIL>`;
      const jwtPayload = {
        id: creditorIdParsed,
        nonce: 'nonce',
        sub: uuid(),
        aud: '',
        email,
      };
      const [jwt, oidc] = await Promise.all([
        setupJwt(jwtPayload),
        createOidcProvider({
          jwks: DEFAULT_MOCKED_JWKS,
          opts: {
            office_id: office.id_escritorio,
            identifier_type: 'provider_id',
            user_info: {
              creditor_id: {
                claim: 'id',
              },
              email: {
                claim: 'email',
              },
              provider_id: {
                claim: 'sub',
              },
            },
            create_user: true,
          },
        }),
      ]);

      await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: creditorIdParsed,
        ativo: true,
        email,
      });

      const discoveryData = oidc.discovery_data as OidcDiscoveryData;

      server.use(
        rest.post(discoveryData.token_endpoint, async (_, res, ctx) => {
          return res(ctx.json({ id_token: jwt, access_token: '1234' }), ctx.status(200));
        }),
      );

      const { body } = await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
          nonce: jwtPayload.nonce,
        }),
      });

      const userIdentifier = await User_Identifier.findOne({
        where: { provider_id: jwtPayload.sub, oidc_id: oidc.id },
      });

      expect(body).toEqual({
        access_token: jwt,
        usuario: {
          email,
          id_credor_externo: creditorIdParsed,
          id_escritorio: office.id_escritorio,
          nome_escritorio: office.nome_escritorio,
        },
      });
      expect(userIdentifier).toMatchObject({
        provider_id: jwtPayload.sub,
        oidc_id: oidc.id,
      });
    });

    it('should call token_endpoint with domain replaced', async () => {
      const oidc = await createOidcProvider({
        discovery_data: {
          type: 'oidc',
          token_endpoint: 'https://token.endpoint.com/tokens',
        } as OidcDiscoveryData,
        jwks: DEFAULT_MOCKED_JWKS,
        opts: {
          identifier_type: 'email',
          user_info: {},
          replace_domains: 'new-origin.comissionamento.com',
        },
      });
      const tokenEndpointReplaced = 'https://new-origin.comissionamento.com/tokens';

      server.use(rest.post(tokenEndpointReplaced, async (_, res) => res()));

      let urlCalled = '';
      server.events.on('request:match', req => {
        urlCalled = req.url.href;
      });

      await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
        }),
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
      expect(urlCalled).toBe(tokenEndpointReplaced);
    });

    it('calls token endpoint with secret value when secret is found', async () => {
      const office = await createTestOffice();
      const tokenEndpointReplaced = 'https://new-origin.comissionamento.com/tokens';
      const clientSecret = 'TESTINHO';
      const oidc = await createOidcProvider({
        client_id: 'TESTINHO',
        secret_name: clientSecret,
        discovery_data: {
          type: 'oidc',
          authorization_endpoint: tokenEndpointReplaced,
          token_endpoint: tokenEndpointReplaced,
          jwks_uri: 'https://google.com/jwks',
        } as OidcDiscoveryData,
        jwks: DEFAULT_MOCKED_JWKS,
        opts: {
          office_id: office.id_escritorio,
          identifier_type: 'office_id+creditor_id',
          user_info: {
            creditor_id: {
              claim: 'id',
              extractor: '[^0-9.,]',
            },
            email: {
              claim: 'email',
            },
            provider_id: {
              claim: 'sub',
            },
          },
          create_user: true,
        },
      });

      const loginPayload = generateBodyMock({
        idProvider: oidc.id,
      });

      const jwtPayload = {
        id: `ID indentifier`,
        sub: 'provider_id',
        email: `${uuid()}-<EMAIL>`,
        aud: 'TESTINHO',
        iss: oidc.issuer,
        nonce: loginPayload.nonce,
      };

      const jwt = await setupJwt(jwtPayload);

      server.use(
        rest.post(tokenEndpointReplaced, async (req, res, ctx) => {
          const reqText = await req.text();
          const params = new URLSearchParams(reqText);
          expect(params.get('client_secret')).toEqual(clientSecret); // ensure we send the correct client secret
          return res(ctx.json({ id_token: jwt, access_token: '1234' }), ctx.status(200));
        }),
      );

      await authByOidc(app, {
        body: loginPayload,
        expectedHttpStatus: HttpStatus.OK,
      });
    });

    it('errors when secret cannot be fetched', async () => {
      const oidc = await createOidcProvider({ secret_name: 'error' });

      await authByOidc(app, {
        body: generateBodyMock({
          idProvider: oidc.id,
        }),
        expectedHttpStatus: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    });

    it('validates implicit login creditor creation', async () => {
      const office = await createTestOffice();

      const oidc = await createOidcProvider({
        jwks: callinkJwks,
        opts: {
          scope: 'openid email profile',
          office_id: office.id_escritorio,
          user_info: {
            name: {
              claim: 'nome',
              required: false,
            },
            email: {
              claim: 'email',
              required: false,
            },
            creditor_id: {
              claim: 'unique_name',
              extractor: '^CLK\\\\',
              source_type: 'id_token',
              required: false,
            },
            provider_id: {
              claim: 'unique_name',
              source_type: 'id_token',
              required: false,
            },
          },
          grant_type: 'id_token+token',
          create_user: true,
          jwt_validator: [
            {
              key: 'aud',
              values: [
                'f1095d5e-8f74-45cd-8e6c-d73c36fc0ada',
                'microsoft:identityserver:f1095d5e-8f74-45cd-8e6c-d73c36fc0ada',
              ],
            },
          ],
          response_mode: 'fragment',
          response_type: 'id_token+token',
          login_response_type: 'id_token',
          identifier_type: 'office_id+creditor_id',
          logout_redirection:
            'https://fso.callink.com.br/adfs/oauth2/logout?post_logout_redirect_uri=https://comissionamento.splitc.com.br/signin/callink',
        },
      });

      await implicitAuthByOidc(app, {
        body: {
          provider_id: oidc.id,
          access_token:
            'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkVNZTJBZHhMTHJnZ01sLUlGbmdVRUpfLXRHTSIsImtpZCI6IkVNZTJBZHhMTHJnZ01sLUlGbmdVRUpfLXRHTSJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FChKlNPT1BtsxirodOgsTuCyzKaxzVQUoF6dct7HqiKRBIE8DAjfPqfs51rZxBfzqMrQi0bi_LUnZ9T9T_EvwDllubXyxAiogaoao7GwDPHscKoYh8fV82gPKn1DrPHyiZ7-rl1J9Kec3V1Z6xYxOH9f1Y4DHsXQA5oV3i1S9JFDZdvFITmK4lxovL9CJRMDSP7LuZI6HsgGyXbW-vbLjraNyh1j_sgCrMQUTV1uqQ4cxz5woPUQ1dHlPqkhpRABPKRYePfAYoWkYTLetBgoJsLXdUQRPe7g7lPGrXQK2Tp9EGgXzOC219xDvHMVN46wF1i936IeBxgB4ML8-msFLw',
          id_token:
            'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkVNZTJBZHhMTHJnZ01sLUlGbmdVRUpfLXRHTSIsImtpZCI6IkVNZTJBZHhMTHJnZ01sLUlGbmdVRUpfLXRHTSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.lcJYbWq5AehfG0XMnxWlqtKMT_G8XgS4XKmne8PdOrXOPYq4-ZSB189dJZW93MXhFXMWyMTkfV6Kwwxap9sDDhB6lp0KbY0FCLgURrxi6wsthvwL8gD3qZ2BnSMylB5PVp5Mvq3mHS82tg2HGVXUYSJaroHPYO1yCkOtKlr1bcnsBBZT2Mib00YHfujWwkIkphd24JhW1LIPzIOBMlpBwXDcFL9DjqcDIvHmvR3GC2oHlO7iNHiOxnTzU_lhfg4Xlq9DHxlmRWR1oYptFALCWMZ9sKCJQrmz8CU94p83Q_MPimu9XwXZ6nKsorW_mVuSEcY3iFJKA-FnnC2uxMRFfw',
        },
      });

      const user = await Credor.findOne({
        where: {
          id_credor_externo: '47306_LETICIA',
          escritorio_id_escritorio: office.id_escritorio,
        },
      });

      expect(user).toBeDefined();

      return Promise.resolve();
    });

    describe('authentication by oidc organization', () => {
      const createOrgAndMock = async () => {
        const org = await createFakeOrganization();
        const { oidc, jwt, jwtPayload } = await generateMock(
          `${uuid()}-<EMAIL>`,
          {
            orgId: org.id,
            userInfo: {
              provider_id: {
                claim: 'sub',
              },
            },
            jwtPayload: {
              sub: uuid(),
            },
          },
        );
        if (oidc.discovery_data.type !== 'oidc') throw new Error();
        server.use(
          rest.post(oidc.discovery_data.token_endpoint, async (_, res, ctx) => {
            return res(ctx.json({ id_token: jwt }), ctx.status(200));
          }),
        );

        return { oidc, jwt, jwtPayload, org };
      };
      it('should fail when oidc provider not belongs to another organization', async () => {
        const oidc = await createOidcProvider();
        await authByOidcOrganization(app, {
          body: generateBodyMock({
            idProvider: oidc.id,
          }),
          expectedHttpStatus: HttpStatus.UNAUTHORIZED,
        });
      });

      it('should fail if it cannot extract the provider id from the token', async () => {
        const org = await createFakeOrganization();
        const { oidc, jwt, jwtPayload } = await generateMock(
          `${uuid()}-<EMAIL>`,
          { orgId: org.id },
        );
        if (oidc.discovery_data.type !== 'oidc') throw new Error();
        server.use(
          rest.post(oidc.discovery_data.token_endpoint, async (_, res, ctx) => {
            return res(ctx.json({ id_token: jwt }), ctx.status(200));
          }),
        );

        await authByOidcOrganization(app, {
          body: generateBodyMock({
            idProvider: oidc.id,
            nonce: jwtPayload.nonce,
          }),
          expectedHttpStatus: HttpStatus.UNAUTHORIZED,
        });
      });

      const setupOrganizationTest = async (
        mainOrg: Organization,
        mainOidc: Oidc_Provider,
        loggedSub: string,
      ) => {
        const [orgA, orgB] = await Promise.all([
          createFakeOrganization(),
          createFakeOrganization(),
        ]);

        const [firstMainOffice, secondMainOffice, officeOrgA, officeOrgB, officeWithoutOrg] =
          await Promise.all([
            createTestOffice('office da main org', mainOrg.id),
            createTestOffice('segundo office da main org', mainOrg.id),
            createTestOffice('office da org A', orgA.id),
            createTestOffice('office da org B', orgB.id),
            createTestOffice('office sem org'),
          ]);

        const createUser = (orgId: number) => {
          return User.create({
            org_id: orgId,
          });
        };

        const [firstMainUser, secondMainUser, firstOrgAUser, secondOrgAUser, orgBUser] =
          await Promise.all([
            createUser(mainOrg.id),
            createUser(mainOrg.id),
            createUser(orgA.id),
            createUser(orgA.id),
            createUser(orgB.id),
          ]);

        const createOrgUserIdentifier = (oidcId: number, userId: number, sub: string) => {
          return Org_User_Identifier.create({
            oidc_id: oidcId,
            user_id: userId,
            provider_id: sub,
          });
        };

        const oidcOrgA = await createOidcProvider({
          org_id: orgA.id,
          jwks: DEFAULT_MOCKED_JWKS,
        });
        const oidcOrgB = await createOidcProvider({
          org_id: orgB.id,
          jwks: DEFAULT_MOCKED_JWKS,
        });

        await Promise.all([
          createOrgUserIdentifier(mainOidc.id, firstMainUser.id, loggedSub),
          createOrgUserIdentifier(
            mainOidc.id,
            secondMainUser.id,
            'second_main_org_user_identifier',
          ),
          createOrgUserIdentifier(oidcOrgA.id, firstOrgAUser.id, 'first_org_a_user_identifier'),
          createOrgUserIdentifier(oidcOrgA.id, secondOrgAUser.id, 'second_org_a_user_identifier'),
          createOrgUserIdentifier(oidcOrgB.id, orgBUser.id, 'first_org_b_user_identifier'),
        ]);

        const createCreditor = (officeId: number, userId?: number) => {
          return Credor.create({
            escritorio_id_escritorio: officeId,
            id_credor_externo: uuid(),
            ativo: true,
            email: faker.internet.email(),
            user_id: userId,
          });
        };

        await Promise.all([
          createCreditor(firstMainOffice.id_escritorio, firstMainUser.id),
          createCreditor(secondMainOffice.id_escritorio, firstMainUser.id),
          createCreditor(secondMainOffice.id_escritorio, secondMainUser.id),
          createCreditor(officeOrgA.id_escritorio, firstOrgAUser.id),
          createCreditor(officeOrgB.id_escritorio, secondOrgAUser.id),
          createCreditor(officeOrgB.id_escritorio, orgBUser.id),
          createCreditor(officeWithoutOrg.id_escritorio),
        ]);

        const firstUserCreditors = await Credor.findAll({
          where: {
            escritorio_id_escritorio: [
              firstMainOffice.id_escritorio,
              secondMainOffice.id_escritorio,
            ],
            user_id: firstMainUser.id,
          },
          include: [Escritorio],
        });

        return {
          firstUserCreditors,
          firstMainOffice,
          secondMainOffice,
        };
      };

      it('should success, if not has another office belongs to the organization', async () => {
        const { org, oidc, jwtPayload } = await createOrgAndMock();

        await authByOidcOrganization(app, {
          body: generateBodyMock({
            idProvider: oidc.id,
            nonce: jwtPayload.nonce,
          }),
        });

        const orgUsers = await Org_User_Identifier.scope({
          method: ['withUser', org.id],
        }).findAll({
          where: {
            oidc_id: oidc.id,
          },
        });
        expect(orgUsers).toHaveLength(1);
      });

      it('should success auth, returning jwt and creditors ownered by the logged user', async () => {
        // create 3 orgs (mainOrg, orgA, orgB) > create 5 offices (2 mainOrg, 1 orgA, 1 orgB, 1 noOrg) > create 5 users (2 mainOrg, 1 orgA, 1 orgB, 1 noOrg) > create 3 oidc (1 mainOrg, 1 orgA, 1 orgB) > create 5 org user identifier (3 mainOrg (2 userA + 1 userB), 1 orgA, 1 orgB) > create 7 creditors (3 mainOrg, 1 orgA, 2 orgB, 1 noOrg)
        const { org, oidc, jwtPayload, jwt } = await createOrgAndMock();
        const { firstUserCreditors } = await setupOrganizationTest(org, oidc, jwtPayload.sub);
        const response = await authByOidcOrganization(app, {
          body: generateBodyMock({
            idProvider: oidc.id,
            nonce: jwtPayload.nonce,
          }),
        });

        expect(response.body).toEqual({
          access_token: jwt,
        });
      });
    });
  });

  describe('refresh token', () => {
    const makeSut = async ({ refreshToken = 'any_refresh_token', activeUser = false } = {}) => {
      const iss = `${uuid()}-issuer`;
      const email = `${uuid()}-<EMAIL>`;
      const office = await createTestOffice();

      const [jwt, encryptedRefreshToken, oidc] = await Promise.all([
        setupJwt({ iss, email }),
        CryptoUtils.encrypt('AesKeyContains16', refreshToken),
        createOidcProvider({ issuer: iss }),
        Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'id-a1',
          email,
          ativo: activeUser,
        }),
      ]);

      return { jwt, refresh_token: encryptedRefreshToken, oidc };
    };

    it('returns 401 when not has auth data cookies', async () => {
      await refreshToken(app, {
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns 401 when user is not active', async () => {
      const { refresh_token, jwt } = await makeSut();
      await refreshToken(app, {
        authorization: `Bearer ${jwt}`,
        refreshToken: refresh_token,
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns 401 when oidc has no token endpoint', async () => {
      const { oidc, refresh_token, jwt } = await makeSut({ activeUser: true });
      await oidc.update({
        discovery_data: {},
      });

      await refreshToken(app, {
        authorization: `Bearer ${jwt}`,
        refreshToken: refresh_token,
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('returns 401 when fetch to oidc /token fails', async () => {
      const { oidc, refresh_token, jwt } = await makeSut({ activeUser: true });
      if (oidc.discovery_data.type !== 'oidc') throw new Error();

      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (_, res, ctx) => {
          return res(ctx.json({}), ctx.status(401));
        }),
      );

      await refreshToken(app, {
        authorization: `Bearer ${jwt}`,
        refreshToken: refresh_token,
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });
    });

    it('should return correct new access token when fetch token oidc is successfully', async () => {
      const { oidc, refresh_token, jwt } = await makeSut({ activeUser: true });
      if (oidc.discovery_data.type !== 'oidc') throw new Error();

      const newJwt = 'new_jwt';
      server.use(
        rest.post(oidc.discovery_data.token_endpoint, async (_, res, ctx) => {
          return res(ctx.json({ id_token: newJwt }), ctx.status(200));
        }),
      );

      const { body } = await refreshToken(app, {
        authorization: `Bearer ${jwt}`,
        refreshToken: refresh_token,
      });
      expect(body).toEqual({
        access_token: newJwt,
      });
    });
  });

  describe('sso', () => {
    const createDirecionalBoraVenderSSOConfig = async (officeId: number) => {
      const cfg: DirecionalBoraVenderSSOConfig & {
        config: {
          identifier_type?: SSOIdentifierType;
        };
      } = {
        type: 'direcional-bora-vender',
        config: {
          endpoint: faker.internet.url(),
          client_id: faker.string.uuid(),
          identifier_type: SSOIdentifierType.OFFICEID_CREDITORID,
        },
      };

      await AtributosEscritorio.create({
        escritorio_id_escritorio: officeId,
        atributo: UsersClaims.OFFICE_SSO_CONFIG,
        valor: JSON.stringify(cfg),
      });

      return cfg;
    };

    const setupDirecionalEndpointResponse = (url: string, resBody: unknown, httpStatus: number) => {
      return server.use(
        rest.get(url, (req, res, ctx) => {
          return res(ctx.json(resBody), ctx.status(httpStatus));
        }),
      );
    };

    it("return 401 if the office doesn't exist", async () => {
      await ssoEndpoint(
        app,
        {
          body: { office_token: 'unknow_client_id', external_jwt: 'any_jwt' },
          additional_data: 'any_additional_data',
        },
        HttpStatus.UNAUTHORIZED,
      );
    });

    it("return 401 if the office doesn't have the sso claims", async () => {
      const office = await createTestOffice();

      await ssoEndpoint(
        app,
        {
          body: { office_token: office.client_id, external_jwt: 'any_jwt' },
          additional_data: 'any_additional_data',
        },
        HttpStatus.UNAUTHORIZED,
      );
    });

    it('should update user name if credor already created', async () => {
      const office = await createTestOffice();
      const jwtIssuer = 'direcional-bora-vender-issuer';
      const email = `user.email.${uuid()}@gmail.com`;

      const config = await createDirecionalBoraVenderSSOConfig(office.id_escritorio);
      const userJwt = jwt.sign(
        {
          exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
          iss: jwtIssuer,
        },
        '1111111111111111',
      );

      const internalId = faker.number.int().toString();
      const firstInternalName = faker.person.firstName();

      setupDirecionalEndpointResponse(
        config.config.endpoint,
        { email, cpf_cnpj: internalId, nome: firstInternalName },
        200,
      );

      await ssoEndpoint(
        app,
        {
          body: { office_token: office.client_id, external_jwt: userJwt },
          additional_data: 'SOME RANDOM NON JSON STRING',
        },
        HttpStatus.CREATED,
      );

      const user = await Credor.findOne({ where: { id_credor_externo: internalId } });

      // should update name
      expect(user.nome_credor).toBe(firstInternalName);

      const secondInternalName = faker.person.firstName();
      setupDirecionalEndpointResponse(
        config.config.endpoint,
        { email, cpf_cnpj: internalId, nome: secondInternalName },
        200,
      );

      await ssoEndpoint(
        app,
        {
          body: { office_token: office.client_id, external_jwt: userJwt },
          additional_data: 'SOME RANDOM NON JSON STRING',
        },
        HttpStatus.CREATED,
      );

      await user.reload();

      // should update name
      expect(user.nome_credor).toBe(secondInternalName);
    });

    describe('teddy', () => {
      const createTeddySsoConfig = async (
        officeId: number,
        issuers?: string[],
        domain_whitelist?: string[],
      ) => {
        const cfg: TeddySSOConfig = {
          type: 'teddy',
          config: {
            bearer: 'aaaq',
            endpoint: 'api.teddy360.com',
            issuer: issuers ?? [],
            domain_whitelist: domain_whitelist ?? [],
          },
        };

        await AtributosEscritorio.create({
          escritorio_id_escritorio: officeId,
          atributo: UsersClaims.OFFICE_SSO_CONFIG,
          valor: JSON.stringify(cfg),
        });
        return cfg;
      };

      const setupTeddyResponse = (url: string, resBody: unknown, httpStatus: number) => {
        return server.use(
          rest.post(url, (req, res, ctx) => {
            return res(ctx.json(resBody), ctx.status(httpStatus));
          }),
        );
      };

      it('stores logo url and theming if returned after successful teddy login', async () => {
        const office = await createTestOffice();
        const jwtIssuer = 'splitc';

        const config = await createTeddySsoConfig(office.id_escritorio, [jwtIssuer]);
        const userJwt = jwt.sign(
          {
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
            iss: jwtIssuer,
            'cognito:username': '<EMAIL>',
            email: '<EMAIL>',
          },
          '1111111111111111',
        );

        const themeToStore = {
          palette: { primary: { main: '#efefef' }, secondary: { main: '#efefef' }, type: 'light' },
        };
        const customLogoUrl = 'https://public.splitc.com.br/some-logo.png';
        setupTeddyResponse(
          `https://${config.config.endpoint}/info-user`,
          {
            usuario: 'nome',
            bitrix_id: '1111',
            whitelabel: {
              theme: themeToStore,
              logo_url: customLogoUrl,
            },
          },
          200,
        );

        await ssoEndpoint(
          app,
          { body: { office_token: office.client_id, external_jwt: userJwt }, additional_data: '' },
          HttpStatus.CREATED,
        );

        const createdUser = await Credor.findOne({
          where: { escritorio_id_escritorio: office.id_escritorio },
        });

        const [logoUrlClaim, themeConfig] = await Promise.all([
          Atributos_credor.findOne({
            where: {
              credor_id_credor: createdUser.id_credor,
              atributo: UsersClaims.OFFICE_LOGO_URL,
            },
          }),

          Atributos_credor.findOne({
            where: {
              credor_id_credor: createdUser.id_credor,
              atributo: UsersClaims.THEME_CONFIG,
            },
          }),
        ]);

        expect(logoUrlClaim).not.toBeNull();
        expect(themeConfig).not.toBeNull();
        expect(logoUrlClaim.valor).toEqual(customLogoUrl);
        expect(themeConfig.valor).toEqual(JSON.stringify(themeToStore));

        return Promise.resolve();
      });

      it('errors if additional_data sends alternate url that is not on whitelist', async () => {
        const office = await createTestOffice();
        const jwtIssuer = 'splitc';

        const config = await createTeddySsoConfig(
          office.id_escritorio,
          [jwtIssuer],
          ['bancoalpha.teddy360.com.br'],
        );

        const userJwt = jwt.sign(
          {
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
            iss: jwtIssuer,
            'cognito:username': '<EMAIL>',
            email: '<EMAIL>',
          },
          '1111111111111111',
        );

        const nonWhitelistedDomain = 'newcustomer.teddy360.com.br';
        const additionalDataSso = Buffer.from(
          JSON.stringify({
            override_domain: nonWhitelistedDomain,
          }),
        );

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userJwt },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userJwt },
            additional_data: additionalDataSso.toString('base64'),
          },
          HttpStatus.UNAUTHORIZED,
        );
      });

      it('calls another url if additional_data is sent on teddy sso', async () => {
        const office = await createTestOffice();
        const jwtIssuer = 'splitc';

        const whitelistedDomain = 'bancoalpha.teddy360.com.br';
        const config = await createTeddySsoConfig(
          office.id_escritorio,
          [jwtIssuer],
          [whitelistedDomain],
        );

        const userJwt = jwt.sign(
          {
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
            iss: jwtIssuer,
            'cognito:username': '<EMAIL>',
            email: `111${office.id_escritorio}@teddy360.com.br`,
          },
          '1111111111111111',
        );

        const additionalDataSso = Buffer.from(
          JSON.stringify({
            override_domain: whitelistedDomain,
          }),
        );

        setupTeddyResponse(
          `https://${whitelistedDomain}/info-user`,
          {
            usuario: 'nome',
            bitrix_id: '1111',
          },
          200,
        );

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userJwt },
            additional_data: additionalDataSso.toString('base64'),
          },
          HttpStatus.CREATED,
        );

        return Promise.resolve();
      });
    });

    describe('boa vista', () => {
      const createBoaVistaSSOConfig = async (officeId: number, issuers: string[]) => {
        const cfg: BoaVistaSSOConfig = {
          type: 'boavista',
          config: {
            endpoint: 'https://boa-vista.endpoint.com.br',
            issuer: issuers ?? [],
          },
        };

        await AtributosEscritorio.create({
          escritorio_id_escritorio: officeId,
          atributo: UsersClaims.OFFICE_SSO_CONFIG,
          valor: JSON.stringify(cfg),
        });
        return cfg;
      };

      const setupBoaVistaEndpointResponse = (url: string, resBody: unknown, httpStatus: number) => {
        return server.use(
          rest.post(url, (req, res, ctx) => {
            return res(ctx.json(resBody), ctx.status(httpStatus));
          }),
        );
      };

      it('returns 401 when sso config is invalid', async () => {
        const office = await createTestOffice();

        const cfg = {
          type: 'boavista',
          config: {},
        };

        await AtributosEscritorio.create({
          escritorio_id_escritorio: office.id_escritorio,
          atributo: UsersClaims.OFFICE_SSO_CONFIG,
          valor: JSON.stringify(cfg),
        });
        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: 'any_jwt' },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );
      });

      it('returns 401 when issuer is invalid', async () => {
        const office = await createTestOffice();

        await createBoaVistaSSOConfig(office.id_escritorio, ['unknow_issuer']);
        const userJwt = jwt.sign(
          {
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
            issuer: 'wrong_issuer',
          },
          '1111111111111111',
        );

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userJwt },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );
      });

      it('returns 401 when boa vista sso does not return email', async () => {
        const office = await createTestOffice();
        const jwtIssuer = 'boa-vista-issuer';

        const config = await createBoaVistaSSOConfig(office.id_escritorio, [jwtIssuer]);
        const userJwt = jwt.sign(
          {
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
            iss: jwtIssuer,
          },
          '1111111111111111',
        );

        setupBoaVistaEndpointResponse(config.config.endpoint, { other_data: 'buzz' }, 200);

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userJwt },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );
      });

      it('return correct user and email when login on sso boa vista is successfully', async () => {
        const office = await createTestOffice();
        const jwtIssuer = 'boa-vista-issuer';
        const email = `user.email.${uuid()}@gmail.com`;

        const config = await createBoaVistaSSOConfig(office.id_escritorio, [jwtIssuer]);
        const userJwt = jwt.sign(
          {
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
            iss: jwtIssuer,
          },
          '1111111111111111',
        );

        const user = await Credor.create({
          id_credor_externo: uuid(),
          escritorio_id_escritorio: office.id_escritorio,
          email,
        });
        setupBoaVistaEndpointResponse(config.config.endpoint, { email }, 200);

        const { body } = await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userJwt },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.CREATED,
        );

        expect(body.access_token).not.toBeNull();
        expect(body.usuario.email).toBe(user.email);
        expect(body.usuario.id_credor_externo).toBe(user.id_credor_externo);
      });
    });

    describe('mont capital', () => {
      const createMontcapitalConfig = async (officeId: number) => {
        const cfg: MontCapitalSSOConfig = {
          type: 'montcapital',
          config: {
            auth_token: 'TOKENZINHO',
            endpoint: 'https://moncapital.endpoint.com.br',
            identifier_type: SSOIdentifierType.OFFICEID_CREDITORID,
          },
        };

        await AtributosEscritorio.create({
          escritorio_id_escritorio: officeId,
          atributo: UsersClaims.OFFICE_SSO_CONFIG,
          valor: JSON.stringify(cfg),
        });
        return cfg;
      };

      const setupMontCapitalEndpointResponse = (
        url: string,
        resBody: unknown,
        httpStatus: number,
      ) => {
        return server.use(
          rest.get(url, (req, res, ctx) => {
            return res(ctx.json(resBody), ctx.status(httpStatus));
          }),
        );
      };

      it('returns 401 when montcapital request fails', async () => {
        const office = await createTestOffice();

        const cfg = await createMontcapitalConfig(office.id_escritorio);
        const userCode = 'SINGLE_USE_USER_CODE';

        setupMontCapitalEndpointResponse(
          `${cfg.config.endpoint}/${userCode}`,
          { other_data: 'buzz' },
          401,
        );

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userCode },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );
      });

      it('returns 401 when montcapital endpoint return 200 but response does not contain codigo_referencia and cpf', async () => {
        const office = await createTestOffice();

        const cfg = await createMontcapitalConfig(office.id_escritorio);
        const userCode = 'SINGLE_USE_USER_CODE';

        setupMontCapitalEndpointResponse(
          `${cfg.config.endpoint}/${userCode}`,
          { email: '<EMAIL>' },
          200,
        );

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userCode },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );
      });

      it('creates user with reference code data and not fill email', async () => {
        const office = await createTestOffice();
        const email = `user.email.${uuid()}@gmail.com`;

        const config = await createMontcapitalConfig(office.id_escritorio);
        const userCode = 'SINGLE_USE_USER_CODE';

        const montCapitalCreditorId = uuid();
        const montCapitalCpf = '12345678901';
        const montCapitalCreditorName = 'goku';
        setupMontCapitalEndpointResponse(
          `${config.config.endpoint}/${userCode}`,
          {
            email,
            codigo_referencia: montCapitalCreditorId,
            cpf: montCapitalCpf,
            nome: montCapitalCreditorName,
          },
          200,
        );

        const { body } = await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userCode },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.CREATED,
        );

        expect(body.access_token).not.toBeNull();

        const internalId = montCapitalCreditorId;
        const createdCreditor = await Credor.findOne({
          where: {
            escritorio_id_escritorio: office.id_escritorio,
            id_credor_externo: internalId,
          },
        });

        expect(createdCreditor.nome_credor).toBe(montCapitalCreditorName);
        expect(createdCreditor.email).toBe(null);
      });

      it('should throw 401 when try loggin with reserved backoffice email', async () => {
        const office = await createTestOffice();
        const backofficeEmail = backofficeList[0];

        const config = await createMontcapitalConfig(office.id_escritorio);
        const userCode = 'SINGLE_USE_USER_CODE';

        const montCapitalCreditorId = uuid();
        const montCapitalCpf = '12345678901';
        const montCapitalCreditorName = 'goku';
        setupMontCapitalEndpointResponse(
          `${config.config.endpoint}/${userCode}`,
          {
            email: backofficeEmail,
            codigo_referencia: montCapitalCreditorId,
            cpf: montCapitalCpf,
            nome: montCapitalCreditorName,
          },
          200,
        );

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userCode },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );
      });

      it('should throw 401 when try loggin with backoffice email pattern', async () => {
        const office = await createTestOffice();
        const backofficeCreditorId = uuid();
        const backofficeEmail = '<EMAIL>';

        const backofficeUser = await Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: backofficeCreditorId,
          ativo: true,
          email: backofficeEmail,
        });
        await Atributos_credor.create({
          credor_id_credor: backofficeUser.id_credor,
          atributo: UsersClaims.BACKOFFICE,
          valor: true,
        });

        const config = await createMontcapitalConfig(office.id_escritorio);
        const userCode = 'SINGLE_USE_USER_CODE';

        const montCapitalCpf = '12345678901';
        const montCapitalCreditorName = 'goku';
        setupMontCapitalEndpointResponse(
          `${config.config.endpoint}/${userCode}`,
          {
            email: backofficeEmail,
            codigo_referencia: backofficeCreditorId,
            cpf: montCapitalCpf,
            nome: montCapitalCreditorName,
          },
          200,
        );

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userCode },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );
      });
    });

    describe('direcional-bora-vender', () => {
      it('should return 401 when sso config is invalid', async () => {
        const office = await createTestOffice();

        const cfg = {
          type: 'direcional-bora-vender',
          config: {},
        };

        await AtributosEscritorio.create({
          escritorio_id_escritorio: office.id_escritorio,
          atributo: UsersClaims.OFFICE_SSO_CONFIG,
          valor: JSON.stringify(cfg),
        });

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: 'any_jwt' },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );
      });

      it('should return 401 when direcional-bora-vender sso does not return internalId', async () => {
        const office = await createTestOffice();
        const jwtIssuer = 'direcional-bora-vender-issuer';

        const config = await createDirecionalBoraVenderSSOConfig(office.id_escritorio);
        const userJwt = jwt.sign(
          {
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
            iss: jwtIssuer,
          },
          '1111111111111111',
        );

        // no email and no internalId
        setupDirecionalEndpointResponse(config.config.endpoint, { other_data: 'buzz' }, 200);

        await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userJwt },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.UNAUTHORIZED,
        );
      });

      it('return correct user when login on sso direcional-bora-vender is successfully ', async () => {
        const office = await createTestOffice();
        const jwtIssuer = 'direcional-bora-vender-issuer';
        const email = `user.email.${uuid()}@gmail.com`;

        const config = await createDirecionalBoraVenderSSOConfig(office.id_escritorio);
        const userJwt = jwt.sign(
          {
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
            iss: jwtIssuer,
          },
          '1111111111111111',
        );

        const internalId = faker.number.int().toString();

        const user = await Credor.create({
          email,
          id_credor_externo: internalId,
          escritorio_id_escritorio: office.id_escritorio,
        });

        setupDirecionalEndpointResponse(
          config.config.endpoint,
          { email, cpf_cnpj: internalId },
          200,
        );

        const { body } = await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userJwt },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.CREATED,
        );

        expect(body.access_token).not.toBeNull();
        expect(body.usuario.id_credor_externo).toBe(user.id_credor_externo);
      });

      it('should create user if it does not exist and return the correct user', async () => {
        const office = await createTestOffice();
        const jwtIssuer = 'direcional-bora-vender-issuer';
        const email = `user.email.${uuid()}@gmail.com`;

        const config = await createDirecionalBoraVenderSSOConfig(office.id_escritorio);
        const userJwt = jwt.sign(
          {
            exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 10,
            iss: jwtIssuer,
          },
          '1111111111111111',
        );

        const internalId = faker.number.int().toString();
        const internalName = faker.person.firstName();

        setupDirecionalEndpointResponse(
          config.config.endpoint,
          { email, cpf_cnpj: internalId, nome: internalName },
          200,
        );

        const { body } = await ssoEndpoint(
          app,
          {
            body: { office_token: office.client_id, external_jwt: userJwt },
            additional_data: 'SOME RANDOM NON JSON STRING',
          },
          HttpStatus.CREATED,
        );

        const user = await Credor.findOne({ where: { id_credor_externo: internalId } });

        expect(user.id_credor_externo).toBe(internalId);
        expect(user.escritorio_id_escritorio).toBe(office.id_escritorio);
        expect(user.nome_credor).toBe(internalName);

        expect(body.access_token).not.toBeNull();
        expect(body.usuario.id_credor_externo).toBe(user.id_credor_externo);
      });
    });
  });

  afterAll(async () => {
    server.close();
    await app.close();
  });
});
