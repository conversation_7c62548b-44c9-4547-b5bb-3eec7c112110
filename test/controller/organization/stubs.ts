import { Organization } from '@app/models/organization';
import { RoleConfig, RoleMatchType } from '@app/types/models/organization';
import { createTestOffice } from '../../utils/massa.utils';

export const roleConfigForJohnsonsCases = [
  {
    role: 'Master',
    admin_only: true,
    create_user: true,
  },
  {
    role: 'Cluster Admin LAS and LAN',
    admin_only: false,
    allowed_tenants_matcher: '(LAS|LAN)($|\\s)',
    create_user: true,
    user_info_matches: [
      {
        claim: 'email',
        value: ['<EMAIL>'],
      },
    ],
  },
  {
    role: 'Cluster Admin LAS',
    admin_only: false,
    allowed_tenants_matcher: 'LAS($|\\s)',
    create_user: true,
    user_info_matches: [
      {
        claim: 'email',
        value: ['<EMAIL>', '<EMAIL>'],
      },
    ],
  },
  {
    role: 'Cluster Admin LAN',
    admin_only: false,
    allowed_tenants_matcher: 'LAN($|\\s)',
    create_user: true,
    user_info_matches: [
      {
        claim: 'email',
        value: ['<EMAIL>', '<EMAIL>'],
      },
    ],
  },
  {
    role: 'Cluster Admin BRA',
    admin_only: false,
    allowed_tenants_matcher: 'BRA($|\\s)',
    create_user: true,
    user_info_matches: [
      {
        claim: 'email',
        value: ['<EMAIL>'],
      },
    ],
  },
  {
    role: 'Regular Users',
    description:
      "Regular users can only see production tenants (those that dont have the '- Staging' string)",
    admin_only: false,
    create_user: false,
    allowed_tenants_matcher: '^(?!.*staging).*$',
    user_info_matches: [
      {
        claim: 'provider_id',
        value: ['*'],
      },
    ],
  },
];
export const johnsonsCases = [
  {
    description: 'can see view all tenants in org',
    emails: ['<EMAIL>'],
    expectedTenants: [
      'Johnson BRA',
      'Johnson LAN',
      'Johnson LAS',
      'Johnson BRA - Staging',
      'Johnson LAN - Staging',
      'Johnson LAS - Staging',
    ],
    isAdmin: true,
  },
  {
    description: 'can see view only LAS and LAN tenants',
    emails: ['<EMAIL>'],
    expectedTenants: [
      'Johnson LAS',
      'Johnson LAN',
      'Johnson LAS - Staging',
      'Johnson LAN - Staging',
    ],
    isAdmin: false,
  },
  {
    description: 'can see view only LAS',
    emails: ['<EMAIL>', '<EMAIL>'],
    expectedTenants: ['Johnson LAS', 'Johnson LAS - Staging'],
    isAdmin: false,
  },
  {
    description: 'can see view only LAN',
    emails: ['<EMAIL>', '<EMAIL>'],
    expectedTenants: ['Johnson LAN', 'Johnson LAN - Staging'],
    isAdmin: false,
  },
  {
    description: 'can see view only BRA',
    emails: ['<EMAIL>'],
    expectedTenants: ['Johnson BRA', 'Johnson BRA - Staging'],
    isAdmin: false,
  },
  {
    description: 'dont can see view any tenants, because no have already creditor',
    emails: ['<EMAIL>'],
    expectedTenants: [],
    isAdmin: false,
  },
  {
    description: 'can see view only all production tenants',
    emails: ['<EMAIL>'],
    expectedTenants: ['Johnson LAS', 'Johnson BRA'],
    isAdmin: false,
    shouldCreateCreditorsOn: ['Johnson LAS', 'Johnson BRA'],
  },
];

export const rolesCases = [
  {
    description: 'admin user',
    isAdmin: true,
    expectedOffices: 3,
  },
  {
    description: 'non-admin',
    isAdmin: false,
    expectedOffices: 1,
  },
];

export const fillRolesConfigForTests = async (
  org: Organization,
  opts?: { rolesConfig?: RoleConfig[]; createUser?: boolean },
) => {
  org.config = {
    role_match_type: RoleMatchType.FIRST_MATCH,
    roles_config: opts?.rolesConfig ?? [
      {
        role: 'Manager',
        create_user: opts?.createUser ?? false,
        user_info_matches: [
          {
            claim: 'creditor_id',
            value: ['A2'],
          },
        ],
        allowed_tenants_matcher: '(?<!Sub\\s)Manager|^Manager$',
      },
      {
        role: 'Sub Manager',
        create_user: opts?.createUser ?? false,
        user_info_matches: [
          {
            claim: 'creditor_id',
            value: ['A1'],
          },
        ],
        allowed_tenants_matcher: '^Sub Manager( \\d+)?$',
      },
    ],
  };
  await org.save();
};

export const makeTenantsForTests = async (orgId: number) => {
  const managerTenants = ['Manager', 'Manager 2', 'Manager 3'];
  const subManagerTenants = ['Sub Manager', 'Sub Manager 2', 'Sub Manager 3'];
  const tenantsToCreate = [
    ...managerTenants,
    'First Office belonging Org',
    'Sem match em nenhum',
    'Sub sem match',
    ...subManagerTenants,
  ];
  await Promise.all(tenantsToCreate.map(tenantName => createTestOffice(tenantName, orgId)));
  return { managerTenants, subManagerTenants, allTenants: tenantsToCreate };
};
