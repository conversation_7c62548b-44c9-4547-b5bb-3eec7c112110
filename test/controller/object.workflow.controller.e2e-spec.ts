import { vi } from 'vitest';
import { SECRET_VAULT } from '../../src/services/google/secret-manager.service';
import { OutboundWebhookService } from '../../src/services/webhooks/outbound.webhook.service';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import { AppModule } from '../../src/app.module';
import { ConfigurationEnv } from '../../src/config/configuration.env';
import { GoogleGcsService } from '../../src/services/google/gcs.service';
import { SheetDbApiService } from '../../src/services/sheet.db.api.service';
import ZombieApiService from '../../src/services/zombie.api.service';
import {
  createTestOffice,
  DEFAULT_GCP_CREDENTIALS,
  makeDefaultPayrollData,
  makePayroll,
  makePayrollConfig,
  mockPaymentRequestPayroll,
  uploadFiles,
} from '../utils/massa.utils';
import {
  createPayrollRequest,
  createPermissionProfile,
  getObjectWorkflow,
  getObjectWorkflowContext,
  getObjectWorkflowHistory,
  processPayroll,
  updateWorkflowStatus,
  updatePermissions,
  updateWorkflowInputs,
  getWorkflowStatusInfo,
  deleteDocuments,
} from '../utils/common.requests';
import { Object_Workflow } from '../../src/models/object_workflow';
import { buildPaymentRequestWorkflow, mockPayrollDataAndProcess } from './payroll/utils';
import {
  googleGcsServiceMock,
  sheetDbApiServiceMock,
  zombieServiceMock,
} from './payroll/utils/mocks';
import { Object_Workflow_Events } from '@app/models/object_workflow_events';
import {
  BackendErrorCodes,
  NodeDefinition,
  OBJECT_WORKFLOW_KEY_TAG,
  PaymentRequestStatus,
  PayrollStatus,
  ResetConfigActionType,
  StatusSerializedGraph,
  UpdateWorkflowInputs,
  UsersClaims,
  ObjectWorkflowEventsReasons,
  StatusUpdateErrorReasons,
  GetCurrentPayrollResponse,
  BackendErrors,
  PayrollHookEventsType,
  HookNodeDefinition,
  ObjectWorkflowType,
  DocumentSource,
  StatusUpdateErrorTypes,
  GetCurrentPayrollDocumentFilters,
  createUniquePayrollDataKey,
} from 'shared-types';
import { Payroll_Data } from '@app/models/payroll_data';
import { StatusGraph } from '@app/domain/payroll/status_graph/status.graph';
import { Payroll_Config } from '@app/models/payroll_config';
import {
  createCreditors,
  setupMockLoginDomain,
  simulateLoggedUserBuilder,
  SimulateLoggedUserData,
} from '../mocks/login.domain.mock';
import { Credor } from '@app/models/credor';
import uuid from 'uuid';
import { JSONSchema7 } from 'json-schema';
import { PaymentRequestStatusGraph } from '@app/domain/payroll/payment_request_graph/payment.request.status.graph';
import { Escritorio } from '@app/models/escritorio';
import { Document, DocumentCreationAttributes } from '@app/models/document';
import { Document_tag } from '@app/models/document_tag';
import { Creditor_Data } from '@app/models/creditor_data';
import { Payment_Data_Request } from '@app/models/payment_data_request';
import { Outbound_webhook_config } from '@outboundWb/models/outbound_webhook_config';
import { Payroll } from '@app/models/payroll';
import { formatDateToString, formatISO } from '@app/utils/date.utils';
import AWSService from '@app/services/aws.sdk.service';
import { Atributos_credor } from '@app/models/atributos_credor';
import { AwsSesFacade } from '@app/services/aws.ses.facade';
import { sesFacadeMock } from '../stubs/aws.ses.facade';
import { createValidationResult } from './ocr/utils/utils';
import { ObjectWorkflowDomain } from '@app/object.workflow/domain/object.workflow.domain';
import { File, StorageType } from '@app/models/file';

process.env.BATCH_STATUS_UPDATE_SIZE = '1';
process.env.BATCH_STATUS_MS_DELAY = '1';
vi.mock('starkbank');

const DOCUMENT_UPLOAD_STATUS = PaymentRequestStatus.PAID; // is a final node
const DOCUMENT_DELETED_STATUS = PaymentRequestStatus.WAITING_FOR_INVOICE;
const INPUT_FAILURE_STATUS = PaymentRequestStatus.WAITING_FOR_INVOICE;
const INPUT_SUCCESS_STATUS = PaymentRequestStatus.PAID;

const payrollAdminClaims = [
  { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
  { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
  { atributo: UsersClaims.OFFICE_CREDITORS_VIEW, valor: 'true' },
];

const validateWorkflowObjectStatus = async (
  payrollsDataId: number[] | number,
  expectedStatus: string,
) => {
  const allPayrollData = await Payroll_Data.scope('withWorkflow').findAll({
    where: {
      id: payrollsDataId,
    },
  });

  allPayrollData.forEach(({ workflow_object }) => {
    expect(workflow_object.status).toBe(expectedStatus);
  });

  return allPayrollData;
};

const outboundWebhookService = {
  processWebhook: vi.fn().mockResolvedValue({ success: true }),
};

const secretVaultMock = {
  getSecret: vi.fn(),
};

const config = new ConfigurationEnv();
const awsSdk = AWSService as unknown as any;

const sortByCreatedAt = (a: Object_Workflow_Events, b: Object_Workflow_Events) =>
  new Date(a.created_at).getTime() - new Date(b.created_at).getTime();

describe('Object Workflow Controller (e2e)', () => {
  let app: INestApplication;
  let mockLoadAuthContext: ReturnType<typeof setupMockLoginDomain>;

  Object.defineProperty(awsSdk, 'ses', { value: {} });
  awsSdk.ses.sendTemplatedEmail = vi.fn().mockReturnValue({
    promise: () => vi.fn().mockResolvedValue('OK'),
  });

  beforeAll(async () => {
    Object.defineProperty(config, 'tokenValidationEnabled', { value: false });
    Object.defineProperty(config, 'GCS_ROOT_FOLDER', { value: 'temp_files/' });
    Object.defineProperty(config, 'serviceAccountAuth', { value: DEFAULT_GCP_CREDENTIALS });

    const moduleBuilder = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .overrideProvider(AWSService)
      .useValue(awsSdk)
      .overrideProvider(SheetDbApiService)
      .useValue(sheetDbApiServiceMock)
      .overrideProvider(GoogleGcsService)
      .useValue(googleGcsServiceMock)
      .overrideProvider(ZombieApiService)
      .useValue(zombieServiceMock)
      .overrideProvider(OutboundWebhookService)
      .useValue(outboundWebhookService)
      .overrideProvider(SECRET_VAULT)
      .useValue(secretVaultMock)
      .overrideProvider(AwsSesFacade)
      .useValue(sesFacadeMock);

    mockLoadAuthContext = setupMockLoginDomain(moduleBuilder);
    const module = await moduleBuilder.compile();

    module.useLogger(module.get(Logger));

    app = module.createNestApplication({ bodyParser: false });

    await app.init();
  });

  const simulateLoggedUser = (params: SimulateLoggedUserData) => {
    return simulateLoggedUserBuilder(mockLoadAuthContext)(params);
  };

  const bulkCreatePaymentRequest = async (
    office: Escritorio,
    payroll: Payroll,
    payroll_config: Payroll_Config,
    credor: Credor,
    inputs?: Record<string, unknown>,
    size: number = 10,
    status?: PaymentRequestStatus,
  ) => {
    sheetDbApiServiceMock.getPayrollDataToProcess.mockResolvedValue({
      hash: 'hash',
      hash_metadata: Array.from({ length: size }).map((_, i) => ({
        period_id: i + 1,
        last_changed_at: '2024-01-02T00:00:00',
      })),
      payroll_data: Array.from({ length: size }).map((_, i) => ({
        payroll_id: payroll.id,
        amount: 120 * (i + 1),
        creditor_id: 'RODOLFO',
        grouping: 'hakuna',
        metadata: [
          {
            Empreendimento: '1',
          },
        ],
      })),
    });

    await processPayroll<GetCurrentPayrollResponse>(app, {
      officeId: office.id_escritorio,
      payout_date: payroll.payout_date,
      payload: { hashOnTheFly: 'hash' },
    });

    const paymentsObjects = Array.from({ length: size }).map((_, i) => ({
      workflow_object: {
        input_data: inputs,
        status:
          status ??
          (i % 2 === 0
            ? PaymentRequestStatus.WAITING_FOR_INVOICE
            : PaymentRequestStatus.INVOICE_ISSUED),
      },
    }));

    const payments = await buildPaymentRequestWorkflow(
      office,
      payroll_config,
      credor,
      ...paymentsObjects,
    );

    await Payment_Data_Request.bulkCreate(
      payments.map((paymentRequest, index) => ({
        payment_request_id: paymentRequest.id,
        payroll_data_key: `"${payroll.id}"."${credor.id_credor_externo}"."rei_leao"`,
        payroll_data_info: {
          payroll_id: payroll.id,
          amount: 120 * (index + 1),
          creditor_id: 'RODOLFO',
          grouping: 'hakuna',
          metadata: [
            {
              Empreendimento: '1',
            },
          ],
        } as unknown as Payroll_Data,
      })),
    );

    return payments;
  };

  const prepareLogin = async (claims = [], loggedCreditor: string | undefined = undefined) => {
    const office = await createTestOffice();

    const { creditorLogged } = await createCreditors({
      officeId: office.id_escritorio,
      creditorId: loggedCreditor,
    });

    const authenticationContextDto = simulateLoggedUser({ office, credor: creditorLogged, claims });

    return { office, credor: creditorLogged, authenticationContextDto };
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers();
  });

  afterAll(async () => {
    await app.close();
    await app.get('SEQUELIZE').close();
  });

  describe('GET /status/info', () => {
    it('should get payroll_data object workflows status info', async () => {
      const payout_date = '2022-09-01';
      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
      ]);

      const { config, payroll } = await mockPaymentRequestPayroll(
        office.id_escritorio,
        payout_date,
      );

      const processResponse = await mockPayrollDataAndProcess(
        app,
        office.id_escritorio,
        payout_date,
        [
          {
            amount: 500,
            creditor_id: credor.id_credor_externo,
            grouping: 'these',
            metadata: [
              {
                Empreendimento: '1',
              },
            ],
          },
          {
            amount: 200,
            creditor_id: credor.id_credor_externo,
            grouping: 'guys',
            metadata: [
              {
                Empreendimento: '1',
              },
            ],
          },
          {
            amount: 650,
            creditor_id: credor.id_credor_externo,
            grouping: 'xpto',
            metadata: [
              {
                Empreendimento: '1',
              },
            ],
          },
        ],
      );

      await Object_Workflow.update(
        {
          status: PaymentRequestStatus.INVOICE_SENT,
        },
        {
          where: {
            object_key: processResponse.body.payroll_data[0].object_key,
          },
        },
      );

      const graph = config.getParsedGraph();

      const { body } = await getWorkflowStatusInfo(app, {
        officeId: office.id_escritorio,
        expectedStatus: HttpStatus.OK,
        filters: {
          type: ObjectWorkflowType.PAYROLL_DATA,
          object_key_starts_with: `"${payroll.id}"`,
        },
      });

      expect(body).toStrictEqual({
        status_in_use: {
          [PaymentRequestStatus.INVOICE_SENT]: {
            count: 1,
            targets: [
              {
                code: 'PAID',
                input_schema: [],
                name: 'Pago',
              },
            ],
          },
          [graph.getInitialStatus()]: {
            count: 2,
            targets: [
              {
                code: 'INVOICE_ISSUED',
                input_schema: [],
                name: 'Nota Fiscal Emitida',
              },
            ],
          },
        },
      });
    });

    it('should get payment_request object workflows status info', async () => {
      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
      ]);

      const defaultGraph = JSON.parse(
        JSON.stringify(PaymentRequestStatusGraph.generateDefaultGraph()),
      );

      const currentGraph: StatusSerializedGraph = {
        ...defaultGraph,
        edges: [
          ...defaultGraph.edges,
          {
            key: 'geid_34_4',
            source: 'WAITING_FOR_INVOICE',
            target: 'INVOICE_SENT',
            attributes: {},
          },
        ],
      };
      const { config, payroll } = await mockPaymentRequestPayroll(
        office.id_escritorio,
        '2022-09-01',
        undefined,
        undefined,
        undefined,
        currentGraph,
      );

      const size = 10;
      const payments = await bulkCreatePaymentRequest(
        office,
        payroll,
        config,
        credor,
        undefined,
        size,
        PaymentRequestStatus.WAITING_FOR_INVOICE,
      );

      const start_creation_date = payments[0].created_at.toISOString();
      const currentDate = new Date();
      currentDate.setDate(currentDate.getDate() + 1);

      const end_creation_date = currentDate.toISOString();

      const response = await getWorkflowStatusInfo(app, {
        officeId: office.id_escritorio,
        expectedStatus: HttpStatus.OK,
        filters: {
          type: ObjectWorkflowType.PAYMENT_REQUEST,
          created_from: start_creation_date,
          created_to: end_creation_date,
        },
      });

      expect(response.body).toEqual({
        status_in_use: {
          [PaymentRequestStatus.WAITING_FOR_INVOICE]: {
            count: size,
            targets: [
              {
                code: 'INVOICE_ISSUED',
                input_schema: [],
                name: 'Nota Fiscal Emitida',
              },
              {
                code: 'INVOICE_SENT',
                input_schema: [],
                name: 'Nota Fiscal Enviada',
              },
            ],
          },
        },
      });
    });

    it('should return only creditors that can see view', async () => {
      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
      ]);

      const [canSeeCreditor, notCanSeeCreditor] = await Promise.all([
        Credor.create({
          id_credor_externo: 'credor_can_see',
          nome_credor: 'credor_can_see',
          escritorio_id_escritorio: office.id_escritorio,
          ativo: true,
        }),
        Credor.create({
          id_credor_externo: 'credor_cant_see',
          nome_credor: 'credor_cant_see',
          escritorio_id_escritorio: office.id_escritorio,
          ativo: true,
        }),
      ]);

      simulateLoggedUser({
        claims: [
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
          {
            atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
            valor: JSON.stringify([canSeeCreditor.id_credor_externo]),
          },
        ],
        office,
        credor,
      });

      const payout_date = '2022-09-01';
      const { payroll } = await mockPaymentRequestPayroll(office.id_escritorio, payout_date);

      await mockPayrollDataAndProcess(app, office.id_escritorio, payout_date, [
        {
          amount: 500,
          creditor_id: credor.id_credor_externo,
          grouping: 'these',
          metadata: [
            {
              Empreendimento: '1',
            },
          ],
        },
        {
          amount: 200,
          creditor_id: credor.id_credor_externo,
          grouping: 'guys',
          metadata: [
            {
              Empreendimento: '1',
            },
          ],
        },
        {
          amount: 200,
          creditor_id: canSeeCreditor.id_credor_externo,
          grouping: 'guys',
          metadata: [
            {
              Empreendimento: '1',
            },
          ],
        },
        {
          amount: 650,
          creditor_id: notCanSeeCreditor.id_credor_externo,
          grouping: 'xpto',
          metadata: [
            {
              Empreendimento: '1',
            },
          ],
        },
      ]);

      const getStatusInfo = (creditors?: string[]) =>
        getWorkflowStatusInfo(app, {
          officeId: office.id_escritorio,
          expectedStatus: HttpStatus.OK,
          filters: {
            type: ObjectWorkflowType.PAYROLL_DATA,
            creditors,
            object_key_starts_with: `"${payroll.id}"`,
          },
        });

      let response = await getStatusInfo();
      expect(response.body).toEqual({
        status_in_use: {
          [PaymentRequestStatus.WAITING_FOR_INVOICE]: {
            count: 3,
            targets: [
              {
                code: 'INVOICE_ISSUED',
                input_schema: [],
                name: 'Nota Fiscal Emitida',
              },
            ],
          },
        },
      });

      response = await getStatusInfo([notCanSeeCreditor.id_credor_externo]); // should return empty, because this creditor is not allowed to see
      expect(response.body).toEqual({
        status_in_use: {},
      });

      response = await getStatusInfo([
        canSeeCreditor.id_credor_externo,
        notCanSeeCreditor.id_credor_externo,
      ]);
      expect(response.body).toEqual({
        status_in_use: {
          [PaymentRequestStatus.WAITING_FOR_INVOICE]: {
            count: 1,
            targets: [
              {
                code: 'INVOICE_ISSUED',
                input_schema: [],
                name: 'Nota Fiscal Emitida',
              },
            ],
          },
        },
      });
    });

    it('should return hook_inputs from each status target', async () => {
      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
      ]);

      const defaultGraph = JSON.parse(
        JSON.stringify(PaymentRequestStatusGraph.generateDefaultGraph()),
      );

      const inputSchema = {
        type: 'object',
        required: ['message'],
        properties: {
          message: {
            type: 'string',
            title: 'Message',
            default: 'Mensagem de notificação padrão',
          },
          subtitle: {
            type: 'string',
            title: 'Subtitle',
            default: 'Subtitulo',
          },
        },
      };

      const currentGraph: StatusSerializedGraph = {
        ...defaultGraph,
        edges: [
          ...defaultGraph.edges,
          {
            key: 'geid_34_3',
            source: 'WAITING_FOR_INVOICE',
            target: 'INVOICE_SENT',
            attributes: {
              hooks: [
                {
                  type: 'notify_users',
                  input_schema: inputSchema,
                },
              ],
            },
          },
        ],
      };
      const { config, payroll } = await mockPaymentRequestPayroll(
        office.id_escritorio,
        '2022-09-01',
        undefined,
        undefined,
        undefined,
        currentGraph,
      );

      const payments = await bulkCreatePaymentRequest(
        office,
        payroll,
        config,
        credor,
        undefined,
        1,
        PaymentRequestStatus.WAITING_FOR_INVOICE,
      );

      const response = await getWorkflowStatusInfo(app, {
        officeId: office.id_escritorio,
        expectedStatus: HttpStatus.OK,
        filters: {
          type: ObjectWorkflowType.PAYMENT_REQUEST,
          object_key_starts_with: payments[0].request_key,
        },
      });

      expect(response.body).toEqual({
        status_in_use: {
          [PaymentRequestStatus.WAITING_FOR_INVOICE]: {
            count: 1,
            targets: [
              {
                code: 'INVOICE_ISSUED',
                input_schema: [],
                name: 'Nota Fiscal Emitida',
              },
              {
                code: 'INVOICE_SENT',
                input_schema: [{ schema: inputSchema, type: 'notify_users' }],
                name: 'Nota Fiscal Enviada',
              },
            ],
          },
        },
      });
    });

    it('should get correctly object workflows when try filter by model GROUPING or SEARCH', async () => {
      const payout_date = '2022-09-01';
      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
      ]);

      const { config, payroll } = await mockPaymentRequestPayroll(
        office.id_escritorio,
        payout_date,
      );

      const processResponse = await mockPayrollDataAndProcess(
        app,
        office.id_escritorio,
        payout_date,
        [
          {
            amount: 500,
            creditor_id: credor.id_credor_externo,
            grouping: 'grouping-1',
            metadata: [
              {
                Empreendimento: '1',
              },
            ],
          },
          {
            amount: 500,
            creditor_id: credor.id_credor_externo,
            grouping: 'grouping-2',
            metadata: [
              {
                Empreendimento: '1',
              },
            ],
          },
          {
            amount: 200,
            creditor_id: credor.id_credor_externo,
            grouping: 'grouping-3',
            metadata: [
              {
                Empreendimento: '1',
              },
            ],
          },
          {
            amount: 650,
            creditor_id: credor.id_credor_externo,
            grouping: 'xpto',
            metadata: [
              {
                Empreendimento: '1',
              },
            ],
          },
        ],
      );

      await Object_Workflow.update(
        {
          status: PaymentRequestStatus.INVOICE_SENT,
          external_data: {
            xpto: 'xyz',
          },
        },
        {
          where: {
            object_key: processResponse.body.payroll_data[0].object_key,
          },
        },
      );

      const graph = config.getParsedGraph();

      let res = await getWorkflowStatusInfo(app, {
        officeId: office.id_escritorio,
        expectedStatus: HttpStatus.OK,
        filters: {
          type: ObjectWorkflowType.PAYROLL_DATA,
          object_key_starts_with: `"${payroll.id}"`,
          groupings: ['grouping-1', 'grouping-2'],
        },
      });

      expect(res.body).toStrictEqual({
        status_in_use: {
          [PaymentRequestStatus.INVOICE_SENT]: {
            count: 1,
            targets: [
              {
                code: 'PAID',
                input_schema: [],
                name: 'Pago',
              },
            ],
          },
          [graph.getInitialStatus()]: {
            count: 1,
            targets: [
              {
                code: 'INVOICE_ISSUED',
                input_schema: [],
                name: 'Nota Fiscal Emitida',
              },
            ],
          },
        },
      });

      res = await getWorkflowStatusInfo(app, {
        officeId: office.id_escritorio,
        expectedStatus: HttpStatus.OK,
        filters: {
          type: ObjectWorkflowType.PAYROLL_DATA,
          object_key_starts_with: `"${payroll.id}"`,
          search: 'xyz',
        },
      });

      expect(res.body).toStrictEqual({
        status_in_use: {
          [PaymentRequestStatus.INVOICE_SENT]: {
            count: 1,
            targets: [
              {
                code: 'PAID',
                input_schema: [],
                name: 'Pago',
              },
            ],
          },
        },
      });

      res = await getWorkflowStatusInfo(app, {
        officeId: office.id_escritorio,
        expectedStatus: HttpStatus.OK,
        filters: {
          type: ObjectWorkflowType.PAYROLL_DATA,
          object_key_starts_with: `"${payroll.id}"`,
          search: 'found_nothing',
        },
      });

      expect(res.body).toStrictEqual({
        status_in_use: {},
      });
    });

    it('Should return correctly object workflow when try filter by DOCUMENTS', async () => {
      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        {
          atributo: UsersClaims.DOCUMENT_MANAGEMENT,
          valor: JSON.stringify({ deletion: 'anyone', upload: 'anyone', view: 'anyone' }),
        },
      ]);

      const { payroll } = await mockPaymentRequestPayroll(office.id_escritorio, '2020-01-01');

      await mockPayrollDataAndProcess(app, office.id_escritorio, '2020-01-01', [
        {
          amount: 500,
          creditor_id: credor.id_credor_externo,
          grouping: '1',
          metadata: [
            {
              Empreendimento: '1',
            },
          ],
        },
        {
          amount: 500,
          creditor_id: credor.id_credor_externo,
          grouping: '2',
          metadata: [
            {
              Empreendimento: '2',
            },
          ],
        },
        {
          amount: 500,
          creditor_id: credor.id_credor_externo,
          grouping: '3',
          metadata: [
            {
              Empreendimento: '3',
            },
          ],
        },
      ]);

      const file = await File.create({
        name: 'nomezin',
        storage_name: 'storage',
        checksum: 'asdasd',
        storage: StorageType.DRIVE,
        office_id: office.id_escritorio,
      });

      const [document, deletedDocument] = await Promise.all([
        Document.create({
          date: '2020-01-01',
          file_id: file.id,
          file: file,
          name: 'Documento de teste',
          approval: 'PENDING',
          creditor_id: credor.id_credor,
          source: DocumentSource.PAYMENT_REQUEST,
          created_by: credor.id_credor,
        }),
        Document.create({
          date: '2020-01-01',
          file_id: file.id,
          file: file,
          name: 'deleted document',
          approval: 'PENDING',
          creditor_id: credor.id_credor,
          source: DocumentSource.PAYMENT_REQUEST,
          created_by: credor.id_credor,
          deleted_at: new Date(),
        }),
      ]);

      await Promise.all([
        Document_tag.create({
          document_id: document.id,
          tag_name: OBJECT_WORKFLOW_KEY_TAG,
          tag_value: createUniquePayrollDataKey({
            creditor_id: credor.id_credor_externo,
            payroll_id: payroll.id,
            grouping: '1',
          }),
        }),
        Document_tag.create({
          document_id: deletedDocument.id, // this deleted document should not be returned when try filter by documents and should be returned when try filter by no documents
          tag_name: OBJECT_WORKFLOW_KEY_TAG,
          tag_value: createUniquePayrollDataKey({
            creditor_id: credor.id_credor_externo,
            payroll_id: payroll.id,
            grouping: '2',
          }),
        }),
      ]);

      const getStatusInfo = (documents: GetCurrentPayrollDocumentFilters) =>
        getWorkflowStatusInfo(app, {
          officeId: office.id_escritorio,
          expectedStatus: HttpStatus.OK,
          filters: {
            type: ObjectWorkflowType.PAYROLL_DATA,
            object_key_starts_with: `"${payroll.id}"`,
            documents,
          },
        });

      let res = await getStatusInfo(GetCurrentPayrollDocumentFilters.DOCUMENT);
      expect(res.body).toStrictEqual({
        status_in_use: {
          [PaymentRequestStatus.WAITING_FOR_INVOICE]: {
            count: 1,
            targets: [
              {
                code: PaymentRequestStatus.INVOICE_ISSUED,
                input_schema: [],
                name: 'Nota Fiscal Emitida',
              },
            ],
          },
        },
      });

      res = await getStatusInfo(GetCurrentPayrollDocumentFilters.NO_DOCUMENT);
      expect(res.body).toStrictEqual({
        status_in_use: {
          [PaymentRequestStatus.WAITING_FOR_INVOICE]: {
            count: 2,
            targets: [
              {
                code: PaymentRequestStatus.INVOICE_ISSUED,
                input_schema: [],
                name: 'Nota Fiscal Emitida',
              },
            ],
          },
        },
      });

      res = await getStatusInfo(GetCurrentPayrollDocumentFilters.ALL);
      expect(res.body).toStrictEqual({
        status_in_use: {
          [PaymentRequestStatus.WAITING_FOR_INVOICE]: {
            count: 3,
            targets: [
              {
                code: PaymentRequestStatus.INVOICE_ISSUED,
                input_schema: [],
                name: 'Nota Fiscal Emitida',
              },
            ],
          },
        },
      });
    });
  });

  describe('GET /:objectKey', () => {
    const getWorkflowCases = [
      {
        description: 'success, because admin role',
        claims: [{ atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' }],
      },
      {
        description: 'success, because user request is workflow owner',
        claims: [],
        ownerRequest: true,
      },
      {
        description: 'success, because user request can see workflow owner',
        claims: [
          {
            atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
            valor: JSON.stringify(['OWNER_CREDITOR']),
          },
        ],
      },
      {
        description: 'fail, because user request cannot see workflow owner',
        claims: [],
        expectedNotFound: true,
      },
    ];
    it.each(getWorkflowCases)(
      'when get object workflow, should be return $description',
      async ({ claims, ownerRequest, expectedNotFound }) => {
        const { credor: AdminCreditor, office } = await prepareLogin([
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        ]);

        const ownerCreditor = await Credor.create({
          id_credor_externo: 'OWNER_CREDITOR',
          nome_credor: 'OWNER_CREDITOR',
          escritorio_id_escritorio: office.id_escritorio,
          ativo: true,
        });

        const creditor = await Credor.create({
          id_credor_externo: 'JOAO',
          nome_credor: 'JOAO',
          escritorio_id_escritorio: office.id_escritorio,
          ativo: true,
        });

        const payrollConfig = await makePayrollConfig(app, office);

        const objectKey = uuid();
        const objectWorkflow = await Object_Workflow.create({
          created_by: AdminCreditor.id_credor,
          office_id: office.id_escritorio,
          object_key: objectKey,
          status: 'test',
          workflow_id: payrollConfig.id,
          requester: AdminCreditor.id_credor,
          object_owner: ownerCreditor.id_credor,
          input_schema: payrollConfig.config,
          input_data: {
            documents: ['doc1', 'doc2'],
          },
        });

        const creditorRequester = ownerRequest ? ownerCreditor : creditor;
        simulateLoggedUser({
          office,
          credor: creditorRequester,
          claims: [{ atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' }, ...claims],
        });

        const response = await getObjectWorkflow(app, {
          officeId: office.id_escritorio,
          objectKey,
          expectStatus: expectedNotFound ? HttpStatus.NOT_FOUND : HttpStatus.OK,
        });

        if (expectedNotFound) {
          expect(response.body).toEqual({
            code: 'OBJECT_WORKFLOW_NOT_FOUND',
            message: 'Não foi possível encontrar o objeto de workflow',
          });
          return;
        }
        expect(response.body).toEqual({
          id: objectWorkflow.id,
          input_schema: {
            input_schema: {
              type: 'object',
              required: ['documents'],
              properties: {
                documents: {
                  $id: '/schemas/documents',
                  type: 'array',
                  items: {
                    type: 'string',
                  },
                  maxItems: 5,
                  minItems: 0,
                },
              },
            },
          },
          inputs: { documents: ['doc1', 'doc2'] },
          context: {
            object_key: objectKey,
            requester: {
              can_see_receiver: true,
              is_payroll_status_manager: false,
              is_receiver: creditorRequester.id_credor_externo === ownerCreditor.id_credor_externo,
              profiles: '',
              user_id: creditorRequester.id_credor_externo,
            },
            receiver: {
              user_id: ownerCreditor.id_credor_externo,
              email: null,
              name: ownerCreditor.id_credor_externo,
            },
            inputs: {
              is_empty: false,
              is_complete: true,
              data: { documents: ['doc1', 'doc2'] },
            },
            external_data: null,
          },
          allow_input_insertion: true,
          inputs_validation: {},
        });

        await Promise.resolve();
      },
    );
  });

  describe('PUT /:objectKey/inputs', () => {
    const makeWorkflowFromPayrollData = async ({
      creditor,
      office,
      inputSchema,
    }: {
      creditor: Credor;
      office: Escritorio;
      inputSchema?: JSONSchema7;
    }) => {
      const payout_date = '2022-08-01';
      const payrollConfig = await makePayrollConfig(app, office, inputSchema);

      const { payrollData } = await makePayroll(office.id_escritorio, payout_date, {
        payroll: { config_id: payrollConfig.id },
        payrollData: [
          {
            creditor_id: creditor.id_credor_externo,
            amount: 100,
            grouping: 'PJ1',
          },
        ],
      });

      const objectKey = payrollData[0].getWorkflowKey();
      const objectWorkflow = await Object_Workflow.findOne({
        where: { object_key: objectKey },
      });

      return objectWorkflow;
    };

    const makeWorkflowFromPaymentRequest = async ({
      creditor,
      office,
      inputSchema = {
        type: 'object',
        properties: {
          documents: {
            $id: '/schemas/documents',
            type: 'array',
            items: {
              type: 'string',
            },
            minItems: 1,
          },
        },
      },
    }: {
      creditor: Credor;
      office: Escritorio;
      inputSchema?: JSONSchema7;
    }) => {
      const payout_date = '2022-08-01';

      const { config } = await mockPaymentRequestPayroll(
        office.id_escritorio,
        payout_date,
        [],
        1000,
        inputSchema,
      );

      const defaultGraph = JSON.parse(
        JSON.stringify(PaymentRequestStatusGraph.generateDefaultGraph()),
      );
      const currentGraph: StatusSerializedGraph = {
        ...defaultGraph,
        attributes: {
          ...defaultGraph.attributes,
          status_reset: {
            configs: [
              {
                type: ResetConfigActionType.ON_DOCUMENT_UPLOAD,
                allow_on_final_nodes: false,
                target_status: DOCUMENT_UPLOAD_STATUS,
              },
              {
                type: ResetConfigActionType.ON_DOCUMENT_DELETE,
                allow_on_final_nodes: true,
                target_status: DOCUMENT_DELETED_STATUS,
              },
              {
                type: ResetConfigActionType.ON_INPUT_FAILURE,
                allow_on_final_nodes: true,
                target_status: INPUT_FAILURE_STATUS,
              },
              {
                type: ResetConfigActionType.ON_INPUT_SUCCESS,
                allow_on_final_nodes: true,
                target_status: INPUT_SUCCESS_STATUS,
              },
            ],
          },
        },
      };
      // allow input insertion on last node
      const lastNode = currentGraph.nodes.at(-1).attributes.def as NodeDefinition;
      lastNode.allow_input_insertion = true;

      await config.update({
        status_flow_config: currentGraph,
      });

      const [paymentRequest] = await buildPaymentRequestWorkflow(office, config, creditor, {
        amount: 100,
        workflow_object: {
          input_schema: inputSchema,
        },
      });

      return paymentRequest.workflow_object;
    };

    const validateDocumentInputCases = [
      {
        description: 'ObjectWorkflowType.PAYROLL',
        makeObjectWorkflowFn: makeWorkflowFromPayrollData,
        documentUploadStatus: PayrollStatus.PAID,
        documentDeletedStatus: PayrollStatus.PENDING_RECEIVER_APPROVAL,
        expectedDocumentDate: '2022-08-01',
      },
      {
        description: 'ObjectWorkflowType.PAYMENT_REQUEST',
        makeObjectWorkflowFn: makeWorkflowFromPaymentRequest,
        documentUploadStatus: PaymentRequestStatus.PAID,
        documentDeletedStatus: PaymentRequestStatus.WAITING_FOR_INVOICE,
        expectedDocumentDate: formatDateToString(new Date()),
      },
    ];
    it.each(validateDocumentInputCases)(
      '($description) validate create document and remove document when update input. (steps: add one document > add two documents > remove 3 documents and add a document)',
      async ({ makeObjectWorkflowFn, documentUploadStatus, expectedDocumentDate }) => {
        const { credor, office } = await prepareLogin([
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
          { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
          {
            atributo: UsersClaims.DOCUMENT_MANAGEMENT,
            valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
          },
        ]);

        const objectWorkflow = await makeObjectWorkflowFn({
          creditor: credor,
          office,
        });

        const files = [
          'NF_teste.pdf',
          '0_row.xlsx',
          '1_row.xlsx',
          '2_rows.xlsx',
          '10_row_monthly_modal.xlsx',
        ];
        const [fileResponse, file2Response, file3Response, file4Response] = (
          await uploadFiles(app, files)
        ).body;

        expect(objectWorkflow.input_data).toBeNull();

        const updateInputAndValidate = async (
          payload: UpdateWorkflowInputs,
          expectedFileDocuments: number[],
          expectedStatus: string = documentUploadStatus,
        ) => {
          await updateWorkflowInputs(app, {
            office_id: office.id_escritorio,
            workflowKey: objectWorkflow.object_key,
            dto: payload,
          });

          await objectWorkflow.reload();

          const documents = await Document.findAll({
            where: {
              file_id: expectedFileDocuments,
              creditor_id: objectWorkflow.object_owner,
              date: expectedDocumentDate,
            },
            include: [Document_tag],
          });

          const expectedDocumentIds = documents.map(doc => String(doc.id));
          expect(objectWorkflow.input_data).toEqual({
            documents: expectedDocumentIds,
          });

          expect(objectWorkflow.status).toBe(expectedStatus);
          documents.forEach(document => {
            expect(document.tags).toHaveLength(1);
            const [tag] = document.tags;
            expect(tag.tag_name).toBe(OBJECT_WORKFLOW_KEY_TAG);
            expect(tag.tag_value).toBe(objectWorkflow.object_key);
          });

          return expectedDocumentIds;
        };

        // add one document
        const firstRequestDocumentIds = await updateInputAndValidate(
          {
            inputs: {
              documents: [],
            },
            files_to_insert: { documents: [fileResponse.fileId] },
          },
          [fileResponse.fileId],
        );

        // add more two documents
        const secondRequestDocumentIds = await updateInputAndValidate(
          {
            inputs: {
              documents: firstRequestDocumentIds,
            },
            files_to_insert: { documents: [file2Response.fileId, file3Response.fileId] },
          },
          [fileResponse.fileId, file2Response.fileId, file3Response.fileId],
        );

        // remove 3 document and add 1
        await updateInputAndValidate(
          {
            inputs: {
              documents: [],
            },
            files_to_insert: { documents: [file4Response.fileId] },
          },
          [file4Response.fileId],
        );
        const documentsToBeDeleted = await Document.findAll({
          where: {
            id: secondRequestDocumentIds,
          },
        });
        expect(documentsToBeDeleted).toHaveLength(0);

        return Promise.resolve();
      },
    );

    it.each(validateDocumentInputCases)(
      '($description) should create events when status changes on document upload/delete and status_reset is configured',
      async ({ makeObjectWorkflowFn, documentUploadStatus, documentDeletedStatus }) => {
        const { credor, office } = await prepareLogin([
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
          { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
          {
            atributo: UsersClaims.DOCUMENT_MANAGEMENT,
            valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
          },
        ]);

        const files = ['NF_teste.pdf', '0_row.xlsx'];
        const [fileResponse, file2Response] = (await uploadFiles(app, files)).body;

        const { id: objectWorkflowId } = await makeObjectWorkflowFn({
          creditor: credor,
          office,
        });

        const objectWorkflow = await Object_Workflow.scope('withEvents').findOne({
          where: { id: objectWorkflowId },
        });

        expect(objectWorkflow.status).toBe(documentDeletedStatus);
        expect(objectWorkflow.events).toHaveLength(0);

        // When adding documents, it should change to the specified status (documentUploadStatus) and create an event
        await updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto: {
            inputs: {
              documents: [],
            },
            files_to_insert: { documents: [fileResponse.fileId, file2Response.fileId] },
          },
        });

        await objectWorkflow.reload();
        expect(objectWorkflow.status).toBe(documentUploadStatus);
        expect(objectWorkflow.events).toHaveLength(1);
        expect(objectWorkflow.events).toEqual([
          expect.objectContaining({
            status: documentUploadStatus,
            old_status: documentDeletedStatus,
            data: expect.objectContaining({
              amount: '100.00',
            }),
          }),
        ]);

        // When removing a document, it should revert to the specified status (documentDeletedStatus) and create an event
        const firstDocumentInputData = objectWorkflow.input_data.documents[0];
        await updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto: {
            inputs: {
              documents: [firstDocumentInputData],
            },
          },
        });

        await objectWorkflow.reload();
        expect(objectWorkflow.status).toBe(documentDeletedStatus);
        expect(objectWorkflow.events).toHaveLength(2);
        expect(objectWorkflow.events.sort(sortByCreatedAt)).toEqual([
          expect.objectContaining({
            status: documentUploadStatus,
            old_status: documentDeletedStatus,
            data: expect.objectContaining({
              amount: '100.00',
            }),
            reason: ObjectWorkflowEventsReasons.ON_DOCUMENT_UPLOAD,
          }),
          expect.objectContaining({
            status: documentDeletedStatus,
            old_status: documentUploadStatus,
            data: expect.objectContaining({
              amount: '100.00',
            }),
            reason: ObjectWorkflowEventsReasons.ON_DOCUMENT_DELETE,
          }),
        ]);
      },
    );

    it.each(validateDocumentInputCases)(
      '($description) should not create events when status is the same and status_reset is configured',
      async ({ makeObjectWorkflowFn, documentUploadStatus, documentDeletedStatus }) => {
        const { credor, office } = await prepareLogin([
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
          { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
          {
            atributo: UsersClaims.DOCUMENT_MANAGEMENT,
            valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
          },
        ]);

        const files = ['NF_teste.pdf', '0_row.xlsx'];
        const [fileResponse, file2Response] = (await uploadFiles(app, files)).body;

        const { id: objectWorkflowId } = await makeObjectWorkflowFn({
          creditor: credor,
          office,
        });

        const objectWorkflow = await Object_Workflow.scope('withEvents').findOne({
          where: { id: objectWorkflowId },
          include: [Payroll_Config],
        });

        // configure to reset status on final nodes too
        const currentGraph = JSON.parse(JSON.stringify(objectWorkflow.workflow.getParsedGraph()));
        const updatedGraph: StatusSerializedGraph = {
          ...currentGraph,
          attributes: {
            ...currentGraph.attributes,
            status_reset: {
              configs: [
                {
                  type: ResetConfigActionType.ON_DOCUMENT_UPLOAD,
                  allow_on_final_nodes: true,
                  target_status: documentUploadStatus,
                },
                {
                  type: ResetConfigActionType.ON_DOCUMENT_DELETE,
                  allow_on_final_nodes: true,
                  target_status: documentDeletedStatus,
                },
              ],
            },
          },
        };
        await objectWorkflow.workflow.update({
          status_flow_config: updatedGraph,
        });

        expect(objectWorkflow.events).toHaveLength(0);

        // When adding documents, it should change to the specified status (documentUploadStatus) and create an event
        await updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto: {
            inputs: {
              documents: [],
            },
            files_to_insert: { documents: [fileResponse.fileId] }, // add only first file
          },
        });
        await objectWorkflow.reload();

        const eventsLengthAfterFirstProcess = objectWorkflow.events.length;

        expect(objectWorkflow.status).toBe(documentUploadStatus);
        expect(eventsLengthAfterFirstProcess).toBe(1);

        // process again same request which would lead to a change of status
        await updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto: {
            inputs: {
              documents: [],
            },
            files_to_insert: { documents: [file2Response.fileId] }, // add only second file
          },
        });
        await objectWorkflow.reload();

        expect(objectWorkflow.status).toBe(documentUploadStatus);
        expect(objectWorkflow.events).toHaveLength(eventsLengthAfterFirstProcess);
      },
    );

    it.each(validateDocumentInputCases)(
      '($description) should not change status and not create events for final nodes with restricted changes (allow_on_final_nodes is false)',
      async ({ makeObjectWorkflowFn, documentDeletedStatus, documentUploadStatus }) => {
        const { credor, office } = await prepareLogin([
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
          { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
          {
            atributo: UsersClaims.DOCUMENT_MANAGEMENT,
            valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
          },
        ]);

        const files = ['NF_teste.pdf', '0_row.xlsx'];
        const [fileResponse, file2Response] = (await uploadFiles(app, files)).body;

        const objectWorkflow = await makeObjectWorkflowFn({
          creditor: credor,
          office,
        });

        await objectWorkflow.reload({ include: [{ model: Payroll_Config }] });

        // Configure all nodes as final and block status changes
        const currentGraph = JSON.parse(
          JSON.stringify(objectWorkflow.workflow.getParsedGraph().toJSON()),
        );
        const updatedGraph: StatusSerializedGraph = {
          ...currentGraph,
          attributes: {
            ...currentGraph.attributes,
            status_reset: {
              configs: [
                {
                  type: ResetConfigActionType.ON_DOCUMENT_UPLOAD,
                  allow_on_final_nodes: false,
                  target_status: documentUploadStatus,
                },
                {
                  type: ResetConfigActionType.ON_DOCUMENT_DELETE,
                  allow_on_final_nodes: false,
                  target_status: documentDeletedStatus,
                },
              ],
            },
          },
        };
        currentGraph.nodes.forEach(node => {
          node.attributes.def.is_final = true;
        });
        await objectWorkflow.workflow.update({
          status_flow_config: updatedGraph,
        });

        expect(objectWorkflow.status).toBe(documentDeletedStatus);

        // Try to add documents - status should remain unchanged
        await updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto: {
            inputs: {
              documents: [],
            },
            files_to_insert: { documents: [fileResponse.fileId, file2Response.fileId] },
          },
        });

        await objectWorkflow.reload({ include: ['events'] });
        expect(objectWorkflow.status).toBe(documentDeletedStatus);
        expect(objectWorkflow.events).toHaveLength(0);

        // Manually change to another final status
        await objectWorkflow.update({
          status: documentUploadStatus,
        });

        // Try to remove documents - status should remain unchanged
        await updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto: {
            inputs: {
              documents: [objectWorkflow.input_data.documents[0]],
            },
          },
        });

        await objectWorkflow.reload({ include: ['events'] });
        expect(objectWorkflow.status).toBe(documentUploadStatus);
        expect(objectWorkflow.events).toHaveLength(0);
      },
    );

    it.each(validateDocumentInputCases)(
      '($description) should update successfully the PaymentRequest inputs',
      async ({ makeObjectWorkflowFn }) => {
        const { credor, office } = await prepareLogin([
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
          { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
          {
            atributo: UsersClaims.DOCUMENT_MANAGEMENT,
            valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
          },
        ]);

        const inputSchema: JSONSchema7 = {
          $id: 'https://example.com/schema',
          type: 'object',
          properties: {
            paymentAmount: {
              type: 'number',
              minimum: 0,
            },
            paymentDate: {
              type: 'string',
              format: 'date',
            },
            description: {
              type: 'string',
              maxLength: 255,
            },
          },
          required: ['paymentAmount', 'paymentDate'],
        };

        const objectWorkflow = await makeObjectWorkflowFn({
          creditor: credor,
          office,
          inputSchema,
        });

        await updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto: {
            inputs: {
              paymentAmount: 123,
              paymentDate: '2020-10-01',
            },
          },
        });
        await objectWorkflow.reload();

        expect(objectWorkflow.input_data).toEqual({
          paymentAmount: 123,
          paymentDate: '2020-10-01',
        });
      },
    );

    const validateAllowInputInsertionCases = [
      {
        description: 'true - ObjectWorkflowType.PAYROLL',
        makeObjectWorkflowFn: makeWorkflowFromPayrollData,
        expectedStatus: HttpStatus.OK,
        allowInputInsertion: true,
        expectedResponse: {},
      },
      {
        description: 'false - ObjectWorkflowType.PAYROLL',
        makeObjectWorkflowFn: makeWorkflowFromPayrollData,
        expectedStatus: HttpStatus.BAD_REQUEST,
        allowInputInsertion: false,
        expectedResponse: {
          code: BackendErrorCodes.OBJECT_WORKFLOW_INPUT_INSERTION_NOT_ALLOWED,
          message: 'Não é possível inserir inputs com o pedido no status atual',
        },
      },
      {
        description: 'undefined - ObjectWorkflowType.PAYROLL',
        makeObjectWorkflowFn: makeWorkflowFromPayrollData,
        expectedStatus: HttpStatus.OK,
        allowInputInsertion: undefined,
        expectedResponse: {},
      },
      {
        description: 'true - ObjectWorkflowType.PAYMENT_REQUEST',
        makeObjectWorkflowFn: makeWorkflowFromPaymentRequest,
        expectedStatus: HttpStatus.OK,
        allowInputInsertion: true,
        expectedResponse: {},
      },
      {
        description: 'false - ObjectWorkflowType.PAYMENT_REQUEST',
        makeObjectWorkflowFn: makeWorkflowFromPaymentRequest,
        expectedStatus: HttpStatus.BAD_REQUEST,
        allowInputInsertion: false,
        expectedResponse: {
          code: BackendErrorCodes.OBJECT_WORKFLOW_INPUT_INSERTION_NOT_ALLOWED,
          message: 'Não é possível inserir inputs com o pedido no status atual',
        },
      },
      {
        description: 'undefined - ObjectWorkflowType.PAYMENT_REQUEST',
        makeObjectWorkflowFn: makeWorkflowFromPaymentRequest,
        expectedStatus: HttpStatus.BAD_REQUEST,
        allowInputInsertion: undefined,
        expectedResponse: {
          code: BackendErrorCodes.OBJECT_WORKFLOW_INPUT_INSERTION_NOT_ALLOWED,
          message: 'Não é possível inserir inputs com o pedido no status atual',
        },
      },
    ];
    it.each(validateAllowInputInsertionCases)(
      '($description) test when allow_input_insertion is false',
      async ({ makeObjectWorkflowFn, allowInputInsertion, expectedStatus, expectedResponse }) => {
        const { credor, office } = await prepareLogin([
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
          { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
          {
            atributo: UsersClaims.DOCUMENT_MANAGEMENT,
            valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
          },
        ]);

        const files = ['NF_teste.pdf', '0_row.xlsx'];
        const [fileResponse, file2Response] = (await uploadFiles(app, files)).body;

        const objectWorkflow = await makeObjectWorkflowFn({
          creditor: credor,
          office,
        });

        await objectWorkflow.reload({ include: [{ model: Payroll_Config }] });

        // Pass all node to allow_input_insertion: false
        const currentGraph = objectWorkflow.workflow.getParsedGraph().toJSON();

        currentGraph.nodes.forEach(node => {
          if (allowInputInsertion !== undefined) {
            node.attributes.def.allow_input_insertion = allowInputInsertion;
            return;
          }
          if (node.attributes.def.allow_input_insertion !== undefined) {
            delete node.attributes.def.allow_input_insertion;
          }
        });

        await objectWorkflow.workflow.update({
          status_flow_config: currentGraph,
        });

        // Try to add documents
        const response = await updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto: {
            inputs: {
              documents: [],
            },
            files_to_insert: { documents: [fileResponse.fileId, file2Response.fileId] },
          },
          expectStatus: expectedStatus,
        });

        expect(response.body).toEqual(expectedResponse);
      },
    );

    it('should transition status correctly based on OCR validation results (fail and success)', async () => {
      const { credor, office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.ALLOWED_OCR_MODULE, valor: 'true' },
        {
          atributo: UsersClaims.DOCUMENT_MANAGEMENT,
          valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
        },
      ]);

      const formulaData = {
        failure_message: 'This is a failure message for validation.',
        formula: '1 == 3',
      };

      const { files, validationConfig, extractionConfig, validationResult } =
        await createValidationResult(office.id_escritorio, {
          validationConfig: [formulaData],
        });

      const schemaName = 'document_validation';

      const inputSchema = {
        type: 'object',
        properties: {
          [schemaName]: {
            $id: '/schemas/document_validation',
            type: 'array',
            items: {
              type: 'number',
            },
            title: 'Nota fiscal',
            maxItems: 3,
            minItems: 1,
            maxLength: 1,
            description: 'asda 123 d',
            $ocr_extraction_config_id: extractionConfig.id,
            $ocr_validation_config_id: validationConfig.id,
            $ocr_validation_extension: ['pdf'],
          },
        },
      } as JSONSchema7;

      const objectWorkflow = await makeWorkflowFromPaymentRequest({
        creditor: credor,
        office,
        inputSchema,
      });

      const documentPayload: DocumentCreationAttributes = {
        date: '2022-08-01',
        file_id: files[0].id,
        creditor_id: objectWorkflow.object_owner,
        approval: 'PENDING',
        created_by: objectWorkflow.object_owner,
        name: 'document.pdf',
        source: DocumentSource.CLOSURE,
      };

      const document = await Document.create(documentPayload);

      zombieServiceMock.executeFormula.mockResolvedValueOnce([
        {
          formula: formulaData.formula,
          result: 'false',
          metadata: {
            ocr: validationResult,
          },
        },
      ]);

      const failResponse = await updateWorkflowInputs(app, {
        office_id: office.id_escritorio,
        workflowKey: objectWorkflow.object_key,
        dto: {
          inputs: {
            documents: [],
            document_validation: [document.id],
            numeroNF: '6666',
            cnpjCorretorAutonomo: '12312312312345',
          },
        },
      });

      const { validations } = failResponse.body;

      expect(validations[schemaName]).toHaveLength(1);
      expect(validations[schemaName][0].results).toEqual([
        {
          success: false,
          message: formulaData.failure_message,
          formula: formulaData.formula,
        },
      ]);

      await objectWorkflow.reload();
      expect(objectWorkflow.status).toBe(INPUT_FAILURE_STATUS);

      zombieServiceMock.executeFormula.mockResolvedValueOnce([
        {
          formula: formulaData.formula,
          result: 'true',
          metadata: {
            ocr: validationResult,
          },
        },
      ]);

      const successResponse = await updateWorkflowInputs(app, {
        office_id: office.id_escritorio,
        workflowKey: objectWorkflow.object_key,
        dto: {
          inputs: {
            documents: [],
            document_validation: [document.id],
            numeroNF: '6666',
            cnpjCorretorAutonomo: '12312312312345',
          },
        },
      });

      const { validations: successValidations } = successResponse.body;
      expect(successValidations[schemaName]).toHaveLength(1);
      expect(successValidations[schemaName][0].results).toEqual([
        {
          success: true,
          message: formulaData.failure_message,
          formula: formulaData.formula,
        },
      ]);

      await objectWorkflow.reload();
      const workflowEvent = await Object_Workflow_Events.findOne({
        where: {
          object_workflow_id: objectWorkflow.id,
        },
        order: [['created_at', 'DESC']],
      });

      expect(objectWorkflow.status).toBe(INPUT_SUCCESS_STATUS);
      expect(workflowEvent.reason).toBe(ObjectWorkflowEventsReasons.ON_INPUT_SUCCESS);
    });

    it('should respect the feature.document.management claim > make the update/deletion > return forbidden', async () => {
      const { credor, office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        {
          atributo: UsersClaims.DOCUMENT_MANAGEMENT,
          valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
        },
      ]);

      const otherCreditor = await createCreditors({
        officeId: office.id_escritorio,
        creditorId: uuid(),
      });

      const objectWorkflow = await makeWorkflowFromPaymentRequest({
        creditor: otherCreditor.creditorLogged,
        office,
        inputSchema: {
          type: 'object',
          properties: {
            documents: {
              $id: '/schemas/documents',
              type: 'array',
              items: {
                type: 'string',
              },
              minItems: 1,
            },
          },
        },
      });

      const files = ['NF_teste.pdf', '0_row.xlsx'];

      const [fileResponse, file2Response] = (await uploadFiles(app, files)).body;

      const updateInput = async (dto: UpdateWorkflowInputs, expectStatus?: number) =>
        updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto,
          expectStatus,
        });

      const createDocument = async (errorStatus?: number) =>
        updateInput(
          {
            inputs: {
              documents: [],
            },
            files_to_insert: { documents: [fileResponse.fileId] },
          },
          errorStatus,
        );

      const deleteDocument = async (errorStatus?: number) =>
        updateInput(
          {
            inputs: {
              documents: [String(file2Response.fileId)],
            },
          },
          errorStatus,
        );

      await createDocument();
      await deleteDocument();

      simulateLoggedUser({
        office,
        credor,
        claims: [
          {
            atributo: UsersClaims.DOCUMENT_MANAGEMENT,
            valor: JSON.stringify({
              deletion: 'self',
              upload: 'self',
              view: 'self',
              approve: true,
            }),
          },
        ],
      });

      await createDocument(HttpStatus.FORBIDDEN);
      await deleteDocument(HttpStatus.FORBIDDEN);
    });

    it('should remove related documents when deleted it', async () => {
      const { office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        {
          atributo: UsersClaims.DOCUMENT_MANAGEMENT,
          valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
        },
      ]);

      const otherCreditor = await createCreditors({
        officeId: office.id_escritorio,
        creditorId: uuid(),
      });

      const objectWorkflow = await makeWorkflowFromPaymentRequest({
        creditor: otherCreditor.creditorLogged,
        office,
        inputSchema: {
          type: 'object',
          properties: {
            document_validation: {
              $id: '/schemas/document_validation',
              type: 'array',
              items: {
                type: 'number',
              },
              title: 'Nota fiscal',
              maxItems: 3,
              minItems: 1,
              maxLength: 1,
              description: 'asda 123 d',
            },
            documents: {
              $id: '/schemas/documents',
              type: 'array',
              items: {
                type: 'string',
              },
              minItems: 1,
            },
          },
        },
      });

      const files = ['NF_teste.pdf'];

      const [fileResponse] = (await uploadFiles(app, files)).body;

      const updateInput = async (dto: UpdateWorkflowInputs, expectStatus?: number) =>
        updateWorkflowInputs(app, {
          office_id: office.id_escritorio,
          workflowKey: objectWorkflow.object_key,
          dto,
          expectStatus,
        });

      const createDocument = async (errorStatus?: number) =>
        updateInput(
          {
            inputs: {
              documents: [],
            },
            files_to_insert: { documents: [fileResponse.fileId] },
          },
          errorStatus,
        );

      await createDocument();
      const fileDocument = await Document.findOne({
        where: {
          file_id: fileResponse.fileId,
          creditor_id: objectWorkflow.object_owner,
        },
      });

      await objectWorkflow.reload();

      objectWorkflow.input_data = {
        ...objectWorkflow.input_data,
        document_validation: [String(fileDocument.id)],
      };

      await objectWorkflow.save();

      expect(objectWorkflow.input_data).toStrictEqual({
        documents: [String(fileDocument.id)],
        document_validation: [String(fileDocument.id)],
      });

      // delete via "Documents" controller
      await deleteDocuments(app, {
        officeId: office.id_escritorio,
        document_ids: [String(fileDocument.id)],
      });

      await objectWorkflow.reload();
      expect(objectWorkflow.input_data).toStrictEqual({
        documents: [],
        document_validation: [],
      });
    });

    it('dont call ses services when email does not exist on creditor to notify', async () => {
      const { credor: creditorLogged, office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
        {
          atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
          valor: 'true',
        },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        {
          atributo: UsersClaims.DOCUMENT_MANAGEMENT,
          valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
        },
      ]);

      const paymentRequestOwner = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A0',
        ativo: true,
        nome_credor: 'Logged Creditor',
      });

      // creditorLogged has no email
      await creditorLogged.update({
        email: null,
      });

      const files = ['NF_teste.pdf', '0_row.xlsx'];
      const [fileResponse, file2Response] = (await uploadFiles(app, files)).body;

      // creates a basic admin profile
      const { body: adminProfile } = await createPermissionProfile(app, {
        officeId: office.id_escritorio,
        payload: {
          permission_data: [{ name: UsersClaims.GENERIC_DATA_IMPORT, value: 'false' }],
          name: 'admin_profile',
        },
      });

      // adds creditorLogged to admin_profile
      await updatePermissions(app, {
        officeId: office.id_escritorio,
        permissionProfileId: adminProfile.id,
        payload: {
          creditor_ids: [creditorLogged.id_credor],
          team_ids: [],
        },
      });

      // adds document management config to notify
      await Atributos_credor.create({
        credor_id_credor: paymentRequestOwner.id_credor,
        atributo: UsersClaims.DOCUMENT_MANAGEMENT,
        valor: JSON.stringify({
          view: 'self',
          upload: 'self',
          deletion: 'self',
          profiles_to_notify_on_upload: ['admin_profile'], // config to notify admin_profile users
        }),
      });

      const objectWorkflow = await makeWorkflowFromPaymentRequest({
        creditor: paymentRequestOwner,
        office,
      });

      await updateWorkflowInputs(app, {
        office_id: office.id_escritorio,
        workflowKey: objectWorkflow.object_key,
        dto: {
          inputs: {
            documents: [],
          },
          files_to_insert: { documents: [fileResponse.fileId, file2Response.fileId] },
        },
      });

      expect(sesFacadeMock.sendTemplatedEmail).toHaveBeenCalledTimes(0);

      return Promise.resolve();
    });

    it('successfully that emails are sent to request owner when logged creditor is configured and try update input', async () => {
      const { credor: creditorLogged, office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
        {
          atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
          valor: 'true',
        },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        {
          atributo: UsersClaims.DOCUMENT_MANAGEMENT,
          valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
        },
      ]);

      const [paymentRequestOwner, userThatWillReceiveEmail] = await Promise.all([
        Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'A0',
          ativo: true,
          nome_credor: 'request owner',
        }),
        Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'A2',
          ativo: true,
          email: '<EMAIL>',
          nome_credor: 'User that will receive email',
        }),
      ]);

      const files = ['NF_teste.pdf', '0_row.xlsx'];
      const [fileResponse, file2Response] = (await uploadFiles(app, files)).body;

      // adds email for logged creditor (the one who gets notified on upload)
      creditorLogged.email = '<EMAIL>';
      await creditorLogged.save();

      // creates a basic admin profile
      const { body: adminProfile } = await createPermissionProfile(app, {
        officeId: office.id_escritorio,
        payload: {
          permission_data: [{ name: UsersClaims.GENERIC_DATA_IMPORT, value: 'false' }],
          name: 'admin_profile',
        },
      });

      // adds creditorLogged to admin_profile
      await updatePermissions(app, {
        officeId: office.id_escritorio,
        permissionProfileId: adminProfile.id,
        payload: {
          creditor_ids: [creditorLogged.id_credor, userThatWillReceiveEmail.id_credor],
          team_ids: [],
        },
      });

      // adds document management config to notify
      await Atributos_credor.create({
        credor_id_credor: paymentRequestOwner.id_credor,
        atributo: UsersClaims.DOCUMENT_MANAGEMENT,
        valor: JSON.stringify({
          view: 'self',
          upload: 'self',
          deletion: 'self',
          profiles_to_notify_on_upload: ['admin_profile'], // config to notify admin_profile users
        }),
      });

      // change current date to assert document date
      vi.setSystemTime(formatISO(new Date(2025, 3, 2, 10, 30)));

      const objectWorkflow = await makeWorkflowFromPaymentRequest({
        creditor: paymentRequestOwner,
        office,
      });

      await updateWorkflowInputs(app, {
        office_id: office.id_escritorio,
        workflowKey: objectWorkflow.object_key,
        dto: {
          inputs: {
            documents: [],
          },
          files_to_insert: { documents: [fileResponse.fileId, file2Response.fileId] },
        },
      });

      expect(sesFacadeMock.sendTemplatedEmail).toHaveBeenCalledTimes(2);
      expect(sesFacadeMock.sendTemplatedEmail).toHaveBeenCalledWith({
        Destination: {
          ToAddresses: ['<EMAIL>'],
        },
        Source: 'SplitC <<EMAIL>>',
        Template: 'GenericPayrollNotification',
        TemplateData: JSON.stringify({
          sub: 'Novos documentos foram enviados em 02/04/2025',
          subject: "Novos documentos em '02/04/2025'",
          message: 'A0 - 2 novo(s) documento(s)',
          mailto: 'Olá, Logged Creditor',
        }),
      });

      expect(sesFacadeMock.sendTemplatedEmail).toHaveBeenCalledWith({
        Destination: {
          ToAddresses: [userThatWillReceiveEmail.email],
        },
        Source: 'SplitC <<EMAIL>>',
        Template: 'GenericPayrollNotification',
        TemplateData: JSON.stringify({
          sub: 'Novos documentos foram enviados em 02/04/2025',
          subject: "Novos documentos em '02/04/2025'",
          message: 'A0 - 2 novo(s) documento(s)',
          mailto: 'Olá, User that will receive email',
        }),
      });

      return Promise.resolve();
    });

    it('validate document create in payment_request.created_at - 3 hours(timezone America/Sao_Paulo)', async () => {
      const { credor, office, authenticationContextDto } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        {
          atributo: UsersClaims.DOCUMENT_MANAGEMENT,
          valor: '{"deletion":"anyone","upload":"anyone", "view": "anyone"}',
        },
      ]);
      const fixedDate = new Date(Date.UTC(2025, 4, 9, 1, 30)); // set fixed date to 2025-05-09 01:30:00 UTC
      vi.setSystemTime(fixedDate);
      const files = ['NF_teste.pdf', '0_row.xlsx'];
      const [fileResponse] = (await uploadFiles(app, files)).body;

      const { object_key } = await makeWorkflowFromPaymentRequest({
        creditor: credor,
        office,
      });
      vi.setSystemTime(Date.now());
      await updateWorkflowInputs(app, {
        office_id: office.id_escritorio,
        workflowKey: object_key,
        dto: {
          inputs: {
            documents: [],
          },
          files_to_insert: { documents: [fileResponse.fileId] }, // add only first file
        },
      });

      const objectWorkflow = await Object_Workflow.scope({
        method: [
          'withDocumentTags',
          {
            requester: authenticationContextDto,
          },
        ],
      }).findOne({
        where: {
          office_id: office.id_escritorio,
          object_key,
        },
      });

      const [document] = objectWorkflow.document_tags.map(tag => tag.document);
      expect(document.date).toBe('2025-05-08');
    });
  });

  describe('GET /:objectKey/context', () => {
    it('should return 404 if no object workflow found', async () => {
      const { office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
      ]);

      await getObjectWorkflowContext(app, {
        officeId: office.id_escritorio,
        objectKey: 'test',
        expectStatus: HttpStatus.NOT_FOUND,
      });

      await Promise.resolve();
    });

    it('should return the workflow context', async () => {
      const { credor, office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
      ]);

      await Object_Workflow.create({
        workflow_id: 1,
        created_by: credor.id_credor,
        office_id: office.id_escritorio,
        object_key: 'test',
        status: 'test',
        requester: credor.id_credor,
        object_owner: credor.id_credor,
        input_schema: {},
      });

      const objectWorkflowContext = await getObjectWorkflowContext(app, {
        officeId: office.id_escritorio,
        objectKey: 'test',
      });

      expect(objectWorkflowContext.body).toEqual({
        inputs: {
          data: null,
          is_complete: false,
          is_empty: true,
        },
        object_key: 'test',
        receiver: {
          user_id: credor.id_credor_externo,
          email: null,
          name: credor.nome_credor,
        },
        requester: {
          can_see_receiver: true,
          is_payroll_status_manager: false,
          is_receiver: true,
          profiles: '',
          user_id: credor.id_credor_externo,
        },
        external_data: null,
      });

      await Promise.resolve();
    });

    it('should return payroll data workflow context', async () => {
      const { credor, office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
      ]);

      const { payroll } = await mockPaymentRequestPayroll(office.id_escritorio, '2022-09-01');

      const payrollData = await Payroll_Data.create({
        payroll_id: payroll.id,
        creditor_id: credor.id_credor_externo,
        grouping: 'test',
        amount: 100,
        status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
      });

      const workflowKey = payrollData.getWorkflowKey();

      await Object_Workflow.create({
        workflow_id: 1,
        created_by: credor.id_credor,
        office_id: office.id_escritorio,
        object_key: workflowKey,
        status: 'initial status',
        requester: credor.id_credor,
        object_owner: credor.id_credor,
        input_schema: {},
      });

      const objectWorkflowContext = await getObjectWorkflowContext(app, {
        officeId: office.id_escritorio,
        objectKey: workflowKey,
      });

      expect(objectWorkflowContext.body).toEqual({
        inputs: {
          data: null,
          is_complete: false,
          is_empty: true,
        },
        object_key: workflowKey,
        receiver: {
          user_id: credor.id_credor_externo,
          email: null,
          name: credor.nome_credor,
        },
        payroll_data: {
          amount: 100,
          grouping: 'test',
          payroll_date: '2022-09-01',
          receiver_user_id: credor.id_credor_externo,
        },
        requester: {
          can_see_receiver: true,
          is_payroll_status_manager: false,
          is_receiver: true,
          profiles: '',
          user_id: credor.id_credor_externo,
        },
        external_data: null,
      });

      await Promise.resolve();
    });

    it('should return payment request workflow context', async () => {
      const payout_date = '2022-09-01';

      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
        { atributo: UsersClaims.ALLOWED_OCR_MODULE, valor: 'true' },
      ]);

      const { payroll, config } = await mockPaymentRequestPayroll(
        office.id_escritorio,
        payout_date,
      );

      config.status_flow_config.nodes[0].attributes.def.is_final = true;
      await config.update({ status_flow_config: config.status_flow_config });

      await mockPayrollDataAndProcess(app, office.id_escritorio, payout_date, [
        {
          amount: 500,
          creditor_id: credor.id_credor_externo,
          grouping: 'these',
          metadata: [
            {
              Empreendimento: '1',
            },
          ],
        },
        {
          amount: 200,
          creditor_id: credor.id_credor_externo,
          grouping: 'guys',
          metadata: [
            {
              Empreendimento: '1',
            },
          ],
        },
      ]);

      const { body } = await createPayrollRequest(app, {
        office_id: office.id_escritorio,
        payload: {
          items: [
            {
              creditor_id: credor.id_credor_externo,
              payroll_data: [
                {
                  payroll_id: payroll.id,
                  amount: 500,
                  creditor_id: credor.id_credor_externo,
                  grouping: 'these',
                  metadata: [
                    {
                      Empreendimento: '1',
                    },
                  ],
                },
                {
                  payroll_id: payroll.id,
                  amount: 200,
                  creditor_id: credor.id_credor_externo,
                  grouping: 'guys',
                  metadata: [
                    {
                      Empreendimento: '1',
                    },
                  ],
                },
              ],
            },
          ],
        },
      });

      const paymentRequest = body.created[0];

      const response = await getObjectWorkflowContext(app, {
        officeId: office.id_escritorio,
        objectKey: paymentRequest.request_key,
      });
      expect(response.body).toEqual({
        object_key: paymentRequest.request_key,
        requester: {
          can_see_receiver: true,
          is_payroll_status_manager: true,
          is_receiver: true,
          profiles: '',
          user_id: 'A1',
        },
        receiver: {
          user_id: 'A1',
          email: null,
          name: 'Logged Creditor',
        },
        payment_request: {
          amount: '700.00',
          grouping: {
            Empreendimento: '1',
          },
          key: paymentRequest.request_key,
          payments: expect.any(Array),
        },
        inputs: {
          is_empty: true,
          is_complete: false,
          data: null,
        },
        external_data: null,
      });
      return Promise.resolve();
    });
  });

  describe('GET /:objectKey/history', () => {
    it('should return 404 if no object workflow found', async () => {
      const { office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
      ]);

      await getObjectWorkflowContext(app, {
        officeId: office.id_escritorio,
        objectKey: 'test',
        expectStatus: HttpStatus.NOT_FOUND,
      });

      await Promise.resolve();
    });

    it('should return 404 if object workflow pertains to a different office', async () => {
      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
      ]);

      await Object_Workflow.create({
        workflow_id: 1,
        created_by: credor.id_credor,
        office_id: office.id_escritorio,
        object_key: 'test',
        status: 'intial',
        requester: credor.id_credor,
        object_owner: credor.id_credor,
        input_schema: {},
      });

      const otherOffice = await createTestOffice();
      await getObjectWorkflowHistory(app, {
        officeId: otherOffice.id_escritorio,
        objectKey: 'test',
        expectStatus: HttpStatus.NOT_FOUND,
      });

      await Promise.resolve();
    });

    it('should return the workflow history', async () => {
      const { credor, office } = await prepareLogin(
        [
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        ],
        'admin',
      );

      const defaultGraph = StatusGraph.generateDefaultGraph();
      const payrollConfig = await Payroll_Config.create({
        office_id: office.id_escritorio,
        name: 'test',
        status_flow_config: defaultGraph.toJSON(),
        created_by: credor.id_credor,
        graph_type: 'payment_request',
      });

      const objectWorkflow = await Object_Workflow.create({
        workflow_id: payrollConfig.id,
        created_by: credor.id_credor,
        office_id: office.id_escritorio,
        type: 'payment_request',
        object_key: '"436"."LUIS/SPLITC"."EMPRESA CAHL"',
        status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
        requester: credor.id_credor,
        object_owner: credor.id_credor,
        input_schema: {},
      });

      const statusGraph = payrollConfig.getParsedGraph();
      const firstEvent = await Object_Workflow_Events.create({
        office_id: office.id_escritorio,
        old_status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
        status: PayrollStatus.RECEIVER_APPROVED,
        requester: credor.id_credor,
        data: { amount: 100 },
        object_workflow_id: objectWorkflow.id,
        object_key: objectWorkflow.object_key,
        reason: ObjectWorkflowEventsReasons.PAYROLL_PROCESS,
      });

      const secondEvent = await Object_Workflow_Events.create({
        office_id: office.id_escritorio,
        old_status: PayrollStatus.RECEIVER_APPROVED,
        status: PayrollStatus.PAID,
        requester: credor.id_credor,
        data: { amount: 100 },
        object_workflow_id: objectWorkflow.id,
        object_key: objectWorkflow.object_key,
        reason: ObjectWorkflowEventsReasons.USER_ACTION,
        metadata: {
          status_name: statusGraph.getStatusDef(PayrollStatus.PAID)?.name,
          old_status_name: statusGraph.getStatusDef(PayrollStatus.RECEIVER_APPROVED)?.name,
        },
      });

      const { body: objectWorkflowHistory } = await getObjectWorkflowHistory(app, {
        officeId: office.id_escritorio,
        objectKey: '"436"."LUIS/SPLITC"."EMPRESA CAHL"',
      });

      expect(objectWorkflowHistory).toEqual({
        events: [
          {
            created_at: secondEvent.created_at.toISOString(),
            requester: `${credor.nome_credor} - ${credor.id_credor_externo}`,
            amount: secondEvent.data?.amount,
            id: secondEvent.id,
            old_status: statusGraph.getStatusDef(PayrollStatus.RECEIVER_APPROVED).name,
            new_status: statusGraph.getStatusDef(PayrollStatus.PAID).name,
            reason: ObjectWorkflowEventsReasons.USER_ACTION,
          },
          {
            created_at: firstEvent.created_at.toISOString(),
            requester: `${credor.nome_credor} - ${credor.id_credor_externo}`,
            amount: firstEvent.data?.amount,
            id: firstEvent.id,
            old_status: statusGraph.getStatusDef(PayrollStatus.PENDING_RECEIVER_APPROVAL).name,
            new_status: statusGraph.getStatusDef(PayrollStatus.RECEIVER_APPROVED).name,
            reason: ObjectWorkflowEventsReasons.PAYROLL_PROCESS,
          },
        ],
      });

      await Promise.resolve();
    });

    it('should not return previous event if the current not changed', async () => {
      const { credor, office } = await prepareLogin(
        [
          { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
          { atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: 'true' },
        ],
        'admin',
      );

      const defaultGraph = StatusGraph.generateDefaultGraph();
      const payrollConfig = await Payroll_Config.create({
        office_id: office.id_escritorio,
        name: 'test',
        status_flow_config: defaultGraph.toJSON(),
        created_by: credor.id_credor,
        graph_type: 'payment_request',
      });

      const objectWorkflow = await Object_Workflow.create({
        workflow_id: payrollConfig.id,
        created_by: credor.id_credor,
        office_id: office.id_escritorio,
        type: 'payment_request',
        object_key: '"436"."LUIS/SPLITC"."EMPRESA CAHL"',
        status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
        requester: credor.id_credor,
        object_owner: credor.id_credor,
        input_schema: {},
      });

      await Object_Workflow_Events.bulkCreate([
        {
          office_id: office.id_escritorio,
          old_status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
          status: PayrollStatus.RECEIVER_APPROVED,
          requester: credor.id_credor,
          data: { amount: 100 },
          object_workflow_id: objectWorkflow.id,
          object_key: objectWorkflow.object_key,
          reason: ObjectWorkflowEventsReasons.PAYROLL_PROCESS,
        },
        {
          office_id: office.id_escritorio,
          old_status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
          status: PayrollStatus.RECEIVER_APPROVED,
          requester: credor.id_credor,
          data: { amount: 100 },
          object_workflow_id: objectWorkflow.id,
          object_key: objectWorkflow.object_key,
          reason: ObjectWorkflowEventsReasons.PAYROLL_PROCESS,
        },
      ]);

      const { body: objectWorkflowHistory } = await getObjectWorkflowHistory(app, {
        officeId: office.id_escritorio,
        objectKey: '"436"."LUIS/SPLITC"."EMPRESA CAHL"',
      });

      expect(objectWorkflowHistory.events).toHaveLength(1);

      await Promise.resolve();
    });
  });

  describe('PATCH /status', () => {
    const mockPaymentRequest = async (
      office: Escritorio,
      payroll: Payroll,
      payroll_config,
      credor: Credor,
      inputs?: Record<string, unknown>,
    ) => {
      sheetDbApiServiceMock.getPayrollDataToProcess.mockResolvedValue({
        hash: 'hash',
        hash_metadata: [
          { period_id: 123, last_changed_at: '2024-01-02T00:00:00' },
          { period_id: 456, last_changed_at: '2024-01-01T00:00:00' },
        ],
        payroll_data: [
          {
            payroll_id: payroll.id,
            amount: 500,
            creditor_id: 'RODOLFO',
            grouping: 'hakuna',
            metadata: [
              {
                Empreendimento: '1',
              },
            ],
          },
          {
            payroll_id: payroll.id,
            amount: 200,
            creditor_id: 'RODOLFO',
            grouping: 'matata',
            metadata: [
              {
                Empreendimento: '2',
              },
            ],
          },
          {
            payroll_id: payroll.id,
            amount: 2000,
            creditor_id: 'RODOLFO',
            grouping: 'rei_leao',
            metadata: [
              {
                Empreendimento: '2',
              },
            ],
          },
        ],
      });

      await processPayroll<GetCurrentPayrollResponse>(app, {
        officeId: office.id_escritorio,
        payout_date: payroll.payout_date,
        payload: { hashOnTheFly: 'hash' },
      });

      const [paymentRequest] = await buildPaymentRequestWorkflow(office, payroll_config, credor, {
        workflow_object: {
          input_data: inputs,
        },
      });

      await Payment_Data_Request.create({
        payment_request_id: paymentRequest.id,
        payroll_data_key: `"${payroll.id}"."${credor.id_credor_externo}"."rei_leao"`,
        payroll_data_info: {
          payroll_id: payroll.id,
          amount: 2000,
          creditor_id: 'RODOLFO',
          grouping: 'rei_leao',
          metadata: [
            {
              Empreendimento: '2',
            },
          ],
        } as unknown as Payroll_Data,
      });

      return paymentRequest;
    };

    const saveEdgeHooks = async (payroll_config, hooks: HookNodeDefinition[]) => {
      const graph = payroll_config.getParsedGraph() as PaymentRequestStatusGraph;
      const edgeAttrs = graph.getEdgeAttributes(
        graph.edge(PaymentRequestStatus.INVOICE_ISSUED, PaymentRequestStatus.INVOICE_SENT),
      );
      edgeAttrs.hooks = hooks;
      payroll_config.status_flow_config = graph as any;
      await payroll_config.save();
    };

    it('validates invalid status and successful status change on bulk change statuses', async () => {
      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
      ]);

      const { payroll, config } = await mockPaymentRequestPayroll(
        office.id_escritorio,
        '2024-01-10',
      );

      const size = 35;
      const payments = await bulkCreatePaymentRequest(
        office,
        payroll,
        config,
        credor,
        undefined,
        size,
      );

      const start_creation_date = payments[0].created_at.toISOString();
      const currentDate = new Date();
      currentDate.setDate(currentDate.getDate() + 1);

      const end_creation_date = currentDate.toISOString();

      const spy = vi.spyOn(ObjectWorkflowDomain.prototype, 'updateWorkflowStatus');

      const errorResponse = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        query: {
          type: ObjectWorkflowType.PAYMENT_REQUEST,
          created_from: start_creation_date,
          created_to: end_creation_date,
        },
        payload: {
          status_mapping: [
            {
              source: PaymentRequestStatus.WAITING_FOR_INVOICE,
              target: PaymentRequestStatus.INVOICE_ISSUED, // must have input_data
            },
            {
              source: PaymentRequestStatus.INVOICE_ISSUED,
              target: PaymentRequestStatus.PAID, // must be PaymentRequestStatus.INVOICE_SENT
            },
          ],
        },
      });

      expect(spy).toHaveBeenCalledTimes(size);

      expect(errorResponse.body).toStrictEqual({
        errors: expect.arrayContaining(
          payments.map(payment => ({
            object_key: payment.workflow_object.object_key,
            message: BackendErrors.INVALID_NEXT_STATUS.description,
            reason: BackendErrorCodes.INVALID_NEXT_STATUS,
            type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
          })),
        ),
      });

      const paymentsOdd = payments.filter((_, index) => index % 2 === 0);
      await Promise.all(
        paymentsOdd.map(async payment => {
          payment.workflow_object.input_data = {
            paymentAmount: 123,
            paymentDate: '2020-10-01',
          };
          await payment.workflow_object.save();
        }),
      );

      const responseSuccess = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        query: {
          type: ObjectWorkflowType.PAYMENT_REQUEST,
          created_from: start_creation_date,
          created_to: end_creation_date,
        },
        payload: {
          status_mapping: [
            {
              source: PaymentRequestStatus.WAITING_FOR_INVOICE,
              target: PaymentRequestStatus.INVOICE_ISSUED,
            },
            {
              source: PaymentRequestStatus.INVOICE_ISSUED,
              target: PaymentRequestStatus.INVOICE_SENT,
            },
          ],
        },
      });

      await Promise.all(payments.map(async payment => payment.reload()));
      expect(responseSuccess.body).toStrictEqual({
        errors: [],
      });
      expect(
        payments.every(
          (payment, index) =>
            payment.workflow_object.status ===
            (index % 2 === 0
              ? PaymentRequestStatus.INVOICE_ISSUED
              : PaymentRequestStatus.INVOICE_SENT),
        ),
      ).toStrictEqual(true);
    });

    it('Should update payroll status correctly and create object workflow events correctly', async () => {
      const loggedUserId = 'A1';
      const { office } = await prepareLogin(payrollAdminClaims, loggedUserId);
      const payout_date = '2024-03-21';

      await makePayroll(office.id_escritorio, payout_date);

      const { defaultPayroll, defaultPayrollData } = makeDefaultPayrollData({
        payroll: { hash: '1234' },
        payrollData: [
          {
            amount: 100,
            creditor_id: loggedUserId,
            grouping: 'PJ1',
          },
        ],
      });

      sheetDbApiServiceMock.getPayrollDataToProcess.mockResolvedValue({
        ...defaultPayroll,
        payroll_data: defaultPayrollData,
      });

      const { body: payroll } = await processPayroll<GetCurrentPayrollResponse>(app, {
        officeId: office.id_escritorio,
        payout_date,
        payload: { hashOnTheFly: defaultPayroll.hash },
      });

      const object_keys = await Payroll_Data.scope('withWorkflow').findAll({
        where: {
          id: payroll.payroll_data.map(({ id }) => id),
        },
      });

      const payrollDataId = object_keys.map(({ id, workflow_object }) => ({
        id,
        object_key: workflow_object.object_key,
      }));

      const { body: firstUpdateResponse } = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        payload: [
          {
            object_key: payrollDataId[0].object_key,
            current_status: PayrollStatus.PAID, // this change is INVALID because the current status is the initial status, not PAID
            new_status: PayrollStatus.RECEIVER_APPROVED,
            hook_inputs: [],
          },
          {
            object_key: payrollDataId[1].object_key,
            current_status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
            new_status: PayrollStatus.RECEIVER_APPROVED, // this change is INVALID, because the receiver is not the requester
            hook_inputs: [],
          },
          {
            object_key: payrollDataId[2].object_key,
            current_status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
            new_status: PayrollStatus.RECEIVER_DISAPPROVED, // this change is INVALID, because the receiver is not the requester
            hook_inputs: [],
          },
          {
            object_key: payrollDataId[3].object_key,
            current_status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
            new_status: PayrollStatus.RECEIVER_APPROVED, // this change IS VALID, because the receiver is the requester
            hook_inputs: [],
          },
        ],
      });

      const errors = [
        {
          object_key: payrollDataId[0].object_key,
          reason: StatusUpdateErrorReasons.INVALID_CURRENT_STATUS,
          type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
        },
        {
          object_key: payrollDataId[1].object_key,
          reason: BackendErrorCodes.INVALID_NEXT_STATUS,
          type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
          message: BackendErrors[StatusUpdateErrorReasons.INVALID_NEXT_STATUS].description,
        },
        {
          object_key: payrollDataId[2].object_key,
          reason: BackendErrorCodes.INVALID_NEXT_STATUS,
          type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
          message: BackendErrors[StatusUpdateErrorReasons.INVALID_NEXT_STATUS].description,
        },
      ].sort((a, b) => a.object_key.localeCompare(b.object_key));

      expect(firstUpdateResponse.errors).toEqual(errors);

      await validateWorkflowObjectStatus(
        payrollDataId[0].id,
        PayrollStatus.PENDING_RECEIVER_APPROVAL,
      );
      await validateWorkflowObjectStatus(
        payrollDataId[1].id,
        PayrollStatus.PENDING_RECEIVER_APPROVAL,
      );
      await validateWorkflowObjectStatus(
        payrollDataId[2].id,
        PayrollStatus.PENDING_RECEIVER_APPROVAL,
      );
      await validateWorkflowObjectStatus(payrollDataId[3].id, PayrollStatus.RECEIVER_APPROVED);

      const getPayrollDataInfo = (payrollData: Payroll_Data) => ({
        amount: '100.00',
        created_at: expect.any(String),
        creditor_id: payrollData.creditor_id,
        grouping: payrollData.grouping,
        id: payrollData.id,
        metadata: payrollData.metadata,
        payroll_id: payrollData.payroll_id,
        updated_at: expect.any(String),
      });

      const statusChangeEvents = await Payroll_Data.scope('withWorkflow').findOne({
        where: { id: payrollDataId[3].id },
        include: [Object_Workflow.scope('withEvents')],
      });

      expect(statusChangeEvents.workflow_object.status).toBe(PayrollStatus.RECEIVER_APPROVED);
      // initial status + receiver_approved status
      expect(statusChangeEvents.workflow_object.events).toHaveLength(2);
      expect(statusChangeEvents.workflow_object.events[0]).toMatchObject({
        status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
        old_status: null,
        data: expect.objectContaining(getPayrollDataInfo(statusChangeEvents)),
        reason: ObjectWorkflowEventsReasons.PAYROLL_PROCESS,
      });
      expect(statusChangeEvents.workflow_object.events[1]).toMatchObject({
        status: PayrollStatus.RECEIVER_APPROVED,
        old_status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
        data: expect.objectContaining(getPayrollDataInfo(statusChangeEvents)),
        reason: ObjectWorkflowEventsReasons.USER_ACTION,
      });

      const { body: secondUpdateResponse } = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        payload: [
          {
            object_key: payrollDataId[0].object_key,
            current_status: PayrollStatus.PAID,
            new_status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
            hook_inputs: [],
          },
          {
            object_key: payrollDataId[1].object_key,
            current_status: PayrollStatus.RECEIVER_APPROVED,
            new_status: PayrollStatus.PAID,
            hook_inputs: [],
          },
          {
            object_key: payrollDataId[2].object_key,
            current_status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
            new_status: PayrollStatus.RECEIVER_APPROVED,
            hook_inputs: [],
          },
          {
            object_key: payrollDataId[3].object_key,
            current_status: PayrollStatus.RECEIVER_APPROVED,
            new_status: PayrollStatus.PAID,
            hook_inputs: [],
          },
        ],
      });

      expect(secondUpdateResponse).toMatchObject({
        errors: expect.arrayContaining([
          {
            object_key: payrollDataId[0].object_key,
            reason: StatusUpdateErrorReasons.INVALID_CURRENT_STATUS,
            type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
          },
          {
            object_key: payrollDataId[1].object_key,
            reason: StatusUpdateErrorReasons.INVALID_CURRENT_STATUS,
            type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
          },
          {
            object_key: payrollDataId[2].object_key,
            reason: BackendErrorCodes.INVALID_NEXT_STATUS,
            type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
            message: BackendErrors[StatusUpdateErrorReasons.INVALID_NEXT_STATUS].description,
          },
        ]),
      });

      await validateWorkflowObjectStatus(
        payrollDataId[0].id,
        PayrollStatus.PENDING_RECEIVER_APPROVAL,
      );
      await validateWorkflowObjectStatus(
        payrollDataId[1].id,
        PayrollStatus.PENDING_RECEIVER_APPROVAL,
      );
      await validateWorkflowObjectStatus(
        payrollDataId[2].id,
        PayrollStatus.PENDING_RECEIVER_APPROVAL,
      );
      await validateWorkflowObjectStatus(payrollDataId[3].id, PayrollStatus.PAID);

      await statusChangeEvents.reload();

      expect(statusChangeEvents.workflow_object.status).toBe(PayrollStatus.PAID);
      // initial status + receiver_approved status + paid status
      expect(statusChangeEvents.workflow_object.events).toHaveLength(3);
      expect(statusChangeEvents.workflow_object.events[2]).toMatchObject(
        expect.objectContaining({
          status: PayrollStatus.PAID,
          old_status: PayrollStatus.RECEIVER_APPROVED,
          data: expect.objectContaining(getPayrollDataInfo(statusChangeEvents)),
        }),
      );

      return Promise.resolve();
    });

    it('Should update status if allowed to', async () => {
      const payout_date = '2024-01-10';

      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
      ]);

      const { payroll, config } = await mockPaymentRequestPayroll(
        office.id_escritorio,
        payout_date,
      );

      const paymentRequest = await mockPaymentRequest(office, payroll, config, credor);

      const response1 = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        query: { type: ObjectWorkflowType.PAYMENT_REQUEST },
        payload: [
          {
            object_key: paymentRequest.workflow_object.object_key,
            new_status: PaymentRequestStatus.INVOICE_ISSUED,
            current_status: paymentRequest.workflow_object.status,
            hook_inputs: [],
          },
        ],
      });
      expect(response1.body).toStrictEqual({
        errors: [
          {
            object_key: paymentRequest.workflow_object.object_key,
            message: BackendErrors.INVALID_NEXT_STATUS.description,
            reason: BackendErrorCodes.INVALID_NEXT_STATUS,
            type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
          },
        ],
      });

      paymentRequest.workflow_object.input_data = {
        paymentAmount: 123,
        paymentDate: '2020-10-01',
      };
      await paymentRequest.workflow_object.save();

      await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        query: {
          type: ObjectWorkflowType.PAYMENT_REQUEST,
        },
        payload: [
          {
            object_key: paymentRequest.workflow_object.object_key,
            new_status: PaymentRequestStatus.INVOICE_ISSUED,
            current_status: paymentRequest.workflow_object.status,
            hook_inputs: [],
          },
        ],
      });

      // assert workflow status
      await paymentRequest.workflow_object.reload();
      expect(paymentRequest.workflow_object.status).toStrictEqual(
        PaymentRequestStatus.INVOICE_ISSUED,
      );
    });

    it('Should throw error if trying to change to an invalid status', async () => {
      const payout_date = '2024-01-10';

      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
      ]);

      const { payroll, config } = await mockPaymentRequestPayroll(
        office.id_escritorio,
        payout_date,
      );

      const paymentRequest = await mockPaymentRequest(office, payroll, config, credor);

      const response1 = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        query: { type: ObjectWorkflowType.PAYMENT_REQUEST },
        payload: [
          {
            object_key: paymentRequest.workflow_object.object_key,
            new_status: 'ALAGOINHAS',
            current_status: paymentRequest.workflow_object.status,
            hook_inputs: [],
          },
        ],
      });
      expect(response1.body).toStrictEqual({
        errors: [
          {
            object_key: paymentRequest.workflow_object.object_key,
            message: BackendErrors.INVALID_NEXT_STATUS.description,
            reason: BackendErrorCodes.INVALID_NEXT_STATUS,
            type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
          },
        ],
      });
    });

    it('Should not be able to update status from a not visible payment request', async () => {
      const payout_date = '2024-01-10';

      const { office } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
      ]);

      const { config } = await mockPaymentRequestPayroll(office.id_escritorio, payout_date);

      const another = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'TESTE',
        nome_credor: 'Teste',
      });

      const [paymentRequest] = await buildPaymentRequestWorkflow(office, config, another, {
        amount: 2000,
      });

      const response1 = await updateWorkflowStatus(
        app,
        {
          officeId: office.id_escritorio,
          query: { type: ObjectWorkflowType.PAYMENT_REQUEST },
          payload: [
            {
              object_key: paymentRequest.workflow_object.object_key,
              new_status: PaymentRequestStatus.INVOICE_ISSUED,
              current_status: paymentRequest.workflow_object.status,
              hook_inputs: [],
            },
          ],
        },
        HttpStatus.NOT_FOUND,
      );
      expect(response1.body).toStrictEqual({
        code: BackendErrorCodes.OBJECT_WORKFLOW_NOT_FOUND,
        message: BackendErrors[BackendErrorCodes.OBJECT_WORKFLOW_NOT_FOUND].description,
      });
    });

    it('Should process edge hooks in status change sending correctly payment request context', async () => {
      const payout_date = '2024-01-10';

      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
      ]);

      const { config } = await mockPaymentRequestPayroll(office.id_escritorio, payout_date);

      const responseData = { id: 123, key: '812' };
      outboundWebhookService.processWebhook = vi.fn().mockResolvedValue({
        success: true,
        response: responseData,
      });

      const outboundConfig = await Outbound_webhook_config.create({
        client_id: office.client_id,
        name: 'Test',
        url: 'http://localhost:3000',
        method: 'POST',
        auth: {},
        header_template: ``,
        body_template: ``,
      });

      const response_extractor = [
        {
          json_path: "$['xpto']",
          key: 'xpto',
        },
      ];
      await saveEdgeHooks(config, [
        {
          type: PayrollHookEventsType.OUTBOUND_WEBHOOKS,
          config_id: outboundConfig.id,
          response_extractor,
        },
      ]);

      const [paymentRequest] = await buildPaymentRequestWorkflow(office, config, credor, {
        amount: 1000,
        grouping: {
          Empreendimento: 'ABC123',
        },
        workflow_object: {
          status: PaymentRequestStatus.INVOICE_ISSUED,
          input_data: {
            paymentDate: '2024-01-01',
            paymentAmount: 1000,
          },
        },
      });

      await Payment_Data_Request.bulkCreate([
        {
          payment_request_id: paymentRequest.id,
          payroll_data_key: '"1"."A1"."G1"',
          payroll_data_info: {
            creditor_id: 'A1',
            amount: 700,
            grouping: 'G1',
            payroll_id: 1,
            metadata: [
              {
                Empreendimento: 'ABC123',
                TipoTP: 'NAR',
              },
            ],
          },
        },
        {
          payment_request_id: paymentRequest.id,
          payroll_data_key: `"1"."A1"."G2"`,
          payroll_data_info: {
            creditor_id: 'A1',
            amount: 300,
            grouping: 'G2',
            payroll_id: 1,
            metadata: [
              {
                Empreendimento: 'ABC123',
                TipoTP: 'NAR',
              },
            ],
          },
        },
      ]);

      const receiverContext = {
        key_1: 'value_1',
        key_2: 'value_2',
      };
      await Creditor_Data.create({
        creditor_id: credor.id_credor,
        data: {
          context: receiverContext,
        },
      });

      const response = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        query: { type: ObjectWorkflowType.PAYMENT_REQUEST },
        payload: [
          {
            object_key: paymentRequest.workflow_object.object_key,
            current_status: paymentRequest.workflow_object.status,
            new_status: PaymentRequestStatus.INVOICE_SENT,
            hook_inputs: [],
          },
        ],
      });

      expect(response.status).toStrictEqual(HttpStatus.OK);
      expect(outboundWebhookService.processWebhook).toBeCalledTimes(1);
      expect(outboundWebhookService.processWebhook).toBeCalledWith({
        configId: outboundConfig.id,
        clientId: office.client_id,
        response_extractor,
        context: {
          object_key: paymentRequest.request_key,
          external_data: null,
          requester: {
            can_see_receiver: true,
            is_payroll_status_manager: true,
            is_receiver: true,
            profiles: '',
            user_id: 'A1',
          },
          receiver: {
            user_id: 'A1',
            email: null,
            name: credor.nome_credor,
            payroll_date: undefined,
            data: receiverContext,
          },
          payment_request: {
            amount: '1000.00',
            key: paymentRequest.request_key,
            grouping: {
              Empreendimento: 'ABC123',
            },
            payments: [
              {
                amount: 700,
                grouping: 'G1',
                receiver_user_id: 'A1',
                payroll_date: undefined,
                metadata: {
                  TipoTP: 'NAR',
                  Empreendimento: 'ABC123',
                },
              },
              {
                amount: 300,
                grouping: 'G2',
                receiver_user_id: 'A1',
                payroll_date: undefined,
                metadata: {
                  TipoTP: 'NAR',
                  Empreendimento: 'ABC123',
                },
              },
            ],
          },
          inputs: {
            is_empty: false,
            is_complete: true,
            data: {
              paymentDate: '2024-01-01',
              paymentAmount: 1000,
            },
          },
        },
      });

      await paymentRequest.reload({ include: [Object_Workflow] });
      expect(paymentRequest.workflow_object.external_data).toStrictEqual(responseData);
    });

    it('Should try process edge hooks in status change sending correctly payment request context with `ASK_FOR_INPUT` hook', async () => {
      const payout_date = '2024-01-10';

      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
      ]);

      const { config } = await mockPaymentRequestPayroll(office.id_escritorio, payout_date);

      const responseData = { id: 123, key: '812' };
      outboundWebhookService.processWebhook = vi.fn().mockResolvedValue({
        success: true,
        response: responseData,
      });

      const outboundConfig = await Outbound_webhook_config.create({
        client_id: office.client_id,
        name: 'Test',
        url: 'http://localhost:3000',
        method: 'POST',
        auth: {},
        header_template: ``,
        body_template: ``,
      });

      const response_extractor = [
        {
          json_path: "$['xpto']",
          key: 'xpto',
        },
      ];
      await saveEdgeHooks(config, [
        {
          type: PayrollHookEventsType.ASK_FOR_INPUT,
          input_schema: {
            type: 'object',
            required: ['justificativa'],
            properties: {
              justificativa: {
                type: 'string',
                title: 'Justificativa',
              },
            },
          },
        },
        {
          type: PayrollHookEventsType.OUTBOUND_WEBHOOKS,
          config_id: outboundConfig.id,
          response_extractor,
        },
      ]);

      const [paymentRequest] = await buildPaymentRequestWorkflow(office, config, credor, {
        amount: 1000,
        grouping: {
          Empreendimento: 'ABC123',
        },
        workflow_object: {
          status: PaymentRequestStatus.INVOICE_ISSUED,
          input_data: {
            paymentDate: '2024-01-01',
            paymentAmount: 1000,
          },
        },
      });

      await Payment_Data_Request.bulkCreate([
        {
          payment_request_id: paymentRequest.id,
          payroll_data_key: '"1"."A1"."G1"',
          payroll_data_info: {
            creditor_id: 'A1',
            amount: 700,
            grouping: 'G1',
            payroll_id: 1,
            metadata: [
              {
                Empreendimento: 'ABC123',
                TipoTP: 'NAR',
              },
            ],
          },
        },
        {
          payment_request_id: paymentRequest.id,
          payroll_data_key: `"1"."A1"."G2"`,
          payroll_data_info: {
            creditor_id: 'A1',
            amount: 300,
            grouping: 'G2',
            payroll_id: 1,
            metadata: [
              {
                Empreendimento: 'ABC123',
                TipoTP: 'NAR',
              },
            ],
          },
        },
      ]);

      const receiverContext = {
        key_1: 'value_1',
        key_2: 'value_2',
      };
      await Creditor_Data.create({
        creditor_id: credor.id_credor,
        data: {
          context: receiverContext,
        },
      });

      const response = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        query: { type: ObjectWorkflowType.PAYMENT_REQUEST },
        payload: [
          {
            object_key: paymentRequest.workflow_object.object_key,
            current_status: paymentRequest.workflow_object.status,
            new_status: PaymentRequestStatus.INVOICE_SENT,
            hook_inputs: [
              {
                type: PayrollHookEventsType.ASK_FOR_INPUT,
                input: {
                  justificativa: 'Justificativa',
                },
              },
            ],
          },
        ],
      });

      expect(response.status).toStrictEqual(HttpStatus.OK);
      expect(outboundWebhookService.processWebhook).toBeCalledTimes(1);
      expect(outboundWebhookService.processWebhook).toBeCalledWith({
        configId: outboundConfig.id,
        clientId: office.client_id,
        response_extractor,
        context: {
          object_key: paymentRequest.request_key,
          external_data: null,
          requester: {
            can_see_receiver: true,
            is_payroll_status_manager: true,
            is_receiver: true,
            profiles: '',
            user_id: 'A1',
          },
          receiver: {
            user_id: 'A1',
            email: null,
            name: credor.nome_credor,
            payroll_date: undefined,
            data: receiverContext,
          },
          payment_request: {
            amount: '1000.00',
            key: paymentRequest.request_key,
            grouping: {
              Empreendimento: 'ABC123',
            },
            payments: [
              {
                amount: 700,
                grouping: 'G1',
                receiver_user_id: 'A1',
                payroll_date: undefined,
                metadata: {
                  TipoTP: 'NAR',
                  Empreendimento: 'ABC123',
                },
              },
              {
                amount: 300,
                grouping: 'G2',
                receiver_user_id: 'A1',
                payroll_date: undefined,
                metadata: {
                  TipoTP: 'NAR',
                  Empreendimento: 'ABC123',
                },
              },
            ],
          },
          inputs: {
            is_empty: false,
            is_complete: true,
            data: {
              paymentDate: '2024-01-01',
              paymentAmount: 1000,
              justificativa: 'Justificativa',
            },
          },
        },
      });

      await paymentRequest.reload({ include: [Object_Workflow] });
      expect(paymentRequest.workflow_object.external_data).toStrictEqual(responseData);
    });

    it('Should return error and not change status if hook fails', async () => {
      const payout_date = '2024-01-10';

      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
      ]);

      const { config } = await mockPaymentRequestPayroll(office.id_escritorio, payout_date);

      outboundWebhookService.processWebhook = vi
        .fn()
        .mockResolvedValue({ success: false, response: { error: 'some error' } });

      const graph = config.getParsedGraph() as PaymentRequestStatusGraph;
      const edgeAttrs = graph.getEdgeAttributes(
        graph.edge(PaymentRequestStatus.INVOICE_ISSUED, PaymentRequestStatus.INVOICE_SENT),
      );
      edgeAttrs.hooks = [
        {
          type: PayrollHookEventsType.OUTBOUND_WEBHOOKS,
          config_id: 1,
        },
      ];
      config.status_flow_config = graph as any;
      await config.save();

      const [paymentRequest] = await buildPaymentRequestWorkflow(office, config, credor, {
        workflow_object: {
          input_data: {
            paymentDate: '2024-01-01',
            paymentAmount: 1000,
          },
          status: PaymentRequestStatus.INVOICE_ISSUED,
        },
      });
      const eventsBeforeProcessing = paymentRequest.workflow_object.events ?? [];

      const response = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        query: { type: ObjectWorkflowType.PAYMENT_REQUEST },
        payload: [
          {
            object_key: paymentRequest.workflow_object.object_key,
            current_status: paymentRequest.workflow_object.status,
            hook_inputs: [],
            new_status: PaymentRequestStatus.INVOICE_SENT,
          },
        ],
      });
      expect(response.body).toStrictEqual({
        errors: [
          {
            object_key: paymentRequest.workflow_object.object_key,
            message: BackendErrors.PAYMENT_REQUEST_ERROR_CALLING_HOOK.description,
            reason: StatusUpdateErrorReasons.ERROR_ON_DISPATCH_HOOK,
            type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
            data: {
              error: 'some error',
            },
          },
        ],
      });
      expect(outboundWebhookService.processWebhook).toBeCalledTimes(1);

      await paymentRequest.reload({
        include: [Object_Workflow.scope('withEvents')],
      });

      // should not save external data if hook fails
      expect(paymentRequest.workflow_object.external_data).toBeNull();
      // assert workflow status
      expect(paymentRequest.workflow_object.status).toStrictEqual(
        PaymentRequestStatus.INVOICE_ISSUED,
      );
      expect(paymentRequest.workflow_object.events.length).toBe(eventsBeforeProcessing.length);
    });

    it('Should return the errors if unable to update status for some payment requests', async () => {
      const payout_date = '2024-01-10';

      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.FEATURE_PAYROLL_REQUESTS, valor: 'true' },
        { atributo: UsersClaims.PAYROLL_STATUS_MANAGER, valor: 'true' },
        { atributo: UsersClaims.GENERIC_DATA_IMPORT, valor: 'true' },
      ]);

      const { config } = await mockPaymentRequestPayroll(office.id_escritorio, payout_date);

      const [paymentRequest1, paymentRequest2, paymentRequest3] = await buildPaymentRequestWorkflow(
        office,
        config,
        credor,
        {
          workflow_object: {
            input_data: {
              paymentAmount: 123,
              paymentDate: '2020-10-01',
            },
          },
        },
        {
          amount: 100,
        },
        {
          amount: 100,
        },
      );

      const { body } = await updateWorkflowStatus(app, {
        officeId: office.id_escritorio,
        query: { type: ObjectWorkflowType.PAYMENT_REQUEST },
        payload: [
          {
            object_key: paymentRequest1.workflow_object.object_key,
            new_status: PaymentRequestStatus.INVOICE_ISSUED,
            current_status: paymentRequest1.workflow_object.status,
            hook_inputs: [],
          },
          {
            object_key: paymentRequest2.workflow_object.object_key,
            new_status: PaymentRequestStatus.INVOICE_ISSUED,
            current_status: paymentRequest2.workflow_object.status,
            hook_inputs: [],
          },
          {
            object_key: paymentRequest3.workflow_object.object_key,
            new_status: PaymentRequestStatus.INVOICE_ISSUED,
            current_status: paymentRequest3.workflow_object.status,
            hook_inputs: [],
          },
        ],
      });

      expect(body).toStrictEqual({
        errors: expect.arrayContaining([
          {
            object_key: paymentRequest2.workflow_object.object_key,
            message: BackendErrors.INVALID_NEXT_STATUS.description,
            reason: BackendErrorCodes.INVALID_NEXT_STATUS,
            type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
          },
          {
            object_key: paymentRequest3.workflow_object.object_key,
            message: BackendErrors.INVALID_NEXT_STATUS.description,
            reason: BackendErrorCodes.INVALID_NEXT_STATUS,
            type: StatusUpdateErrorTypes.STATUS_UPDATE_ERROR,
          },
        ]),
      });

      await Promise.all([
        paymentRequest1.reload({ include: [Object_Workflow] }),
        paymentRequest2.reload({ include: [Object_Workflow] }),
        paymentRequest3.reload({ include: [Object_Workflow] }),
      ]);

      // assert workflow status
      expect(paymentRequest1.workflow_object.status).toStrictEqual(
        PaymentRequestStatus.INVOICE_ISSUED,
      );
      expect(paymentRequest2.workflow_object.status).toStrictEqual(
        PaymentRequestStatus.WAITING_FOR_INVOICE,
      );
      expect(paymentRequest3.workflow_object.status).toStrictEqual(
        PaymentRequestStatus.WAITING_FOR_INVOICE,
      );
    });
  });
});
