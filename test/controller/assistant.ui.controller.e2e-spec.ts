import { AppModule } from '@app/app.module';
import { ConfigurationEnv } from '@app/config/configuration.env';
import { Credor } from '@app/models/credor';
import { SheetDbApiService } from '../../src/services/sheet.db.api.service';
import { faker } from '@faker-js/faker';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import { CommissionPlanResponse } from 'shared-types';
import { vi } from 'vitest';
import {
  setupMockLoginDomain,
  simulateLoggedUserBuilder,
  SimulateLoggedUserData,
} from '../mocks/login.domain.mock';
import { getAssistantUiToken } from '../utils/common.requests';
import { createTestOffice } from '../utils/massa.utils';

vi.mock('starkbank');

const sheetDbApiServiceMock = {
  getCommissionPlan: vi.fn(),
};

const config = new ConfigurationEnv();

describe('Assistant UI', () => {
  let app: INestApplication;
  let mockLoadAuthContext: ReturnType<typeof setupMockLoginDomain>;

  beforeAll(async () => {
    Object.defineProperty(config, 'tokenValidationEnabled', { value: false });

    const moduleBuilder = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .overrideProvider(SheetDbApiService)
      .useValue(sheetDbApiServiceMock);

    mockLoadAuthContext = setupMockLoginDomain(moduleBuilder);
    const module = await moduleBuilder.compile();
    module.useLogger(module.get(Logger));

    app = module.createNestApplication({ bodyParser: false });
    await app.init();

    vi.stubGlobal('fetch', vi.fn());
  });

  const simulateLoggedUser = (params: SimulateLoggedUserData) => {
    simulateLoggedUserBuilder(mockLoadAuthContext)(params);
  };

  describe('POST /assistant-ui/tokens', () => {
    it('should generate a token successfully in a commission plan', async () => {
      const office = await createTestOffice();
      const token = faker.string.uuid();
      const mockedFetch = vi.mocked(fetch);

      const loggedUser = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
        escritorio: office,
      });

      simulateLoggedUser({
        office,
        claims: [],
        credor: loggedUser,
      });

      mockedFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          token,
        }),
        clone: () => ({}),
      } as Response);

      const plan: CommissionPlanResponse = {
        id: faker.number.int(),
        payout_periods: [],
        name: 'PLAN NAME',
        config: {},
        created_at: faker.date.past().toISOString(),
        updated_at: faker.date.past().toISOString(),
      };

      sheetDbApiServiceMock.getCommissionPlan.mockResolvedValueOnce(plan);

      const response = await getAssistantUiToken(app, {
        body: {
          type: 'plan',
          planId: plan.id,
        },
        expectedHttpStatus: HttpStatus.CREATED,
      });

      expect(response.body.token).toEqual(token);
      expect(mockedFetch).toBeCalledWith(`${config.assistantUiBaseUrl}/v1/auth/tokens`, {
        headers: {
          'Aui-User-Id': `PLAN-CHAT_${office.id_escritorio}_${plan.id}_${loggedUser.id_credor}`,
          'Aui-Workspace-Id': `PLAN-CHAT_${office.id_escritorio}_${plan.id}_${loggedUser.id_credor}`,
          Authorization: expect.any(String),
          'Content-Type': 'application/json',
        },
        method: 'POST',
      });

      return Promise.resolve();
    });

    it('should throw an 404 error when trying to generate a token for a non existing plan', async () => {
      const office = await createTestOffice();
      const token = faker.string.uuid();
      const mockedFetch = vi.mocked(fetch);

      const loggedUser = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
        escritorio: office,
      });

      simulateLoggedUser({
        office,
        claims: [],
        credor: loggedUser,
      });

      mockedFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          token,
        }),
        clone: () => ({}),
      } as Response);

      sheetDbApiServiceMock.getCommissionPlan.mockResolvedValueOnce(undefined);

      await getAssistantUiToken(app, {
        body: {
          type: 'plan',
          planId: 1,
        },
        expectedHttpStatus: HttpStatus.NOT_FOUND,
      });

      return Promise.resolve();
    });

    it('should generate a token successfully in data source', async () => {
      const office = await createTestOffice();
      const token = faker.string.uuid();
      const mockedFetch = vi.mocked(fetch);

      const loggedUser = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });

      simulateLoggedUser({
        office,
        claims: [],
        credor: loggedUser,
      });

      mockedFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => ({
          token,
        }),
        clone: () => ({}),
      } as Response);

      const response = await getAssistantUiToken(app, {
        body: {
          type: 'data_source',
        },
        expectedHttpStatus: HttpStatus.CREATED,
      });

      expect(response.body.token).toEqual(token);
      expect(mockedFetch).toBeCalled();
      expect(mockedFetch).toBeCalledWith(`${config.assistantUiBaseUrl}/v1/auth/tokens`, {
        headers: {
          'Aui-User-Id': `PLAN-CHAT_${office.id_escritorio}_${loggedUser.id_credor}`,
          'Aui-Workspace-Id': `PLAN-CHAT_${office.id_escritorio}_${loggedUser.id_credor}`,
          Authorization: expect.any(String),
          'Content-Type': 'application/json',
        },
        method: 'POST',
      });
      expect(sheetDbApiServiceMock.getCommissionPlan).not.toBeCalled();

      return Promise.resolve();
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  afterAll(async () => {
    await app.close();
    await app.get('SEQUELIZE').close();
  });
});
