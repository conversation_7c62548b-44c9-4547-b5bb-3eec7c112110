import { vi } from 'vitest';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import { AppModule } from '../../src/app.module';
import { ConfigurationEnv } from '../../src/config/configuration.env';
import { Credor } from '../../src/models/credor';
import { getUserTenants } from '../utils/common.requests';
import {
  createTestOffice,
  createProvider,
  mockAccessToken,
  setupOrgLogin,
} from '../utils/massa.utils';
import { v4 as uuid } from 'uuid';
import { RoleConfig } from '@app/types/models/organization';
import { Escritorio } from '@app/models/escritorio';
import {
  fillRolesConfigForTests,
  johnsonsCases,
  makeTenantsForTests,
  roleConfigForJohnsonsCases,
  rolesCases,
} from './organization/stubs';

vi.mock('starkbank');

const config = new ConfigurationEnv();
describe('user (tenants route) controller', () => {
  let app: INestApplication;
  beforeAll(async () => {
    Object.defineProperty(config, 'tokenValidationEnabled', { value: true });

    const module = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .compile();
    module.useLogger(module.get(Logger));

    app = module.createNestApplication({ bodyParser: false });
    await app.init();
  });

  describe('validate [get] /user/tenants [CUSTOM LOGIN]', () => {
    it('returns empty array when the logged-in user when no have tenants(match email)', async () => {
      const office = await createTestOffice(undefined);
      const randIssuer = uuid();
      const oidc = await createProvider(
        office.id_escritorio,
        {
          issuer: randIssuer,
          default: false,
        },
        {
          identifier_type: 'office_id+creditor_id',
          user_info: {
            creditor_id: { claim: 'unique_name' },
          },
        },
      );

      const accessToken = mockAccessToken(oidc, {
        name: 'José',
        unique_name: 'A1',
      });

      const response = await getUserTenants(app, {
        token: accessToken,
      });

      expect(response.body).toEqual({
        tenants: [],
        user: { name: '', is_user_org: false },
      });
    });

    it('sucessfully return  tenants with creditor matched email', async () => {
      const office = await createTestOffice('First Office');
      await createTestOffice('Second Office');
      const thirdOffice = await createTestOffice('Third Office', undefined, false);
      const email = `${office.id_escritorio}<EMAIL>`;

      const officesWithCreditor = [office, thirdOffice];
      await Credor.bulkCreate(
        officesWithCreditor.map(o => ({
          escritorio_id_escritorio: o.id_escritorio,
          email,
          id_credor_externo: 'A1',
          active: 1,
        })),
      );

      const randIssuer = uuid();
      const oidc = await createProvider(
        office.id_escritorio,
        {
          issuer: randIssuer,
          default: false,
        },
        {
          office_id: office.id_escritorio,
          identifier_type: 'office_id+creditor_id',
          user_info: {
            creditor_id: { claim: 'unique_name' },
            name: { claim: 'name' },
          },
        },
      );

      const accessToken = mockAccessToken(oidc, { unique_name: 'A1', name: 'José' });

      const response = await getUserTenants(app, {
        token: accessToken,
      });

      expect(response.body).toEqual({
        tenants: [
          {
            name: office.nome_escritorio,
            id: office.id_escritorio,
          },
        ],
        user: { name: 'José', is_user_org: false },
      });
    });
  });

  describe('validate [get] /user/tenants [ORG LOGIN]', () => {
    it('return empty array when the logged-in user is not a member of any tenant', async () => {
      const { oidc, providerId, user } = await setupOrgLogin(
        {
          user_info: {
            provider_id: {
              claim: 'sub',
            },
            name: {
              claim: 'name',
            },
          },
        },
        { isAdmin: false },
      );
      const accessToken = mockAccessToken(oidc, { sub: providerId, name: user.name });

      const response = await getUserTenants(app, {
        token: accessToken,
      });
      expect(response.body).toEqual({
        user: {
          name: user.name,
          is_user_org: true,
        },
        tenants: [],
      });
    });

    it('validates tenants retrieval for organization with one and then two offices', async () => {
      const { oidc, providerId, office, user, org } = await setupOrgLogin({
        user_info: {
          provider_id: {
            claim: 'sub',
          },
        },
      });
      user.is_admin = true;
      await user.save();
      const accessToken = mockAccessToken(oidc, { sub: providerId });

      const response = await getUserTenants(app, {
        token: accessToken,
      });

      const expectedTenants = {
        user: { name: user.name, is_user_org: true },
        tenants: [
          {
            id: office.id_escritorio,
            name: office.nome_escritorio,
          },
        ],
      };

      expect(response.body).toEqual(expectedTenants);

      const secondOffice = await createTestOffice('Second Office belonging Org', org.id);

      const secondResponse = await getUserTenants(app, {
        token: accessToken,
      });

      expectedTenants.tenants.push({
        id: secondOffice.id_escritorio,
        name: 'Second Office belonging Org',
      });
      expect(secondResponse.body).toEqual(expectedTenants);
    });

    it.each(rolesCases)(
      'validates tenant retrieval for $description (without org.config => admin can see all tenants, non-admin can see only offices with creditor)',
      async ({ isAdmin, expectedOffices }) => {
        const { oidc, providerId, user, org } = await setupOrgLogin({
          identifier_type: 'office_id+creditor_id',
          user_info: {
            provider_id: {
              claim: 'sub',
            },
            creditor_id: {
              claim: 'creditor_id',
            },
          },
        });
        user.is_admin = isAdmin;
        await user.save();
        const accessToken = mockAccessToken(oidc, { sub: providerId, creditor_id: 'A1' });
        await createTestOffice('Second Office belonging Org', org.id);
        const thirdOffice = await createTestOffice('Thrid Office belonging Org', org.id);

        await Credor.create({
          escritorio_id_escritorio: thirdOffice.id_escritorio,
          id_credor_externo: 'A1',
          nome_credor: 'john kramer',
          email: `111${thirdOffice.id_escritorio}@email.com.br`,
          ativo: true,
        });

        const response = await getUserTenants(app, {
          token: accessToken,
        });

        expect(response.body.tenants).toHaveLength(expectedOffices);
      },
    );

    const nonAdminDoesntCanSeeAnyTenantCases = [
      {
        description: 'did not match any role config(user_info match)',
        creditorId: 'A3',
        userCreate: true,
      },
      {
        description:
          'did not match any tenant config(tenant_config match), filtered tenants return empty array',
        creditorId: 'A1',
        userCreate: true,
        rolesConfig: [
          {
            role: 'Never has tenants ',
            user_info_matches: [
              {
                claim: 'creditor_id',
                value: ['A1'],
              },
            ],
            allowed_tenants_matcher: '^Never has tenants$',
          },
        ],
      },
      {
        description:
          'did match tenant config(tenant_config match), but no has creditor already created',
        creditorId: 'A2',
        userCreate: false,
      },
    ];
    it.each(nonAdminDoesntCanSeeAnyTenantCases)(
      'return empty array when the user(non admin) $description, cannot see any tenant',
      async ({ creditorId, rolesConfig, userCreate }) => {
        const { oidc, providerId, org, user } = await setupOrgLogin(
          {
            identifier_type: 'office_id+creditor_id',
            create_user: userCreate,
            user_info: {
              provider_id: {
                claim: 'sub',
              },
              creditor_id: {
                claim: 'creditor_id',
              },
              name: {
                claim: 'name',
              },
            },
          },
          { isAdmin: false },
        );

        await makeTenantsForTests(org.id);
        await fillRolesConfigForTests(org, { rolesConfig });

        const accessToken = mockAccessToken(oidc, {
          sub: providerId,
          creditor_id: creditorId,
          name: user.name,
        });
        const response = await getUserTenants(app, {
          token: accessToken,
        });
        expect(response.body).toEqual({
          user: {
            name: user.name,
            is_user_org: true,
          },
          tenants: [],
        });
      },
    );

    // ADMIN USER - (1)UNMATCH AND (2)MATCH USER INFO - (3)HAS AND HASNT(2) TENANT_CONFIG(admin_only)
    const adminNotMatchCases = [
      {
        description: 'even if not match any role config(user_info_match)',
        creditorId: 'A3',
      },
      {
        description: 'even if not match any tenants config(tenant_config match)',
        creditorId: 'A1', // has user_info match
      },
      {
        description: 'even if match tenants config(tenant_config match)',
        creditorId: 'A1', // has user_info match and tenant config match
        rolesConfig: [
          {
            role: 'Admin',
            admin_only: true,
            user_info_matches: [
              {
                claim: 'creditor_id',
                value: ['A1'],
              },
            ],
          },
        ],
      },
    ];
    it.each(adminNotMatchCases)(
      'returns all tenants for user(admin), $description',
      async ({ creditorId, rolesConfig }) => {
        const { oidc, providerId, org, office } = await setupOrgLogin(
          {
            identifier_type: 'office_id+creditor_id',
            user_info: {
              provider_id: {
                claim: 'sub',
              },
              creditor_id: {
                claim: 'creditor_id',
              },
            },
          },
          { isAdmin: true },
        );

        const { allTenants } = await makeTenantsForTests(org.id);
        await fillRolesConfigForTests(org, { rolesConfig });

        const accessToken = mockAccessToken(oidc, { sub: providerId, creditor_id: creditorId });
        const response = await getUserTenants(app, {
          token: accessToken,
        });

        const allTenanstLen = [...allTenants, office.nome_escritorio].length;
        expect(response.body.tenants).toHaveLength(allTenanstLen);
      },
    );

    it('return only matched tenants for user(non admin), all tenants same not has creditor created', async () => {
      const { oidc, providerId, org } = await setupOrgLogin(
        {
          identifier_type: 'office_id+creditor_id',
          create_user: true,
          user_info: {
            provider_id: {
              claim: 'sub',
            },
            creditor_id: {
              claim: 'creditor_id',
            },
          },
        },
        { isAdmin: false },
      );
      const { managerTenants, subManagerTenants } = await makeTenantsForTests(org.id);
      await fillRolesConfigForTests(org, { createUser: true });

      const validateRequest = async (creditorId: string, expectedTenantsName: string[]) => {
        const accessToken = mockAccessToken(oidc, { sub: providerId, creditor_id: creditorId });

        const response = await getUserTenants(app, {
          token: accessToken,
        });

        expect(response.body.tenants).toHaveLength(expectedTenantsName.length);
        expect(response.body.tenants.map(o => o.name)).toEqual(expectedTenantsName);
      };

      await validateRequest('A1', subManagerTenants);
      await validateRequest('A2', managerTenants);
    });

    it('throw 500 when invalid json org.config', async () => {
      const { oidc, providerId, org } = await setupOrgLogin(
        {
          identifier_type: 'office_id+creditor_id',
          user_info: {
            provider_id: {
              claim: 'sub',
            },
            creditor_id: {
              claim: 'creditor_id',
            },
          },
        },
        { isAdmin: false },
      );
      const roleConfig = [{ invalid: 'json' }];
      await fillRolesConfigForTests(org, {
        rolesConfig: roleConfig as RoleConfig[],
      });

      const accessToken = mockAccessToken(oidc, { sub: providerId, creditor_id: 'A1' });

      const response = await getUserTenants(app, {
        token: accessToken,
        expectedHttpStatus: HttpStatus.INTERNAL_SERVER_ERROR,
      });

      expect(response.body).toEqual({
        message: 'invalid org configuration',
        error: 'Internal Server Error',
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      });
    });

    it.each(johnsonsCases)(
      'validate with johnsons org config',
      async ({ isAdmin, emails, shouldCreateCreditorsOn, expectedTenants }) => {
        const { oidc, providerId, org, office } = await setupOrgLogin(
          {
            identifier_type: 'email',
            create_user: false,
            user_info: {
              provider_id: {
                claim: 'sub',
              },
              email: {
                claim: 'email',
              },
            },
          },
          { isAdmin },
        );

        office.org_id = null;
        await office.save();
        const orgId = org.id;
        const tenantsName = [
          'Johnson BRA',
          'Johnson LAN',
          'Johnson LAS',
          'Johnson BRA - Staging',
          'Johnson LAN - Staging',
          'Johnson LAS - Staging',
        ];
        await Promise.all(tenantsName.map(tenantName => createTestOffice(tenantName, orgId)));

        if (shouldCreateCreditorsOn) {
          const tenants = await Escritorio.findAll({
            where: {
              org_id: org.id,
              nome_escritorio: shouldCreateCreditorsOn,
            },
          });
          await Promise.all(
            tenants.map(tenant =>
              Credor.create({
                escritorio_id_escritorio: tenant.id_escritorio,
                id_credor_externo: 'A1',
                email: emails[0],
                ativo: true,
                nome_credor: 'A1',
              }),
            ),
          );
        }

        const rolesConfig = roleConfigForJohnsonsCases;
        await fillRolesConfigForTests(org, { rolesConfig });

        const validateRequest = async (email: string, expectedTenantsName: string[]) => {
          const accessToken = mockAccessToken(oidc, { sub: providerId, email });

          const response = await getUserTenants(app, {
            token: accessToken,
          });

          expect(response.body.tenants).toHaveLength(expectedTenantsName.length);
          expect(response.body.tenants.map(o => o.name).sort((a, b) => a.localeCompare(b))).toEqual(
            expectedTenantsName.sort((a, b) => a.localeCompare(b)),
          );
        };

        await Promise.all(emails.map(async email => validateRequest(email, expectedTenants)));
      },
    );

    it('validates that the prop claims is normalized when matching the rules', async () => {
      const { oidc, providerId, org } = await setupOrgLogin(
        {
          identifier_type: 'office_id+creditor_id',
          create_user: true,
          user_info: {
            provider_id: {
              claim: 'sub',
            },
            creditor_id: {
              claim: 'creditor_id',
            },
            email: {
              claim: 'email',
            },
          },
        },
        { isAdmin: false },
      );
      await makeTenantsForTests(org.id);

      const rolesConfig = [
        {
          role: 'Admin',
          admin_only: true,
          user_info_matches: [
            {
              claim: 'email',
              value: ['<EMAIL>'],
            },
          ],
        },
        {
          role: 'Gabs Rule',
          admin_only: false,
          create_user: true,
          user_info_matches: [
            {
              claim: 'email',
              value: [' <EMAIL>  '],
            },
          ],
          allowed_tenants_matcher: '^First Office belonging Org$',
        },
      ];

      await fillRolesConfigForTests(org, { rolesConfig });

      const accessToken = mockAccessToken(oidc, {
        sub: providerId,
        creditor_id: 'A2',
        email: ' <EMAIL> ',
      });
      const response = await getUserTenants(app, {
        token: accessToken,
      });

      expect(response.body.tenants).toEqual([
        {
          id: expect.any(Number),
          name: 'First Office belonging Org',
        },
      ]);
    });
  });

  describe('validate [get] /user/tenants [DEFAULT LOGIN]', () => {
    it('returns empty array when the logged-in user when no have tenants(match email)', async () => {
      const office = await createTestOffice(undefined);
      const randIssuer = uuid();
      const oidc = await createProvider(office.id_escritorio, { issuer: randIssuer });
      const accessToken = mockAccessToken(oidc, {
        name: 'José',
        email: `${office.id_escritorio}-<EMAIL>`,
      });

      const response = await getUserTenants(app, {
        token: accessToken,
      });

      expect(response.body).toEqual({
        tenants: [],
        user: { name: '', is_user_org: false },
      });
    });

    it('sucessfully return  tenants with creditor matched email', async () => {
      const office = await createTestOffice('First Office');
      await createTestOffice('Second Office');
      const thirdOffice = await createTestOffice('Third Office', undefined, false);
      const email = `${office.id_escritorio}<EMAIL>`;

      const officesWithCreditor = [office, thirdOffice];
      await Credor.bulkCreate(
        officesWithCreditor.map(o => ({
          escritorio_id_escritorio: o.id_escritorio,
          email,
          id_credor_externo: 'A1',
          active: 1,
        })),
      );

      const randIssuer = uuid();
      const oidc = await createProvider(
        office.id_escritorio,
        { issuer: randIssuer },
        {
          user_info: {
            email: {
              claim: 'email',
            },
            name: {
              claim: 'name',
            },
          },
        },
      );

      const accessToken = mockAccessToken(oidc, { name: 'José', email });

      const response = await getUserTenants(app, {
        token: accessToken,
      });

      expect(response.body).toEqual({
        tenants: officesWithCreditor.map(o => ({ id: o.id_escritorio, name: o.nome_escritorio })),
        user: { name: 'José', is_user_org: false },
      });
    });
  });

  afterAll(async () => {
    await app.close();
    await app.get('SEQUELIZE').close();
  });
});
