import { vi, Mocked } from 'vitest';
import { AppModule } from '@app/app.module';
import { ConfigurationEnv } from '@app/config/configuration.env';
import { DocumentSignatureApiService } from '@app/document.signature/service/document.signature.api.service';
import { Creditor_Teams } from '@app/models/creditor_teams';
import { Credor } from '@app/models/credor';
import { Permission_Profile } from '@app/models/permission_profile';
import { User_Permission } from '@app/models/user_permission';
import AWSService from '@app/services/aws.sdk.service';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import {
  BackendSignRequest,
  ORIGINAL_USER_HEADER,
  CreatePacketsRequest,
  PacketResponse,
  PacketTagTypes,
  PacketTypeEnum,
  PatchPacketRequest,
  PermissionProfileResponse,
  PolicyAgreementStatusEnum,
  SignatureEngineTypeEnum,
  SignerStatusEnum,
  UsersClaims,
  POLICY_AGREEMENT_MANAGER_HEADER,
  OFFICE_NAME_HEADER,
  USER_LOCALE_HEADER,
  OFFICE_LOCALE_HEADER,
} from 'shared-types';
import {
  createPackets,
  patchPackets,
  createPermissionProfile,
  getPacket,
  getPackets,
  getPacketsToSign,
  signPacket,
  updatePermissions,
  deletePackets,
} from '../utils/common.requests';
import { createTeam, createTestOffice } from '../utils/massa.utils';
import uuid = require('uuid');
import { GaxiosResponse } from 'gaxios';
import {
  setupMockLoginDomain,
  simulateLoggedUserBuilder,
  SimulateLoggedUserData,
} from '../mocks/login.domain.mock';

vi.mock('../../src/services/aws.sdk.service');
vi.mock('starkbank');
const awsSdk = AWSService as unknown as Mocked<AWSService>;
const claimPolicyAgreementModule = {
  atributo: UsersClaims.POLICY_AGREEMENT_MODULE,
  valor: 'true',
};
const claimPolicyAgreementManager = {
  atributo: UsersClaims.POLICY_AGREEMENT_MANAGER,
  valor: 'true',
};

Object.defineProperty(awsSdk, 'cognito', { value: {} });

const config = new ConfigurationEnv();
describe('Document Signer', () => {
  let app: INestApplication;
  let mockLoadAuthContext: ReturnType<typeof setupMockLoginDomain>;

  beforeAll(async () => {
    Object.defineProperty(config, 'tokenValidationEnabled', { value: false });

    const moduleBuilder = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .overrideProvider(AWSService)
      .useValue(awsSdk);
    mockLoadAuthContext = setupMockLoginDomain(moduleBuilder);
    const module = await moduleBuilder.compile();
    module.useLogger(module.get(Logger));

    app = module.createNestApplication({ bodyParser: false });
    await app.init();
  });

  const simulateLoggedUser = (params: SimulateLoggedUserData) => {
    simulateLoggedUserBuilder(mockLoadAuthContext)(params);
  };

  beforeEach(() => {
    vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient').mockResolvedValue({
      data: {},
    } as any);
  });

  describe('GET /offices/:officeId/document-signer/packets', () => {
    it('validate call document-signer service, getting packets to sign', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementModule],
        credor: creditorLogged,
      });

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient').mockResolvedValue({
        data: { data: [], meta: { page: 1, page_size: 100, total: 0 } },
      } as GaxiosResponse);

      await getPacketsToSign(app, { office });

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets`,
        {
          headers: {
            [ORIGINAL_USER_HEADER]: creditorLogged.id_credor_externo,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'false',
          },
          method: 'GET',
          params: {
            status_values: [PolicyAgreementStatusEnum.RUNNING],
            status_mode: 'in',
            type: [PacketTypeEnum.POLICY_AGREEMENT],
            external_user_ids: [creditorLogged.id_credor_externo],
            signer_status: [SignerStatusEnum.PENDING],
          },
        },
      );
      return Promise.resolve();
    });

    it('validate call document-signer service, getting packets to sign when is policy agreement MANAGER', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [
          claimPolicyAgreementModule,
          {
            atributo: UsersClaims.POLICY_AGREEMENT_MANAGER,
            valor: 'true',
          },
        ],
        credor: creditorLogged,
      });

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient').mockResolvedValue({
        data: { data: [], meta: { page: 1, page_size: 100, total: 0 } },
      } as GaxiosResponse);

      await getPackets(app, { office });

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets`,
        {
          headers: {
            [ORIGINAL_USER_HEADER]: creditorLogged.id_credor_externo,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'true',
          },
          method: 'GET',
          params: {
            external_user_ids: [],
          },
        },
      );

      return Promise.resolve();
    });

    it('validate call document-signer service, getting packets to sign when not is policy agreement MANAGER', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementModule, claimPolicyAgreementManager],
        credor: creditorLogged,
      });

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient').mockResolvedValue({
        data: { data: [], meta: { page: 1, page_size: 100, total: 0 } },
      } as GaxiosResponse);

      await getPackets(app, { office });

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenLastCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets`,
        {
          headers: {
            [ORIGINAL_USER_HEADER]: creditorLogged.id_credor_externo,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'true',
          },
          method: 'GET',
          params: {
            external_user_ids: [],
          },
        },
      );

      const managerClaimFalse = Object.assign({}, claimPolicyAgreementManager);
      managerClaimFalse.valor = 'false';

      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementModule, managerClaimFalse],
        credor: creditorLogged,
      });

      await getPackets(app, { office });

      expect(spy).toHaveBeenCalledTimes(2);
      expect(spy).toHaveBeenLastCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets`,
        {
          headers: {
            [ORIGINAL_USER_HEADER]: creditorLogged.id_credor_externo,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'false',
          },
          method: 'GET',
          params: {
            external_user_ids: [creditorLogged.id_credor_externo],
          },
        },
      );

      return Promise.resolve();
    });
  });

  describe('GET /offices/:officeId/document-signer/packets/:packetId', () => {
    it('validate call document-signer service, get packet by id', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementModule],
        credor: creditorLogged,
      });

      const expectedResponse: PacketResponse = {
        id: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        documents: [],
        engine_type: SignatureEngineTypeEnum.ELETRONIC,
        name: 'Packet One',
        lock_application: false,
        packet_tags: [],
        packet_type: PacketTypeEnum.POLICY_AGREEMENT,
        status: PolicyAgreementStatusEnum.RUNNING,
      };

      const spy = vi
        .spyOn(DocumentSignatureApiService.prototype, 'httpClient')
        .mockResolvedValueOnce({ data: expectedResponse } as GaxiosResponse);

      const response = (await getPacket(app, { office, packetId: 1 })).body as PacketResponse;

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets/1`,
        {
          headers: {
            [ORIGINAL_USER_HEADER]: creditorLogged.id_credor_externo,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'false',
          },
          method: 'GET',
        },
      );

      expect(response).toEqual(expectedResponse);
      return Promise.resolve();
    });

    it('validate call document-signer service, get packet by id when logged user is MANAGER', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementModule, claimPolicyAgreementManager],
        credor: creditorLogged,
      });

      const expectedResponse: PacketResponse = {
        id: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        documents: [],
        engine_type: SignatureEngineTypeEnum.ELETRONIC,
        name: 'Packet One',
        lock_application: true,
        packet_tags: [],
        packet_type: PacketTypeEnum.POLICY_AGREEMENT,
        status: PolicyAgreementStatusEnum.RUNNING,
      };

      const spy = vi
        .spyOn(DocumentSignatureApiService.prototype, 'httpClient')
        .mockResolvedValueOnce({ data: expectedResponse } as GaxiosResponse);

      const response = (await getPacket(app, { office, packetId: 1 })).body as PacketResponse;

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets/1`,
        {
          headers: {
            [ORIGINAL_USER_HEADER]: creditorLogged.id_credor_externo,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'true',
          },
          method: 'GET',
        },
      );

      expect(response).toEqual(expectedResponse);
      return Promise.resolve();
    });
  });

  describe('POST /offices/:officeId/document-signer/packets/:packetId/sign', () => {
    it('fails because invalid payload', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementModule],
        credor: creditorLogged,
      });

      const response = (
        await signPacket(app, {
          office,
          packetId: 1,
          payload: {} as BackendSignRequest,
          expectStatus: HttpStatus.BAD_REQUEST,
        })
      ).body;

      expect(response).toEqual({
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation error: Required at "timezone"',
        error: 'Bad Request',
      });
      return Promise.resolve();
    });

    it('validate request to document signer', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementModule],
        credor: creditorLogged,
      });

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient');
      await signPacket(app, {
        office,
        packetId: 1,
        payload: { timezone: 'GMT-3' },
      });

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets/1/signers/${creditorLogged.id_credor_externo}/sign`,
        {
          method: 'POST',
          body: {
            timezone: 'GMT-3',
            ip_address: '::ffff:127.0.0.1',
            timestamp: expect.any(String),
          },
          headers: {
            [ORIGINAL_USER_HEADER]: creditorLogged.id_credor_externo,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'false',
          },
        },
      );
      return Promise.resolve();
    });
  });

  describe('POST /offices/:officeId/document-signer/packets', () => {
    it('throw 403 when not has POLICY_AGREEMENT_MANAGER', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementModule],
        credor: creditorLogged,
      });

      await createPackets(app, {
        office,
        payload: {
          items: [
            {
              name: 'My test',
              packet_type: PacketTypeEnum.POLICY_AGREEMENT,
              tags: [],
            },
          ],
        },
        expectStatus: HttpStatus.FORBIDDEN,
      });
      return Promise.resolve();
    });

    it('validate request to document signer api', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementManager],
        credor: creditorLogged,
      });

      const tags = [
        {
          tag_type: PacketTagTypes.profile,
          tag_value: '1',
        },
        {
          tag_type: PacketTagTypes.team,
          tag_value: 'team_2',
        },
      ];

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient');
      await createPackets(app, {
        office,
        payload: {
          items: [
            {
              name: 'My test',
              packet_type: PacketTypeEnum.POLICY_AGREEMENT,
              tags,
            },
          ],
        },
      });

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets`,
        {
          method: 'POST',
          body: {
            items: [
              {
                name: 'My test',
                packet_type: PacketTypeEnum.POLICY_AGREEMENT,
                tags,
                signers: [],
              },
            ],
          },
        },
      );
      return Promise.resolve();
    });

    it('should get correctly all creditors from tags', async () => {
      const office = await createTestOffice();
      const [creditorLogged] = await Promise.all([
        Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'A1',
          ativo: true,
          nome_credor: 'Creditor One',
          email: `${uuid()}<EMAIL>`,
        }),
        Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'A2',
          ativo: true,
          nome_credor: 'Creditor Two',
          email: `${uuid()}<EMAIL>`,
        }),
        Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'A3',
          ativo: true,
          nome_credor: 'Creditor Two',
        }),
      ]);

      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementManager],
        credor: creditorLogged,
      });

      const [firstProfile, _secondProfile] = await Promise.all([
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_1',
          priority: 1,
          permission_data: [],
        }),
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_2',
          priority: 0,
          permission_data: [],
        }),
      ]);

      await Promise.all([
        User_Permission.create({
          creditor_id: creditorLogged.id_credor,
          permission_profile_id: firstProfile.id,
        }),
      ]);

      const tags = [
        {
          tag_type: PacketTagTypes.profile,
          tag_value: String(firstProfile.id),
        },
        {
          tag_type: PacketTagTypes.team,
          tag_value: 'team_2',
        },
      ];

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient');
      await createPackets(app, {
        office,
        payload: {
          items: [
            {
              name: 'My test',
              packet_type: PacketTypeEnum.POLICY_AGREEMENT,
              tags,
            },
          ],
        },
      });

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets`,
        {
          method: 'POST',
          body: {
            items: [
              {
                name: 'My test',
                packet_type: PacketTypeEnum.POLICY_AGREEMENT,
                tags,
                signers: [
                  {
                    creditor_id: creditorLogged.id_credor_externo,
                    email: creditorLogged.email,
                    name: creditorLogged.nome_credor,
                  },
                ],
              },
            ],
          },
        },
      );
      return Promise.resolve();
    });

    it('should create a packet with unioun of team A (A1, A2, A4), team MANAGERS (M1, M2, M3, A6) and profile GERENTES (A4, A5, A6 & MANAGERS)', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({
        office,
        claims: [
          { atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' },
          { atributo: UsersClaims.OFFICE_CREDITORS_VIEW, valor: 1 },
          {
            atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
            valor: true,
          },
          {
            atributo: UsersClaims.POLICY_AGREEMENT_MANAGER,
            valor: true,
          },
        ],
      });
      const externalCreditorIds = ['A3', 'A4', 'A5', 'A6', 'M1', 'M2', 'M3', 'M4', 'A1', 'A2'];
      const creditors = await Credor.bulkCreate(
        externalCreditorIds.map(creditor => ({
          id_credor_externo: creditor,
          escritorio_id_escritorio: office.id_escritorio,
          ativo: true,
          nome_credor: creditor,
          email: `${creditor}@${uuid()}.com`,
        })),
      );

      const creditorsMap = new Map<string, Credor>();
      creditors.forEach(creditor => {
        creditorsMap.set(creditor.id_credor_externo, creditor);
      });

      const team = await createTeam(office.id_escritorio, 'TEAM A');
      const managerTeam = await createTeam(office.id_escritorio, 'MANAGER TEAM');

      const creditorsOnTeamA = [
        creditorsMap.get('A1')!,
        creditorsMap.get('A2')!,
        creditorsMap.get('A4')!,
      ];
      const creditorsOnTeamManagers = [
        creditorsMap.get('M1')!,
        creditorsMap.get('M2')!,
        creditorsMap.get('M3')!,
        creditorsMap.get('A6')!,
      ];
      await Creditor_Teams.bulkCreate([
        ...creditorsOnTeamA.map(creditor => ({
          creditor_id: creditor.id_credor,
          team_id: team.id,
        })),
        ...creditorsOnTeamManagers.map(creditor => ({
          creditor_id: creditor.id_credor,
          team_id: managerTeam.id,
        })),
      ]);

      // setup manger profile
      const permissionProfileResponse = (
        await createPermissionProfile(app, {
          officeId: office.id_escritorio,
          payload: {
            name: 'MANAGERS',
            permission_data: [
              {
                name: 'office.creditors.view',
                value: 'true',
              },
            ],
          },
        })
      ).body as PermissionProfileResponse;

      await updatePermissions(app, {
        officeId: office.id_escritorio,
        permissionProfileId: permissionProfileResponse.id,
        payload: {
          creditor_ids: [
            creditorsMap.get('A4')!.id_credor,
            creditorsMap.get('A5')!.id_credor,
            creditorsMap.get('A6')!.id_credor,
          ],
          team_ids: [managerTeam.id],
        },
      });

      const payload: CreatePacketsRequest = {
        items: [
          {
            name: 'My Packet',
            packet_type: PacketTypeEnum.POLICY_AGREEMENT,
            tags: [
              {
                tag_type: PacketTagTypes.team,
                tag_value: team.name,
              },
              {
                tag_type: PacketTagTypes.team,
                tag_value: managerTeam.name,
              },
              {
                tag_type: PacketTagTypes.profile,
                tag_value: String(permissionProfileResponse.id),
              },
            ],
          },
        ],
      };
      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient');
      spy.mockClear();
      await createPackets(app, {
        office,
        payload,
      });

      const signers = Array.from(creditorsMap.values())
        .map(creditor => {
          if (['A1', 'A2', 'A3', 'A5', 'M4'].includes(creditor.id_credor_externo)) return;
          return {
            creditor_id: creditor.id_credor_externo,
            email: creditor.email,
            name: creditor.nome_credor,
          };
        })
        .filter(Boolean);
      const expectedBody: CreatePacketsRequest = {
        items: [
          {
            name: 'My Packet',
            packet_type: PacketTypeEnum.POLICY_AGREEMENT,
            tags: [
              {
                tag_type: PacketTagTypes.team,
                tag_value: team.name,
              },
              {
                tag_type: PacketTagTypes.team,
                tag_value: managerTeam.name,
              },
              {
                tag_type: PacketTagTypes.profile,
                tag_value: String(permissionProfileResponse.id),
              },
            ],
            signers: expect.anything(),
          },
        ],
      };
      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets`,
        {
          method: 'POST',
          body: expectedBody,
        },
      );
      const calledWith = spy.mock.calls[0][1].body as CreatePacketsRequest;
      expect(calledWith.items[0].signers).toHaveLength(signers.length);
      calledWith.items[0].signers.forEach(signer => {
        expect(signers).toContainEqual(signer);
      });

      return Promise.resolve();
    });

    it('should create a packet with the incrementing tags with the TEAMS from PERMISSION_PROFILES', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({
        office,
        claims: [
          { atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' },
          { atributo: UsersClaims.OFFICE_CREDITORS_VIEW, valor: 1 },
          {
            atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
            valor: true,
          },
          {
            atributo: UsersClaims.POLICY_AGREEMENT_MANAGER,
            valor: true,
          },
        ],
      });

      const [_, teamB, teamC] = await Promise.all([
        createTeam(office.id_escritorio, 'TEAM A'),
        createTeam(office.id_escritorio, 'TEAM B'),
        createTeam(office.id_escritorio, 'TEAM C'),
      ]);

      // setup manger profile
      const permissionProfileResponse = (
        await createPermissionProfile(app, {
          officeId: office.id_escritorio,
          payload: {
            name: 'MANAGERS',
            permission_data: [
              {
                name: 'office.creditors.view',
                value: 'true',
              },
            ],
          },
        })
      ).body as PermissionProfileResponse;

      await updatePermissions(app, {
        officeId: office.id_escritorio,
        permissionProfileId: permissionProfileResponse.id,
        payload: {
          creditor_ids: [],
          team_ids: [teamB.id, teamC.id],
        },
      });

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient');
      spy.mockClear();

      const payload: CreatePacketsRequest = {
        items: [
          {
            name: 'My Packet',
            packet_type: PacketTypeEnum.POLICY_AGREEMENT,
            tags: [
              {
                tag_type: PacketTagTypes.team,
                tag_value: 'TEAM A',
              },
              {
                tag_type: PacketTagTypes.profile,
                tag_value: String(permissionProfileResponse.id),
              },
              {
                tag_type: PacketTagTypes.team,
                tag_value: 'TEAM B',
              },
            ],
          },
        ],
      };
      await createPackets(app, {
        office,
        payload,
      });

      const expectedBody: CreatePacketsRequest = {
        items: [
          {
            name: 'My Packet',
            packet_type: PacketTypeEnum.POLICY_AGREEMENT,
            tags: [
              {
                tag_type: PacketTagTypes.team,
                tag_value: 'TEAM A',
              },
              {
                tag_type: PacketTagTypes.profile,
                tag_value: String(permissionProfileResponse.id),
              },
              {
                tag_type: PacketTagTypes.team,
                tag_value: 'TEAM B',
              },
              {
                tag_type: PacketTagTypes.team,
                tag_value: 'TEAM C',
              },
            ],
            signers: [],
          },
        ],
      };

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets`,
        {
          method: 'POST',
          body: expectedBody,
        },
      );

      return Promise.resolve();
    });
  });

  describe('PATCH offices/:officeId/document-signer/packets/:packetId', () => {
    it('should patch a packet incrementing tags with the TEAMS from PERMISSION_PROFILES', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({
        office,
        claims: [
          { atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' },
          { atributo: UsersClaims.OFFICE_CREDITORS_VIEW, valor: 1 },
          {
            atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
            valor: true,
          },
          {
            atributo: UsersClaims.POLICY_AGREEMENT_MANAGER,
            valor: true,
          },
        ],
      });

      const [_, teamB, teamC, teamD] = await Promise.all([
        createTeam(office.id_escritorio, 'TEAM A'),
        createTeam(office.id_escritorio, 'TEAM B'),
        createTeam(office.id_escritorio, 'TEAM C'),
        createTeam(office.id_escritorio, 'TEAM D'),
      ]);

      // setup manger profile
      const permissionProfileA = (
        await createPermissionProfile(app, {
          officeId: office.id_escritorio,
          payload: {
            name: 'Profile A',
            permission_data: [
              {
                name: 'office.creditors.view',
                value: 'true',
              },
            ],
          },
        })
      ).body as PermissionProfileResponse;

      const permissionProfileB = (
        await createPermissionProfile(app, {
          officeId: office.id_escritorio,
          payload: {
            name: 'Profile B',
            permission_data: [
              {
                name: 'office.creditors.view',
                value: 'true',
              },
            ],
            inherit_profile_id: permissionProfileA.id,
          },
        })
      ).body as PermissionProfileResponse;

      await Promise.all([
        updatePermissions(app, {
          officeId: office.id_escritorio,
          permissionProfileId: permissionProfileA.id,
          payload: {
            creditor_ids: [],
            team_ids: [teamB.id, teamC.id],
          },
        }),
        updatePermissions(app, {
          officeId: office.id_escritorio,
          permissionProfileId: permissionProfileB.id,
          payload: {
            creditor_ids: [],
            team_ids: [teamC.id, teamD.id],
          },
        }),
      ]);

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient');
      spy.mockClear();

      const payload: PatchPacketRequest = {
        name: 'My Packet',
        tags: [
          {
            tag_type: PacketTagTypes.profile,
            tag_value: String(permissionProfileA.id),
          },
          {
            tag_type: PacketTagTypes.profile,
            tag_value: String(permissionProfileB.id),
          },
        ],
      };
      await patchPackets(app, {
        office,
        payload,
        packetId: 1,
      });

      const expectedBody: PatchPacketRequest = {
        name: 'My Packet',
        tags: [
          {
            tag_type: PacketTagTypes.profile,
            tag_value: String(permissionProfileA.id),
          },
          {
            tag_type: PacketTagTypes.team,
            tag_value: 'TEAM B',
          },
          {
            tag_type: PacketTagTypes.team,
            tag_value: 'TEAM C',
          },
          {
            tag_type: PacketTagTypes.profile,
            tag_value: String(permissionProfileB.id),
          },
          {
            tag_type: PacketTagTypes.team,
            tag_value: 'TEAM D',
          },
        ],
        creditors: [],
      };

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets/1`,
        {
          method: 'PATCH',
          body: expectedBody,
          headers: {
            [ORIGINAL_USER_HEADER]: undefined,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'false',
          },
        },
      );

      return Promise.resolve();
    });

    it('should do not increment tags with TEAMS from PERMISSION_PROFILES if TEAMS is already in tags', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({
        office,
        claims: [
          { atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' },
          { atributo: UsersClaims.OFFICE_CREDITORS_VIEW, valor: 1 },
          {
            atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
            valor: true,
          },
          {
            atributo: UsersClaims.POLICY_AGREEMENT_MANAGER,
            valor: true,
          },
        ],
      });

      const [teamA, teamB, teamC] = await Promise.all([
        createTeam(office.id_escritorio, 'TEAM A'),
        createTeam(office.id_escritorio, 'TEAM B'),
        createTeam(office.id_escritorio, 'TEAM C'),
      ]);

      // setup manger profile
      const permissionProfileA = (
        await createPermissionProfile(app, {
          officeId: office.id_escritorio,
          payload: {
            name: 'Profile A',
            permission_data: [
              {
                name: 'office.creditors.view',
                value: 'true',
              },
            ],
          },
        })
      ).body as PermissionProfileResponse;

      await updatePermissions(app, {
        officeId: office.id_escritorio,
        permissionProfileId: permissionProfileA.id,
        payload: {
          creditor_ids: [],
          team_ids: [teamB.id, teamC.id],
        },
      });
      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient');
      spy.mockClear();

      const payload: PatchPacketRequest = {
        name: 'My Packet',
        tags: [
          {
            tag_type: PacketTagTypes.profile,
            tag_value: String(permissionProfileA.id),
          },
          {
            tag_type: PacketTagTypes.team,
            tag_value: String(teamA.name),
          },
        ],
      };
      await patchPackets(app, {
        office,
        payload,
        packetId: 1,
      });

      const expectedBody: PatchPacketRequest = {
        name: 'My Packet',
        tags: [
          {
            tag_type: PacketTagTypes.profile,
            tag_value: String(permissionProfileA.id),
          },
          {
            tag_type: PacketTagTypes.team,
            tag_value: 'TEAM A',
          },
        ],
        creditors: [],
      };

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets/1`,
        {
          method: 'PATCH',
          body: expectedBody,
          headers: {
            [ORIGINAL_USER_HEADER]: undefined,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'false',
          },
        },
      );

      return Promise.resolve();
    });

    it('should not send creditors and tag if not pass tags field', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({
        office,
        claims: [
          {
            atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
            valor: true,
          },
          {
            atributo: UsersClaims.POLICY_AGREEMENT_MANAGER,
            valor: true,
          },
        ],
      });

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient');
      spy.mockClear();

      const payload: PatchPacketRequest = {
        name: 'My Packet',
      };
      await patchPackets(app, {
        office,
        payload,
        packetId: 1,
      });

      const expectedBody: PatchPacketRequest = {
        name: 'My Packet',
      };

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets/1`,
        {
          method: 'PATCH',
          body: expectedBody,
          headers: {
            [ORIGINAL_USER_HEADER]: undefined,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'false',
          },
        },
      );

      return Promise.resolve();
    });
  });

  describe('DELETE /offices/:officeId/document-signer/packets', () => {
    it('validate call document-signer service, delete packets', async () => {
      const office = await createTestOffice();
      const creditorLogged = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A1',
        ativo: true,
        nome_credor: 'Creditor One',
      });
      simulateLoggedUser({
        office,
        claims: [claimPolicyAgreementModule, claimPolicyAgreementManager],
        credor: creditorLogged,
      });

      const spy = vi.spyOn(DocumentSignatureApiService.prototype, 'httpClient');
      await deletePackets(app, { office, packet_ids: [1], expectedStatus: HttpStatus.NO_CONTENT });

      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        `${config.documentSignatureUrl}/offices/${office.client_id}/packets`,
        {
          method: 'DELETE',
          body: { packet_ids: [1] },
          headers: {
            [ORIGINAL_USER_HEADER]: creditorLogged.id_credor_externo,
            [POLICY_AGREEMENT_MANAGER_HEADER]: 'true',
          },
        },
      );
      return Promise.resolve();
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  afterAll(async () => {
    await app.close();
    await app.get('SEQUELIZE').close();
  });
});
