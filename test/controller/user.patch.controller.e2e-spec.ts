import { DocumentSignatureApiService } from '@app/document.signature/service/document.signature.api.service';
import { NON_EXISTING_USER_ERROR } from '@app/domain/repository/userRepository';
import { Creditor_Teams } from '@app/models/creditor_teams';
import { Permission_Profile } from '@app/models/permission_profile';
import { Teams } from '@app/models/teams';
import { User_Identifier } from '@app/models/user_identifier';
import { User_Permission } from '@app/models/user_permission';
import { CustomException } from '@app/utils/error.utils';
import { sanitizeUsername } from '@app/utils/string.utils';
import { faker } from '@faker-js/faker';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import bodyParser from 'body-parser';
import { Logger } from 'nestjs-pino';
import {
  BackendErrorCodes,
  PacketTagTypes,
  PermissionProfileResponse,
  User,
  UsersClaims,
} from 'shared-types';
import { vi, type Mocked } from 'vitest';
import { AppModule } from '../../src/app.module';
import { ConfigurationEnv } from '../../src/config/configuration.env';
import { Atributos_credor } from '../../src/models/atributos_credor';
import { Credor } from '../../src/models/credor';
import AWSService from '../../src/services/aws.sdk.service';
import {
  setupMockLoginDomain,
  simulateLoggedUserBuilder,
  SimulateLoggedUserData,
} from '../mocks/login.domain.mock';
import { documentSignatureApiServiceStub } from '../stubs/document.signature.api.service';
import {
  createPermissionProfile,
  createUserEndpoint,
  getOfficeTeamsEndpoint,
  getUserEndpoint,
  updateClaims,
  updateUserEndpoint,
  updateUserPassword,
} from '../utils/common.requests';
import {
  createProfiles,
  createTestOffice,
  mockCognitoReturnSuccess,
  processComissionClosureFile,
  uploadFile,
  validateUserPermissions,
} from '../utils/massa.utils';
import uuid = require('uuid');

vi.mock('../../src/services/aws.sdk.service');
vi.mock('starkbank');

const getSessionMock = vi.fn((): any => ({ status: 'active', userId: 'any_user_id' }));
const getUserMock = vi.fn((): any => ({ id: 'any_id', username: 'any_username' }));
const updateUserMock = vi.fn();

vi.mock('@clerk/backend', async () => ({
  createClerkClient: vi.fn(() => ({
    sessions: {
      getSession: getSessionMock,
    },
    users: {
      getUser: getUserMock,
      updateUser: updateUserMock,
    },
  })),
}));

const loggedUserClaims = [
  { atributo: UsersClaims.USER_CREATE, valor: '1' },
  { atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' },
];

const userToCreateClaims = [
  { name: UsersClaims.USER_CREATE, value: 'true' },
  { name: UsersClaims.CLOSURE_UPDATE, value: 'true' },
];

const claimPolicyAgreementModule = {
  atributo: UsersClaims.POLICY_AGREEMENT_MODULE,
  valor: 'true',
};

const noUserCreationClaim = [{ atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' }];
const awsSdk = AWSService as unknown as Mocked<AWSService>;

const mockCognitoUserDoesntExist = (userName: string) => {
  awsSdk.cognito.adminGetUser = vi.fn().mockReturnValue({
    promise: vi.fn().mockRejectedValue({ code: NON_EXISTING_USER_ERROR }),
  });
};

const mockCognitoReturnExistingUser = (userName: string, email?: string) => {
  awsSdk.cognito.adminGetUser = vi.fn().mockReturnValue({
    promise: vi
      .fn()
      .mockResolvedValue({ Username: userName, UserAttributes: [{ Name: 'email', Value: email }] }),
  });
};

const mockCognitoCreateReturnUsername = (userName?: string) => {
  awsSdk.cognito.adminCreateUser = vi.fn().mockReturnValue({
    promise: vi.fn().mockImplementation(() => ({ User: { Username: userName ?? uuid() } })),
  });
};

// const mockSesSendInvitationMailSuccess = () => {
//   awsSdk.ses.sendBulkTemplatedEmail = vi.fn().mockReturnValue({
//     promise: vi.fn().mockResolvedValue('OK'),
//   });
// };

const config = new ConfigurationEnv();
describe('User PATCH (e2e)', () => {
  let app: INestApplication;
  let mockLoadAuthContext: ReturnType<typeof setupMockLoginDomain>;

  beforeAll(async () => {
    Object.defineProperty(config, 'tokenValidationEnabled', { value: false });
    Object.defineProperty(awsSdk, 'cognito', { value: {} });
    Object.defineProperty(awsSdk, 'ses', { value: {} });
    awsSdk.cognito.adminCreateUser = vi.fn().mockReturnValue({
      promise: () => vi.fn().mockResolvedValue('OK'),
    });
    awsSdk.cognito.adminDeleteUser = vi.fn().mockReturnValue({
      promise: () => vi.fn().mockResolvedValue('OK'),
    });
    awsSdk.cognito.adminResetUserPassword = vi.fn().mockReturnValue({
      promise: () => vi.fn().mockResolvedValue('OK'),
    });
    awsSdk.cognito.adminDeleteUserAttributes = vi.fn().mockReturnValue({
      promise: () => vi.fn().mockResolvedValue('OK'),
    });
    awsSdk.cognito.adminUpdateUserAttributes = vi.fn().mockReturnValue({
      promise: () => vi.fn().mockResolvedValue('OK'),
    });
    awsSdk.ses.sendBulkTemplatedEmail = vi.fn().mockReturnValue({
      promise: () => vi.fn().mockResolvedValue('OK'),
    });

    const moduleBuilder = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .overrideProvider(AWSService)
      .useValue(awsSdk)
      .overrideProvider(DocumentSignatureApiService)
      .useValue(documentSignatureApiServiceStub);

    mockLoadAuthContext = setupMockLoginDomain(moduleBuilder);
    const module = await moduleBuilder.compile();
    module.useLogger(module.get(Logger));

    app = module.createNestApplication({ bodyParser: false });
    app.use(bodyParser.json({ limit: '50mb' }));
    app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
    await app.init();
  });

  const simulateLoggedUser = (params: SimulateLoggedUserData) => {
    simulateLoggedUserBuilder(mockLoadAuthContext)(params);
  };

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('When user doesnt have required permissions', () => {
    it('Returns Forbidden', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: noUserCreationClaim });
      await updateUserEndpoint(app, office, 'A1', {}, HttpStatus.FORBIDDEN);
      return Promise.resolve();
    });
  });

  describe('When creditor ID exists on office', () => {
    describe('And tries to set email', () => {
      describe('But remote oauth user doesnt exist', () => {
        it('Doesnt update local state and returns NOTFOUND', async () => {
          const userToUpdate = 'NOT_FOUND_USER_EMAIL';
          mockCognitoUserDoesntExist('<EMAIL>');
          mockCognitoUserDoesntExist('<EMAIL>');

          const office = await createTestOffice();

          simulateLoggedUser({ office, claims: loggedUserClaims });
          mockCognitoReturnSuccess();
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: `${office.id_escritorio}-<EMAIL>`,
            },
            HttpStatus.CREATED,
          );

          await updateClaims(app, {
            officeId: office.id_escritorio,
            externalCreditorId: userToUpdate,
            payload: userToCreateClaims,
          });

          await updateUserEndpoint(
            app,
            office,
            userToUpdate + 'ASD',
            {
              name: 'Lucas Segers Fabro',
              email: `${office.id_escritorio}-<EMAIL>`,
              active: true,
              reset_password: false,
            },
            HttpStatus.NOT_FOUND,
          );

          const updatedUser = (await getUserEndpoint(app, office, userToUpdate)).body as User;

          validateUserPermissions(updatedUser.permissions, [
            { name: UsersClaims.CLOSURE_UPDATE, value: 'true' },
            { name: UsersClaims.USER_CREATE, value: 'true' },
          ]);

          return Promise.resolve();
        });
      });

      describe('But email already exists for another user', () => {
        it('Returns conflict', async () => {
          const office = await createTestOffice();
          const userToUpdate = 'ALREADY_EXISTING_EMAIL';

          const existentEmail = `${office.id_escritorio}-<EMAIL>`;
          const anotherExistentEmail = `${office.id_escritorio}-<EMAIL>`;

          mockCognitoReturnSuccess();
          simulateLoggedUser({ office, claims: loggedUserClaims });
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: existentEmail,
            },
            HttpStatus.CREATED,
          );

          await updateClaims(app, {
            officeId: office.id_escritorio,
            externalCreditorId: userToUpdate,
            payload: userToCreateClaims,
          });

          const createdUser = (await getUserEndpoint(app, office, userToUpdate)).body as User;

          validateUserPermissions(createdUser.permissions, [
            { name: UsersClaims.CLOSURE_UPDATE, value: 'true' },
            { name: UsersClaims.USER_CREATE, value: 'true' },
          ]);

          mockCognitoReturnSuccess();

          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: 'ANOTHER_MF',
              name: 'Lucas Segers Fabro',
              email: anotherExistentEmail,
            },
            HttpStatus.CREATED,
          );

          await updateClaims(app, {
            officeId: office.id_escritorio,
            externalCreditorId: 'ANOTHER_MF',
            payload: userToCreateClaims,
          });

          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'Lucas Segers Fabro',
              email: anotherExistentEmail,
              active: true,
              reset_password: false,
            },
            HttpStatus.CONFLICT,
          );

          return Promise.resolve();
        });
      });

      describe('For the same user', () => {
        it('User successfully updated', async () => {
          const office = await createTestOffice();
          const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
          const existentEmail = `${office.id_escritorio}-<EMAIL>`;

          mockCognitoReturnSuccess();
          mockCognitoCreateReturnUsername();
          simulateLoggedUser({ office, claims: loggedUserClaims });
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: existentEmail,
            },
            HttpStatus.CREATED,
          );

          // activate user.create and closure.update
          await updateClaims(app, {
            officeId: office.id_escritorio,
            externalCreditorId: userToUpdate,
            payload: userToCreateClaims,
          });

          mockCognitoReturnExistingUser(existentEmail, existentEmail);
          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'Lucas Segers Fabro',
              email: existentEmail,
              active: true,
              reset_password: false,
            },
            HttpStatus.OK,
          );

          // activate only user.create
          await updateClaims(app, {
            officeId: office.id_escritorio,
            externalCreditorId: userToUpdate,
            payload: [{ name: UsersClaims.USER_CREATE, value: 'true' }],
          });

          const updatedUser = (await getUserEndpoint(app, office, userToUpdate)).body as User;
          validateUserPermissions(updatedUser.permissions, [
            { name: UsersClaims.USER_CREATE, value: 'true' },
          ]);

          return Promise.resolve();
        });

        it.only('should update user email on cognito when email changed', async () => {
          const office = await createTestOffice();
          const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
          const existentEmail = `${office.id_escritorio}-<EMAIL>`;

          mockCognitoReturnSuccess();
          mockCognitoCreateReturnUsername();
          simulateLoggedUser({ office, claims: loggedUserClaims });
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: existentEmail,
            },
            HttpStatus.CREATED,
          );

          mockCognitoReturnExistingUser(existentEmail, existentEmail);
          vi.spyOn(awsSdk.cognito, 'adminUpdateUserAttributes');
          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'Lucas Segers Fabro',
              email: `new_${existentEmail}`,
              active: true,
            },
            HttpStatus.OK,
          );

          expect(awsSdk.cognito.adminUpdateUserAttributes).toHaveBeenCalled();
        });

        it('should not update user email on cognito when email did not changed', async () => {
          const office = await createTestOffice();
          const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
          const existentEmail = `${office.id_escritorio}-<EMAIL>`;

          mockCognitoReturnSuccess();
          mockCognitoCreateReturnUsername();
          simulateLoggedUser({ office, claims: loggedUserClaims });
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: existentEmail,
            },
            HttpStatus.CREATED,
          );

          mockCognitoReturnExistingUser(existentEmail, existentEmail);
          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'Lucas Segers Fabro',
              email: existentEmail,
              active: true,
            },
            HttpStatus.OK,
          );

          expect(awsSdk.cognito.adminUpdateUserAttributes).not.toHaveBeenCalled();
        });

        it('should create user on cognito when user does not have an email and there is a non-empty email in the request', async () => {
          const office = await createTestOffice();
          const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
          const newEmail = `${office.id_escritorio}-<EMAIL>`;
          const providerId = uuid();

          simulateLoggedUser({ office, claims: loggedUserClaims });

          mockCognitoReturnSuccess();

          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
            },
            HttpStatus.CREATED,
          );

          mockCognitoCreateReturnUsername(providerId);
          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'Lucas Segers Fabro',
              email: newEmail,
              active: true,
            },
            HttpStatus.OK,
          );

          const user = await Credor.findOne({
            where: {
              escritorio_id_escritorio: office.id_escritorio,
              id_credor_externo: userToUpdate,
            },
          });

          const userCognitoIdentifier = await User_Identifier.findOne({
            where: {
              oidc_id: config.userAndPasswordOidcId,
              creditor_id: user.id_credor,
            },
          });

          expect(awsSdk.cognito.adminCreateUser).toHaveBeenCalled();
          expect(userCognitoIdentifier.provider_id).toEqual(providerId);
        });

        it('should send invitation email when reset_password flag is true', async () => {
          const office = await createTestOffice();
          const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
          const existentEmail = `${office.id_escritorio}-<EMAIL>`;

          mockCognitoReturnSuccess();
          mockCognitoCreateReturnUsername();
          simulateLoggedUser({ office, claims: loggedUserClaims });
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: existentEmail,
            },
            HttpStatus.CREATED,
          );

          mockCognitoReturnExistingUser(existentEmail, existentEmail);
          vi.spyOn(awsSdk.ses, 'sendBulkTemplatedEmail');

          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'Lucas Segers Fabro',
              email: existentEmail,
              active: true,
              reset_password: true,
            },
            HttpStatus.OK,
          );

          expect(awsSdk.ses.sendBulkTemplatedEmail).toHaveBeenCalled();
        });

        it('should not send invitation email when reset_password flag is false or undefined', async () => {
          const office = await createTestOffice();
          const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
          const existentEmail = `${office.id_escritorio}-<EMAIL>`;

          mockCognitoReturnSuccess();
          mockCognitoCreateReturnUsername();
          simulateLoggedUser({ office, claims: loggedUserClaims });
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: existentEmail,
            },
            HttpStatus.CREATED,
          );

          mockCognitoReturnExistingUser(existentEmail, existentEmail);
          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'Lucas Segers Fabro',
              email: existentEmail,
              active: true,
            },
            HttpStatus.OK,
          );

          expect(awsSdk.ses.sendBulkTemplatedEmail).not.toHaveBeenCalled();
        });

        it('should delete user on cognito and clear provider_id when email on request is empty', async () => {
          const office = await createTestOffice();
          const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
          const existentEmail = `${office.id_escritorio}-<EMAIL>`;

          mockCognitoReturnSuccess();
          mockCognitoCreateReturnUsername();
          simulateLoggedUser({ office, claims: loggedUserClaims });
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: existentEmail,
            },
            HttpStatus.CREATED,
          );

          mockCognitoReturnExistingUser(existentEmail, existentEmail);
          vi.spyOn(awsSdk.cognito, 'adminDeleteUser');
          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'Lucas Segers Fabro',
              email: '',
              active: true,
            },
            HttpStatus.OK,
          );

          const user_identifier = await User_Identifier.findAll({
            where: {
              oidc_id: config.userAndPasswordOidcId,
              creditor_id: userToUpdate,
            },
          });

          expect(awsSdk.cognito.adminDeleteUser).toHaveBeenCalled();
          expect(user_identifier.length).toBe(0);
        });
      });
      describe('And remote user exists', () => {
        it('Updates local store and changes email', async () => {
          const office = await createTestOffice();

          const userToUpdate = 'A12315';
          const firstEmail = `${office.id_escritorio}-<EMAIL>`;
          const emailAfterChange = `${office.id_escritorio}-<EMAIL>`;

          mockCognitoReturnSuccess();
          mockCognitoCreateReturnUsername();
          simulateLoggedUser({ office, claims: loggedUserClaims });
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: firstEmail,
            },
            HttpStatus.CREATED,
          );

          await updateClaims(app, {
            officeId: office.id_escritorio,
            externalCreditorId: userToUpdate,
            payload: userToCreateClaims,
          });

          mockCognitoReturnExistingUser(emailAfterChange);
          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'New name',
              email: emailAfterChange,
              active: false,
              reset_password: false,
            },
            HttpStatus.OK,
          );

          const userModel = await Credor.findOne({
            where: {
              escritorio_id_escritorio: office.id_escritorio,
              id_credor_externo: userToUpdate,
            },
          });

          const customReportClaim = {
            credor_id_credor: userModel.id_credor,
            atributo: UsersClaims.CUSTOM_REPORTS,
            valor: JSON.stringify([{ name: 'Relatório xpto', questionId: 52 }]),
          };

          // adds non editable claim to user
          await Atributos_credor.create(customReportClaim);

          await updateClaims(app, {
            officeId: office.id_escritorio,
            externalCreditorId: userToUpdate,
            payload: [
              { name: UsersClaims.USER_CREATE, value: 'true' },
              { name: UsersClaims.CLOSURE_UPDATE, value: 'true' },
              { name: UsersClaims.OFFICE_CREDITORS_VIEW, value: 'true' },
            ],
          });

          expect(awsSdk.cognito.adminCreateUser).toHaveBeenCalled();

          const detailedActualUser = (await getUserEndpoint(app, office, userToUpdate))
            .body as User;

          expect(detailedActualUser.active).toBeFalsy();
          expect(detailedActualUser.name).toBe('New name');
          expect(detailedActualUser.email).toBe(emailAfterChange);
          expect(detailedActualUser.permissions.length).toBe(3); // validates that non editable claim doesnt return on API GET

          const apiClaims = [
            { name: UsersClaims.OFFICE_CREDITORS_VIEW, value: 'true' },
            { name: UsersClaims.CLOSURE_UPDATE, value: 'true' },
            { name: UsersClaims.USER_CREATE, value: 'true' },
          ];
          validateUserPermissions(detailedActualUser.permissions, apiClaims);

          const claims = (
            await Atributos_credor.findAll({
              where: { credor_id_credor: userModel.id_credor },
            })
          ).map(c => c.get() as Atributos_credor);

          const modelClaims = apiClaims.map(c => ({
            credor_id_credor: userModel.id_credor,
            atributo: c.name,
            valor: c.value,
          }));

          modelClaims.push(customReportClaim);

          expect(claims).toEqual(expect.arrayContaining(modelClaims)); // validates that non editable claims gets untouched when editing via API

          return Promise.resolve();
        });
      });

      it('Set email as null when sending empty string', async () => {
        const office = await createTestOffice();

        const userToUpdate = 'NOT_FOUND_USER_EMAIL';
        const email = `${office.id_escritorio}-<EMAIL>`;

        mockCognitoReturnSuccess();

        simulateLoggedUser({ office, claims: loggedUserClaims });
        await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: userToUpdate,
            name: 'Lucas Segers Fabro',
            email: '',
          },
          HttpStatus.CREATED,
        );

        // validates that created user with empty email gets null email
        const createdUser = (await getUserEndpoint(app, office, userToUpdate)).body as User;
        expect(createdUser.email).toBeNull();

        await updateClaims(app, {
          officeId: office.id_escritorio,
          externalCreditorId: userToUpdate,
          payload: userToCreateClaims,
        });

        // updates to a valid email
        mockCognitoUserDoesntExist(email);
        await updateUserEndpoint(
          app,
          office,
          userToUpdate,
          {
            name: 'Lucas Segers Fabro',
            email,
            active: true,
            reset_password: false,
          },
          HttpStatus.OK,
        );

        const updatedUser = (await getUserEndpoint(app, office, userToUpdate)).body as User;
        expect(updatedUser.email).toBe(email);

        // updates again with empty email to validate patch
        await updateUserEndpoint(
          app,
          office,
          userToUpdate,
          {
            name: 'Lucas Segers Fabro',
            email: '',
            active: true,
            reset_password: false,
          },
          HttpStatus.OK,
        );

        const updatedUserWithoutEmail = (await getUserEndpoint(app, office, userToUpdate))
          .body as User;
        expect(updatedUserWithoutEmail.email).toBeNull();

        return Promise.resolve();
      });
    });
    describe('And doesnt send email', () => {
      it('Updates local store', async () => {
        const office = await createTestOffice();

        simulateLoggedUser({ office, claims: loggedUserClaims });

        const uploadFileResponse = await uploadFile(app, 'test_resources/2_rows.xlsx');

        await processComissionClosureFile(
          app,
          office,
          '1990-12-01',
          uploadFileResponse.body.fullPath,
        );

        await updateUserEndpoint(
          app,
          office,
          'A67111',
          {
            email: null,
            name: 'Changed Name',
            active: false,
          },
          HttpStatus.OK,
        );

        const actualUser = await Credor.findOne({
          where: {
            id_credor_externo: 'A67111',
            escritorio_id_escritorio: office.id_escritorio,
          },
        });

        expect(actualUser.ativo).toBeFalsy();
        expect(actualUser.nome_credor).toBe('Changed Name');
        expect(actualUser.email).toBeFalsy();

        return Promise.resolve();
      });
    });

    describe('And tries to set creditor`s teams', () => {
      describe('successfully updated user', () => {
        it('setting 1 new team', async () => {
          const office = await createTestOffice();
          const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
          const teamNameToInsert = 'team A';

          simulateLoggedUser({ office, claims: loggedUserClaims });

          const existentEmail = `${office.id_escritorio}-<EMAIL>`;

          mockCognitoReturnSuccess();
          mockCognitoCreateReturnUsername();
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userToUpdate,
              name: 'Lucas Segers Fabro',
              email: existentEmail,
            },
            HttpStatus.CREATED,
          );

          let creditorResponse = await getUserEndpoint(app, office, userToUpdate);
          expect(creditorResponse.body.teams.length).toBe(0);

          mockCognitoReturnExistingUser(existentEmail, existentEmail);
          await updateUserEndpoint(
            app,
            office,
            userToUpdate,
            {
              name: 'Lucas Segers Fabro',
              email: existentEmail,
              active: true,
              reset_password: false,
              teams: [teamNameToInsert],
            },
            HttpStatus.OK,
          );

          const officeTeamsResponse = await getOfficeTeamsEndpoint(app, office);
          creditorResponse = await getUserEndpoint(app, office, userToUpdate);

          expect(officeTeamsResponse.body.length).toBe(1);
          expect(officeTeamsResponse.body[0].name).toBe(teamNameToInsert);
          expect(creditorResponse.body.teams.length).toBe(1);
          expect(creditorResponse.body.teams[0]).toBe(teamNameToInsert);

          return Promise.resolve();
        });
        describe('setting various teams', () => {
          it('setting 2 teams', async () => {
            const office = await createTestOffice();
            const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
            const teamA = 'team A';
            const teamB = 'team B';

            simulateLoggedUser({ office, claims: loggedUserClaims });

            const existentEmail = `${office.id_escritorio}-<EMAIL>`;

            mockCognitoReturnSuccess();
            mockCognitoCreateReturnUsername();
            await createUserEndpoint(
              app,
              office,
              {
                external_creditor_id: userToUpdate,
                name: 'Lucas Segers Fabro',
                email: existentEmail,
              },
              HttpStatus.CREATED,
            );

            mockCognitoReturnExistingUser(existentEmail);
            let creditorResponse = await getUserEndpoint(app, office, userToUpdate);

            expect(creditorResponse.body.teams.length).toBe(0);

            mockCognitoReturnExistingUser(existentEmail, existentEmail);
            await updateUserEndpoint(
              app,
              office,
              userToUpdate,
              {
                name: 'Lucas Segers Fabro',
                email: existentEmail,
                active: true,
                reset_password: false,
                teams: [teamA, teamB],
              },
              HttpStatus.OK,
            );

            const officeTeamsResponse = await getOfficeTeamsEndpoint(app, office);
            creditorResponse = await getUserEndpoint(app, office, userToUpdate);

            expect(officeTeamsResponse.body.length).toBe(2);

            expect(creditorResponse.body.teams.length).toBe(2);

            return Promise.resolve();
          });
          it('setting 2 teams and then removes 1 team', async () => {
            const office = await createTestOffice();
            const userToUpdate = 'ALREADY_EXISTING_EMAIL_BUT_FOR_SAME_USER';
            const teamA = 'team A';
            const teamB = 'team B';

            simulateLoggedUser({ office, claims: loggedUserClaims });

            const existentEmail = `${office.id_escritorio}-<EMAIL>`;

            mockCognitoReturnSuccess();
            mockCognitoCreateReturnUsername();
            await createUserEndpoint(
              app,
              office,
              {
                external_creditor_id: userToUpdate,
                name: 'Lucas Segers Fabro',
                email: existentEmail,
              },
              HttpStatus.CREATED,
            );

            mockCognitoReturnExistingUser(existentEmail);
            let creditorResponse = await getUserEndpoint(app, office, userToUpdate);
            expect(creditorResponse.body.teams.length).toBe(0);

            mockCognitoReturnExistingUser(existentEmail, existentEmail);
            await updateUserEndpoint(
              app,
              office,
              userToUpdate,
              {
                name: 'Lucas Segers Fabro',
                email: existentEmail,
                active: true,
                reset_password: false,
                teams: [teamA, teamB],
              },
              HttpStatus.OK,
            );

            let officeTeamsResponse = await getOfficeTeamsEndpoint(app, office);
            creditorResponse = await getUserEndpoint(app, office, userToUpdate);

            expect(officeTeamsResponse.body.length).toBe(2);

            expect(creditorResponse.body.teams.length).toBe(2);

            mockCognitoReturnExistingUser(existentEmail, existentEmail);
            await updateUserEndpoint(
              app,
              office,
              userToUpdate,
              {
                name: 'Lucas Segers Fabro',
                email: existentEmail,
                active: true,
                reset_password: false,
                teams: [teamA],
              },
              HttpStatus.OK,
            );

            officeTeamsResponse = await getOfficeTeamsEndpoint(app, office);
            creditorResponse = await getUserEndpoint(app, office, userToUpdate);

            expect(officeTeamsResponse.body.length).toBe(1);
            expect(officeTeamsResponse.body[0].name).toBe(teamA);

            expect(creditorResponse.body.teams.length).toBe(1);
            expect(creditorResponse.body.teams[0]).toBe(teamA);

            return Promise.resolve();
          });
        });
      });

      describe('Creates 2 users, updates its teams, validates office and users teams', () => {
        it('removes unused teams and set only one team for both users', async () => {
          const office = await createTestOffice();

          simulateLoggedUser({ office, claims: loggedUserClaims });

          const userAToUpdate = 'USER_A';
          const userAEmail = `${office.id_escritorio}-<EMAIL>`;

          const userBToUpdate = 'USER_b';
          const userBEmail = `${office.id_escritorio}-<EMAIL>`;

          const teamA = 'team A';
          const teamB = 'team B';

          mockCognitoReturnSuccess();
          mockCognitoCreateReturnUsername();
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userAToUpdate,
              name: 'Fulano A',
              email: userAEmail,
            },
            HttpStatus.CREATED,
          );

          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: userBToUpdate,
              name: 'Fulano B',
              email: userBEmail,
            },
            HttpStatus.CREATED,
          );

          let aCreditorResponse = await getUserEndpoint(app, office, userAToUpdate);
          let bCreditorResponse = await getUserEndpoint(app, office, userBToUpdate);
          expect(aCreditorResponse.body.teams.length).toBe(0);
          expect(bCreditorResponse.body.teams.length).toBe(0);

          mockCognitoReturnExistingUser(userAEmail, userAEmail);
          await updateUserEndpoint(
            app,
            office,
            userAToUpdate,
            {
              name: 'Fulano A',
              email: userAEmail,
              active: true,
              reset_password: false,
              teams: [teamA, teamB],
            },
            HttpStatus.OK,
          );

          mockCognitoReturnExistingUser(userBEmail, userBEmail);
          await updateUserEndpoint(
            app,
            office,
            userBToUpdate,
            {
              name: 'Fulano B',
              email: userBEmail,
              active: true,
              reset_password: false,
              teams: [teamA],
            },
            HttpStatus.OK,
          );

          let officeTeamsReponse = await getOfficeTeamsEndpoint(app, office);
          aCreditorResponse = await getUserEndpoint(app, office, userAToUpdate);
          bCreditorResponse = await getUserEndpoint(app, office, userBToUpdate);

          expect(officeTeamsReponse.body.length).toBe(2);
          expect(aCreditorResponse.body.teams.length).toBe(2);
          expect(bCreditorResponse.body.teams.length).toBe(1);

          mockCognitoReturnExistingUser(userAEmail, userAEmail);
          await updateUserEndpoint(
            app,
            office,
            userAToUpdate,
            {
              name: 'Fulano A',
              email: userAEmail,
              active: true,
              reset_password: false,
              teams: [teamA],
            },
            HttpStatus.OK,
          );

          officeTeamsReponse = await getOfficeTeamsEndpoint(app, office);
          aCreditorResponse = await getUserEndpoint(app, office, userAToUpdate);
          bCreditorResponse = await getUserEndpoint(app, office, userBToUpdate);

          expect(officeTeamsReponse.body.length).toBe(1);
          expect(aCreditorResponse.body.teams.length).toBe(1);
          expect(bCreditorResponse.body.teams.length).toBe(1);

          return Promise.resolve();
        });
      });
    });
  });

  describe('When creditor doesnt exist on office', () => {
    it('Returns NOTFOUND', async () => {
      const office = await createTestOffice();
      const userToUpdate = 'A00000010';

      await updateUserEndpoint(
        app,
        office,
        userToUpdate,
        {
          name: 'Lucas Segers Fabro',
          email: `${office.id_escritorio}-<EMAIL>`,
          active: true,
        },
        HttpStatus.NOT_FOUND,
      );

      return Promise.resolve();
    });
  });

  describe('restricted claims', () => {
    it('prevents saving office only claims to user', async () => {
      const office = await createTestOffice();
      const userToUpdate = 'A00000010';

      simulateLoggedUser({ office, claims: loggedUserClaims });
      mockCognitoReturnSuccess();
      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: userToUpdate,
          name: 'Lucas Segers Fabro',
          email: `${office.id_escritorio}-<EMAIL>`,
        },
        HttpStatus.CREATED,
      );

      const res = await updateClaims(app, {
        officeId: office.id_escritorio,
        externalCreditorId: userToUpdate,
        payload: [
          {
            name: UsersClaims.FEATURE_DIVISION_ROUNDING,
            value: JSON.stringify({ decimals: 2, round_mode: 'up' }),
          },
        ],
        expectedHttpStatus: HttpStatus.BAD_REQUEST,
      });

      expect(res.body).toMatchSnapshot();

      return Promise.resolve();
    });
  });

  describe('update user', () => {
    // createPayload will be sent using admin privileges
    // updatePayload will be sent using claim array defined on each case
    const updateCases = [
      {
        description:
          'admin claim(validate insert teams and profiles) > create without team and profile > update with two teams and profiles',
        claims: [
          {
            atributo: UsersClaims.USER_CREATE,
            valor: 'true',
          },
          {
            atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
            valor: 'true',
          },
        ],
        payloadCreate: {
          teams: [],
          profiles: [],
        },
        payloadUpdate: {
          teams: ['TEAM_A', 'TEAM_B'],
          profiles: ['ADMIN_USER', 'DEFAULT_USER'],
        },
        expectedUpdate: {
          teams: ['TEAM_A', 'TEAM_B'],
          profiles: ['ADMIN_USER', 'DEFAULT_USER'],
        },
      },
      {
        description:
          'when having user management claim > create with two teams and profiles > update empty teams and profiles > validate that only teams and profiles that user has access are removed',
        claims: [
          {
            atributo: UsersClaims.USER_MANAGEMENT,
            valor:
              '{"teams":["TEAM_B"],"profiles":["DEFAULT_USER"],"allow_create":true,"allow_delete":false}',
          },
          {
            atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
            valor: '["A1"]',
          },
        ],
        payloadCreate: {
          teams: ['TEAM_A', 'TEAM_B'],
          profiles: ['ADMIN_USER', 'DEFAULT_USER'],
        },
        payloadUpdate: {
          teams: [],
          profiles: [],
        },
        expectedUpdate: {
          teams: ['TEAM_A'],
          profiles: ['ADMIN_USER'],
        },
      },
      {
        description:
          'when having user management claim > validates that profiles and teams not allowed for the requester gets ignored, and that previous ones that requester doesnt have permission are kept',
        claims: [
          {
            atributo: UsersClaims.USER_MANAGEMENT,
            valor:
              '{"teams":["TEAM_B"],"profiles":["DEFAULT_USER"],"allow_create":true,"allow_delete":false}',
          },
          {
            atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
            valor: '["A1"]',
          },
        ],
        payloadCreate: {
          teams: ['TEAM_A'],
          profiles: ['ADMIN_USER'],
        },
        payloadUpdate: {
          teams: ['TEAM_B', 'TEAM_C'],
          profiles: ['DEFAULT_USER', 'XPTO_USER'],
        },
        expectedUpdate: {
          teams: ['TEAM_A', 'TEAM_B'],
          profiles: ['ADMIN_USER', 'DEFAULT_USER'],
        },
      },
      {
        description:
          'user management claim > create without teams and profiles > update with two teams and profiles > validates that only teams and profiles that requester has access are inserted',
        claims: [
          {
            atributo: UsersClaims.USER_MANAGEMENT,
            valor:
              '{"teams":["TEAM_B"],"profiles":["DEFAULT_USER"],"allow_create":true,"allow_delete":false}',
          },
          {
            atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
            valor: '["A1"]',
          },
        ],
        payloadCreate: {
          teams: [],
          profiles: [],
        },
        payloadUpdate: {
          teams: ['TEAM_A', 'TEAM_B', 'TEAM_C'],
          profiles: ['ADMIN_USER', 'DEFAULT_USER', 'XPTO_USER'],
        },
        expectedUpdate: {
          teams: ['TEAM_B'],
          profiles: ['DEFAULT_USER'],
        },
      },
    ];
    it.each(updateCases)(
      '$description',
      async ({ claims, payloadCreate, payloadUpdate, expectedUpdate }) => {
        const office = await createTestOffice();
        const loggedUser = await Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'LOGGED_USER',
          ativo: true,
          nome_credor: 'Usuário logado',
        });

        simulateLoggedUser({
          office,
          credor: loggedUser,
          claims: [
            {
              atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
              valor: 'true',
            },
            {
              atributo: UsersClaims.USER_CREATE,
              valor: 'true',
            },
          ],
        });

        const { body: adminProfile } = await createPermissionProfile<PermissionProfileResponse>(
          app,
          {
            officeId: office.id_escritorio,
            payload: {
              name: 'ADMIN_USER',
              permission_data: [
                {
                  name: UsersClaims.OFFICE_CREDITORS_VIEW,
                  value: 'true',
                },
              ],
            },
          },
        );

        const { body: defaultUserProfile } =
          await createPermissionProfile<PermissionProfileResponse>(app, {
            officeId: office.id_escritorio,
            payload: {
              name: 'DEFAULT_USER',
              permission_data: [
                {
                  name: UsersClaims.PAYROLL_VIEW,
                  value: 'true',
                },
              ],
            },
          });

        // mapa para depara do payload
        const permissionsMap = new Map<string, number>();
        permissionsMap.set('ADMIN_USER', adminProfile.id);
        permissionsMap.set('DEFAULT_USER', defaultUserProfile.id);

        const response = await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: 'A1',
            name: 'A1',
            permission_profile_ids: payloadCreate.profiles.map(p => permissionsMap.get(p)),
            teams: payloadCreate.teams,
          },
          HttpStatus.CREATED,
        );

        const validateTeamsAndProfiles = async (
          creditorId: number,
          expected: {
            teams: string[];
            profiles: string[];
          },
        ) => {
          const userTeams = await Creditor_Teams.findAll({
            where: {
              creditor_id: creditorId,
            },
            include: [Teams],
          });
          const userTeamsName = userTeams.map(userTeam => userTeam.team.name);
          expect(userTeamsName).toEqual(expected.teams);

          const userProfiles = await User_Permission.findAll({
            where: {
              creditor_id: creditorId,
            },
            include: [Permission_Profile],
          });

          const userProfilesName = userProfiles.map(
            userProfile => userProfile.permission_profile.name,
          );
          expect(userProfilesName).toEqual(expected.profiles);
        };

        await validateTeamsAndProfiles(response.body.id_credor, {
          profiles: payloadCreate.profiles,
          teams: payloadCreate.teams,
        });

        simulateLoggedUser({
          office,
          credor: loggedUser,
          claims,
        });
        await updateUserEndpoint(
          app,
          office,
          'A1',
          {
            name: 'A1 alterado',
            permission_profile_ids: payloadUpdate.profiles.map(p => permissionsMap.get(p)),
            teams: payloadUpdate.teams,
            active: true,
          },
          HttpStatus.OK,
        );

        await validateTeamsAndProfiles(response.body.id_credor, {
          teams: expectedUpdate.teams,
          profiles: expectedUpdate.profiles,
        });

        return Promise.resolve();
      },
    );

    const reconcileProfileCases = [
      {
        description: 'Belongs to two profiles, add one more',
        payloadCreate: {
          teams: [],
          profiles: ['PA', 'PB'],
        },
        payloadUpdate: {
          teams: [],
          profiles: ['PA', 'PB', 'PC'],
        },
      },
      {
        description: 'Belongs to two profiles, remove one and add another',
        payloadCreate: {
          teams: [],
          profiles: ['PA', 'PB'],
        },
        payloadUpdate: {
          teams: [],
          profiles: ['PB', 'PC'],
        },
      },
      {
        description: 'Does not belong to any profile, adds them all',
        payloadCreate: {
          teams: [],
          profiles: [],
        },
        payloadUpdate: {
          teams: [],
          profiles: ['PA', 'PB', 'PC'],
        },
      },
      {
        description: 'Belongs to everyone profile, remove all',
        payloadCreate: {
          teams: [],
          profiles: ['PA', 'PB', 'PC'],
        },
        payloadUpdate: {
          teams: [],
          profiles: [],
        },
      },
    ];
    const reconcileTeamCases = [
      {
        description: 'Belongs to two teams, adds one more',
        payloadCreate: {
          teams: ['TA', 'TB'],
          profiles: [],
        },
        payloadUpdate: {
          teams: ['TA', 'TB', 'TC'],
          profiles: [],
        },
      },
      {
        description: 'Belongs to two teams, removes one and adds another',
        payloadCreate: {
          teams: ['TA', 'TB'],
          profiles: [],
        },
        payloadUpdate: {
          teams: ['TB', 'TC'],
          profiles: [],
        },
      },
      {
        description: 'Does not belong to any team, add everyone',
        payloadCreate: {
          teams: [],
          profiles: [],
        },
        payloadUpdate: {
          teams: ['TA', 'TB', 'TC'],
          profiles: [],
        },
      },
      {
        description: 'Belongs to all teams, removes all',
        payloadCreate: {
          teams: ['TA', 'TB', 'TC'],
          profiles: [],
        },
        payloadUpdate: {
          teams: [],
          profiles: [],
        },
      },
    ];
    it.each([...reconcileTeamCases, ...reconcileProfileCases])(
      'validate reconcile signers request on create/update user ($description)',
      async ({ payloadCreate, payloadUpdate }) => {
        const office = await createTestOffice();
        const loggedUser = await Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'LOGGED_USER',
          ativo: true,
          nome_credor: 'Usuário logado',
        });
        simulateLoggedUser({
          office,
          credor: loggedUser,
          claims: [
            { atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' },
            {
              atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
              valor: 'true',
            },
            {
              atributo: UsersClaims.USER_CREATE,
              valor: 'true',
            },
            claimPolicyAgreementModule,
          ],
        });

        const profilesMap = await createProfiles(app, office.id_escritorio);

        const expectedOldTags = [
          ...payloadCreate.teams.map(team => ({
            tag_value: team,
            tag_type: PacketTagTypes.team,
          })),
          ...payloadCreate.profiles.map(p => ({
            tag_value: String(profilesMap.get(p)),
            tag_type: PacketTagTypes.profile,
          })),
        ];

        // validate reconcile request on create user
        await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: 'A1',
            name: 'A1',
            permission_profile_ids: payloadCreate.profiles.map(p => profilesMap.get(p)),
            teams: payloadCreate.teams,
          },
          HttpStatus.CREATED,
        );

        const expectedReconcileRequestOnCreateUser = [
          {
            creditor_id: 'A1',
            creditor_name: 'A1',
            old_tags: [],
            current_tags: expectedOldTags,
          },
        ];

        expect(documentSignatureApiServiceStub.reconcileSigners).toHaveBeenCalledTimes(
          expectedOldTags?.length ? 1 : 0,
        );
        if (expectedOldTags?.length)
          expect(documentSignatureApiServiceStub.reconcileSigners).toHaveBeenCalledWith({
            clientId: office.client_id,
            body: expectedReconcileRequestOnCreateUser,
          });

        documentSignatureApiServiceStub.reconcileSigners.mockClear();

        // validate reconcile request on update user
        await updateUserEndpoint(
          app,
          office,
          'A1',
          {
            name: 'A1 alterado',
            permission_profile_ids: payloadUpdate.profiles.map(p => profilesMap.get(p)),
            teams: payloadUpdate.teams,
            active: true,
          },
          HttpStatus.OK,
        );

        expect(documentSignatureApiServiceStub.reconcileSigners).toHaveBeenCalledTimes(1);
        return Promise.resolve();
      },
    );

    it.each([
      {
        usersClaims: [
          [
            {
              atributo: UsersClaims.USER_CREATE,
              valor: 'true',
            },
          ],
          [
            {
              atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
              valor: '["A1"]',
            },
            {
              atributo: UsersClaims.USER_MANAGEMENT,
              valor:
                '{"teams":["TEAM_A"],"profiles":["DEFAULT_USER"],"allow_create":true,"allow_delete":true}',
            },
          ],
        ],
        postRequest: ['TEAM_A', 'TEAM_B', 'TEAM_C'],
        patchRequest: [],
        expectFinalAs: ['TEAM_B', 'TEAM_C'],
      },
      {
        usersClaims: [
          [
            {
              atributo: UsersClaims.USER_CREATE,
              valor: 'true',
            },
          ],
          [
            {
              atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
              valor: '["A1"]',
            },
            {
              atributo: UsersClaims.USER_MANAGEMENT,
              valor:
                '{"teams":["TEAM_A"],"profiles":["DEFAULT_USER"],"allow_create":true,"allow_delete":true}',
            },
          ],
        ],
        postRequest: ['TEAM_A', 'TEAM_B'],
        patchRequest: ['TEAM_B'],
        expectFinalAs: ['TEAM_B'],
      },
      {
        usersClaims: [
          [
            {
              atributo: UsersClaims.USER_CREATE,
              valor: 'true',
            },
          ],
          [
            {
              atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
              valor: '["A1"]',
            },
            {
              atributo: UsersClaims.USER_MANAGEMENT,
              valor:
                '{"teams":["TEAM_D", "TEAM_E"],"profiles":["DEFAULT_USER"],"allow_create":true,"allow_delete":true}',
            },
          ],
        ],
        postRequest: ['TEAM_A', 'TEAM_B'],
        patchRequest: [],
        expectFinalAs: ['TEAM_A', 'TEAM_B'],
      },
      {
        usersClaims: [
          [
            {
              atributo: UsersClaims.USER_CREATE,
              valor: 'true',
            },
          ],
          [
            {
              atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
              valor: '["A1"]',
            },
            {
              atributo: UsersClaims.USER_MANAGEMENT,
              valor:
                '{"teams":["TEAM_A"],"profiles":["DEFAULT_USER"],"allow_create":true,"allow_delete":true}',
            },
          ],
        ],
        postRequest: [],
        patchRequest: ['TEAM_A', 'TEAM_B', 'TEAM_C'],
        expectFinalAs: ['TEAM_A'],
      },
    ])(
      'should only remove the teams that the logged user has permission',
      async ({ postRequest, patchRequest, expectFinalAs, usersClaims }) => {
        const office = await createTestOffice();
        const userOne = await Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'LOGGED_USER',
          ativo: true,
          nome_credor: 'Usuário logado',
        });

        const userTwo = await Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'USER_TWO',
          ativo: true,
          nome_credor: 'Usuário dois',
        });

        simulateLoggedUser({
          office,
          credor: userOne,
          claims: usersClaims[0],
        });

        await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: 'A1',
            name: 'A1',
            active: true,
            teams: postRequest,
          },
          HttpStatus.CREATED,
        );

        const { body: user } = await getUserEndpoint(app, office, 'A1');

        expect(user.teams).toEqual(postRequest);

        simulateLoggedUser({
          office,
          credor: userTwo,
          claims: usersClaims[1],
        });

        await updateUserEndpoint(
          app,
          office,
          'A1',
          {
            name: user.name,
            teams: patchRequest,
            active: true,
          },
          HttpStatus.OK,
        );

        const { body: updatedUser } = await getUserEndpoint(app, office, 'A1');

        expect(updatedUser.teams).toEqual(expectFinalAs);
      },
    );

    it('fail to update when requester dont have permission to update user A2', async () => {
      const office = await createTestOffice();
      const loggedUser = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'LOGGED_USER',
        ativo: true,
        nome_credor: 'Usuário logado',
      });

      await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'A2',
        ativo: true,
        nome_credor: 'A2',
      });

      simulateLoggedUser({
        office,
        credor: loggedUser,
        claims: [
          {
            atributo: UsersClaims.USER_MANAGEMENT,
            valor:
              '{"teams":["TEAM_B"],"profiles":["DEFAULT_USER"],"allow_create":true,"allow_delete":false}',
          },
          {
            atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
            valor: '["A1"]',
          },
        ],
      });

      await updateUserEndpoint(
        app,
        office,
        'A2',
        {
          name: 'A2 alterado',
          active: true,
        },
        HttpStatus.NOT_FOUND,
      );

      return Promise.resolve();
    });
  });

  describe('Update User Password', () => {
    const createCreditor = async (officeId: number) =>
      Credor.create({
        id_credor_externo: uuid(),
        nome_credor: 'any_name',
        email: `${uuid()}@any.email`,
        ativo: true,
        escritorio_id_escritorio: officeId,
      });

    const claims = [
      ...loggedUserClaims,
      { atributo: UsersClaims.ALLOW_CREDENTIALS_MANAGEMENT, valor: '1' },
    ];

    it(`should throw an error if logged user doesnt have ${UsersClaims.ALLOW_CREDENTIALS_MANAGEMENT}`, async () => {
      const office = await createTestOffice();

      const credor = await createCreditor(office.id_escritorio);
      simulateLoggedUser({ office, claims: loggedUserClaims, credor });

      await updateUserPassword(app, {
        officeId: office.id_escritorio,
        sessionId: 'any_session',
        password: 'any_password',
        expectedHttpStatus: HttpStatus.FORBIDDEN,
      });
    });

    it.each([
      () =>
        getSessionMock.mockResolvedValueOnce({
          userId: 'any_user+id',
          status: 'invalid_status',
        }),
      () => getUserMock.mockResolvedValueOnce({ username: 'invalid_username' }),
    ])('should throw error if session is not active', async mockFn => {
      const office = await createTestOffice();

      const credor = await createCreditor(office.id_escritorio);
      simulateLoggedUser({ office, claims, credor });

      mockFn();

      const { body } = await updateUserPassword(app, {
        officeId: office.id_escritorio,
        sessionId: 'any_session',
        password: 'any_password',
        expectedHttpStatus: HttpStatus.UNAUTHORIZED,
      });

      expect(body).toEqual(
        new CustomException(BackendErrorCodes.AUTHENTICATION_FAILED).getResponse(),
      );
    });

    it('should update user password correctly', async () => {
      const office = await createTestOffice();

      const credor = await createCreditor(office.id_escritorio);
      simulateLoggedUser({ office, claims, credor });

      const userId = 'any_user_id';
      const username = sanitizeUsername(`${office.id_escritorio}_${credor.id_credor_externo}`);
      getUserMock.mockResolvedValueOnce({
        id: userId,
        username,
      });

      const password = faker.lorem.word();
      await updateUserPassword(app, {
        officeId: office.id_escritorio,
        sessionId: 'any_session',
        password,
        expectedHttpStatus: HttpStatus.OK,
      });

      expect(updateUserMock).toHaveBeenCalledWith(userId, {
        password,
        publicMetadata: {},
      });
    });
  });

  afterAll(async () => {
    await app.close();
    await app.get('SEQUELIZE').close();
  });
});
