import { vi, Mocked } from 'vitest';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import {
  UsersClaims,
  BackendErrorCodes,
  BackendErrors,
  PayrollGroupRoles,
  GetPayrollGroupResponse,
} from 'shared-types';
import { AppModule } from '../../../src/app.module';
import { ConfigurationEnv } from '../../../src/config/configuration.env';
import { Credor } from '../../../src/models/credor';
import AWSService from '../../../src/services/aws.sdk.service';
import { GoogleGcsService } from '../../../src/services/google/gcs.service';
import { SheetDbApiService } from '../../../src/services/sheet.db.api.service';
import ZombieApiService from '../../../src/services/zombie.api.service';
import { slugify } from '../../../src/utils/string.utils';
import {
  getPayrollGroups,
  postPayrollGroup,
  patchPayrollGroup,
  getPayrollGroupBySlug,
} from '../../utils/common.requests';
import { DEFAULT_GCP_CREDENTIALS, makePayroll } from '../../utils/massa.utils';
import { v4 as uuid } from 'uuid';
import { SECRET_VAULT } from '@app/services/google/secret-manager.service';
import { LambdaPaymentsService } from '@app/services/baas/lambdapayments/lambda.payments.service';
import { AwsSesFacade } from '../../../src/services/aws.ses.facade';
import {
  CLAIMS_ADMIN,
  PAYROLL_V2_CLAIM,
  createCreditors,
  PAYROLL_GROUP_MANAGER,
  PAYROLL_STATUS_MANAGER,
} from './utils';
import { googleGcsServiceMock, sheetDbApiServiceMock, zombieServiceMock } from './utils/mocks';
import { OutboundWebhookService } from '@app/services/webhooks/outbound.webhook.service';
import { DocumentSignatureApiService } from '@app/document.signature/service/document.signature.api.service';
import { documentSignatureApiServiceStub } from '../../stubs/document.signature.api.service';
import {
  prepareLoginAdapter,
  setupMockLoginDomain,
  simulateLoggedUserBuilder,
  SimulateLoggedUserData,
} from '../../mocks/login.domain.mock';
import { Payroll_Group } from '@app/models/payroll_group';
import { Permission_Profile } from '@app/models/permission_profile';
import { Escritorio } from '@app/models/escritorio';
import { Payroll } from '@app/models/payroll';
import { Payroll_Data } from '@app/models/payroll_data';

vi.mock('../../../src/services/aws.sdk.service');
vi.mock('starkbank');

const sesFacadeMock = {
  sendMail: vi.fn(),
  sendTemplatedEmail: vi.fn(),
  sendBulkTemplatedEmail: vi.fn(),
};

const lambdaPaymentsService = {
  sendCheckoutRequest: vi.fn(),
};

const outboundWebhookService = {
  processWebhook: vi.fn().mockResolvedValue({ success: true }),
};

const secretVaultMock = {
  getSecret: vi.fn(),
};

const awsSdk = AWSService as unknown as Mocked<AWSService>;

const config = new ConfigurationEnv();

describe('Payroll (E2E)', () => {
  let app: INestApplication;
  let mockLoadAuthContext: ReturnType<typeof setupMockLoginDomain>;

  beforeAll(async () => {
    Object.defineProperty(config, 'tokenValidationEnabled', { value: false });
    Object.defineProperty(awsSdk, 'ses', { value: {} });
    Object.defineProperty(config, 'GCS_ROOT_FOLDER', { value: 'temp_files/' });
    Object.defineProperty(config, 'serviceAccountAuth', { value: DEFAULT_GCP_CREDENTIALS });
    Object.defineProperty(config, 'digitalSignatureValidationEnabled', { value: false });

    awsSdk.ses.sendTemplatedEmail = vi.fn().mockReturnValue({
      promise: () => vi.fn().mockResolvedValue('OK'),
    });

    const moduleBuilder = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .overrideProvider(AWSService)
      .useValue(awsSdk)
      .overrideProvider(SheetDbApiService)
      .useValue(sheetDbApiServiceMock)
      .overrideProvider(GoogleGcsService)
      .useValue(googleGcsServiceMock)
      .overrideProvider(ZombieApiService)
      .useValue(zombieServiceMock)
      .overrideProvider(LambdaPaymentsService)
      .useValue(lambdaPaymentsService)
      .overrideProvider(DocumentSignatureApiService)
      .useValue(documentSignatureApiServiceStub)
      .overrideProvider(AwsSesFacade)
      .useValue(sesFacadeMock)
      .overrideProvider(OutboundWebhookService)
      .useValue(outboundWebhookService)
      .overrideProvider(SECRET_VAULT)
      .useValue(secretVaultMock);
    mockLoadAuthContext = setupMockLoginDomain(moduleBuilder);
    const module = await moduleBuilder.compile();

    module.useLogger(module.get(Logger));

    app = module.createNestApplication({ bodyParser: false });

    await app.init();
  });

  const simulateLoggedUser = (params: SimulateLoggedUserData) => {
    simulateLoggedUserBuilder(mockLoadAuthContext)(params);
  };

  const prepareLogin = (
    claims: SimulateLoggedUserData['claims'] = [CLAIMS_ADMIN, PAYROLL_V2_CLAIM],
    creditor_to_create?: string,
    creditorProps?: Partial<Credor>,
  ) => prepareLoginAdapter(mockLoadAuthContext)(claims, creditor_to_create, creditorProps);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterAll(async () => {
    await app.close();
    await app.get('SEQUELIZE').close();
  });

  describe('PATCH /group/:slug', () => {
    it('should update/clear permissions without removing other permissions', async () => {
      const { office } = await prepareLogin([PAYROLL_V2_CLAIM, PAYROLL_GROUP_MANAGER], 'admin', {
        id_credor_externo: 'admin',
      });

      const group = await Payroll_Group.create({
        office_id: office.id_escritorio,
        label: 'Grupo 1',
        slug: slugify('Grupo 1'),
      });

      const profile3Permissions = [
        {
          name: UsersClaims.FEATURE_PAYROLL_V2,
          value: '1',
        },
        {
          name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
          value: JSON.stringify({
            payroll_group_permissions: [
              {
                payroll_group_slug: 'xpto-xyz',
                roles: [],
              },
            ],
          }),
        },
      ];

      const [profileWithManager, profileViewer, profile3] = await Promise.all([
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_1',
          priority: 1,
          permission_data: [],
        }),
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_2',
          priority: 2,
          permission_data: [
            {
              name: UsersClaims.FEATURE_PAYROLL_V2,
              value: '1',
            },
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: JSON.stringify({
                payroll_group_permissions: [
                  {
                    payroll_group_slug: group.slug,
                    roles: [],
                  },
                ],
              }),
            },
          ],
        }),
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_3',
          priority: 3,
          permission_data: profile3Permissions,
        }),
      ]);

      // should add role PAYROLL_MANAGER to profile profileViewer and profileWithManager
      await patchPayrollGroup(app, {
        officeId: office.id_escritorio,
        payload: {
          slug: group.slug,
          permissions: [
            {
              permission_profile_id: profileWithManager.id,
              roles: [PayrollGroupRoles.PAYROLL_MANAGER],
            },
            {
              permission_profile_id: profileViewer.id,
              roles: [PayrollGroupRoles.PAYROLL_MANAGER],
            },
          ],
        },
      });

      const reloadProfiles = () =>
        Promise.all([profileWithManager.reload(), profileViewer.reload(), profile3.reload()]);

      await reloadProfiles();

      expect(profileWithManager.permission_data).toHaveLength(1);
      expect(profileViewer.permission_data).toHaveLength(2);

      [
        { profile: profileWithManager, index: 0 },
        { profile: profileViewer, index: 1 },
      ].forEach(({ profile, index }) => {
        expect(JSON.parse(profile.permission_data[index].value)).toEqual({
          payroll_group_permissions: [
            {
              payroll_group_slug: group.slug,
              roles: [PayrollGroupRoles.PAYROLL_MANAGER],
            },
          ],
        });
      });

      // should remove profileViewer permissions from group
      await patchPayrollGroup(app, {
        officeId: office.id_escritorio,
        payload: {
          slug: group.slug,
          permissions: [
            {
              permission_profile_id: profileWithManager.id,
              roles: [PayrollGroupRoles.PAYROLL_MANAGER],
            },
          ],
        },
      });

      await reloadProfiles();

      expect(profileWithManager.permission_data).toHaveLength(1);
      expect(profileViewer.permission_data).toHaveLength(1);
      expect(JSON.parse(profileWithManager.permission_data[0].value)).toEqual({
        payroll_group_permissions: [
          {
            payroll_group_slug: group.slug,
            roles: [PayrollGroupRoles.PAYROLL_MANAGER],
          },
        ],
      });
      expect(profileViewer.permission_data).toEqual([
        {
          name: UsersClaims.FEATURE_PAYROLL_V2,
          value: '1',
        },
      ]);
      expect(profile3.permission_data).toHaveLength(2);
      expect(profile3.permission_data).toEqual(profile3Permissions);

      // should remove permissions from profileViewer and profileWithManager
      await patchPayrollGroup(app, {
        officeId: office.id_escritorio,
        payload: {
          slug: group.slug,
          permissions: [],
        },
      });

      await reloadProfiles();

      expect(profileWithManager.permission_data).toHaveLength(0);
      expect(profileViewer.permission_data).toHaveLength(1);
      expect(profileViewer.permission_data).toEqual([
        {
          name: UsersClaims.FEATURE_PAYROLL_V2,
          value: '1',
        },
      ]);
      expect(profile3.permission_data).toHaveLength(2);
      expect(profile3.permission_data).toEqual(profile3Permissions);
    });

    it('should patch partially and manage permissions', async () => {
      const { office, credor } = await prepareLogin(
        [PAYROLL_V2_CLAIM, PAYROLL_GROUP_MANAGER],
        'admin',
        {
          id_credor_externo: 'admin',
        },
      );

      const group = await Payroll_Group.create({
        office_id: office.id_escritorio,
        label: 'Grupo 1',
        slug: slugify('Grupo 1'),
      });

      const profile2Permissions = [
        {
          name: UsersClaims.FEATURE_PAYROLL_V2,
          value: '1',
        },
      ];

      const [profile, profile2] = await Promise.all([
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_1',
          priority: 1,
          permission_data: [],
        }),
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_2',
          priority: 2,
          permission_data: profile2Permissions,
        }),
      ]);

      simulateLoggedUser({
        office,
        credor,
        permissionProfiles: [profile],
        claims: [PAYROLL_V2_CLAIM, PAYROLL_GROUP_MANAGER],
      });

      const { body } = await patchPayrollGroup(app, {
        officeId: office.id_escritorio,
        payload: {
          slug: group.slug,
          permissions: [
            {
              permission_profile_id: profile.id,
              roles: [PayrollGroupRoles.PAYROLL_MANAGER],
            },
            {
              permission_profile_id: profile2.id,
              roles: [],
            },
          ],
        },
      });

      expect(body.id).toBe(group.id);
      expect(body.label).toBe('Grupo 1');

      const checkPermissions = async () => {
        await Promise.all([profile.reload(), profile2.reload()]);

        expect(profile.permission_data).toHaveLength(1);
        expect(profile.permission_data[0].name).toBe(UsersClaims.PAYROLL_GROUP_PERMISSIONS);
        expect(JSON.parse(profile.permission_data[0].value).payroll_group_permissions).toEqual([
          {
            payroll_group_slug: group.slug,
            roles: [PayrollGroupRoles.PAYROLL_MANAGER],
          },
        ]);
        expect(profile2.permission_data).toEqual(
          expect.arrayContaining([
            ...profile2Permissions,
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: JSON.stringify({
                payroll_group_permissions: [
                  {
                    payroll_group_slug: group.slug,
                    roles: [],
                  },
                ],
              }),
            },
          ]),
        );
      };

      await checkPermissions();

      // change only the name
      const { body: body2 } = await patchPayrollGroup(app, {
        officeId: office.id_escritorio,
        payload: {
          slug: group.slug,
          label: 'Grupo 2',
        },
      });

      expect(body2.id).toBe(group.id);
      expect(body2.label).toBe('Grupo 2');

      await checkPermissions();
    });
  });

  describe('POST /group', () => {
    it('should return 403 if user does not have PAYROLL_GROUP_MANAGER claim', async () => {
      const { office } = await prepareLogin([]);

      const { body } = await postPayrollGroup(app, {
        officeId: office.id_escritorio,
        payload: {
          label: 'Grupo 1',
          slug: slugify('Grupo 1'),
        },
        expectStatus: HttpStatus.FORBIDDEN,
      });

      expect(body).toStrictEqual({
        error: 'Forbidden',
        message: 'Forbidden resource',
        statusCode: HttpStatus.FORBIDDEN,
      });
    });

    it('should create a new group', async () => {
      const { office } = await prepareLogin([
        { atributo: UsersClaims.PAYROLL_GROUP_MANAGER, valor: '1' },
      ]);

      const { body } = await postPayrollGroup(app, {
        officeId: office.id_escritorio,
        payload: {
          label: 'Grupo 1',
          slug: slugify('Grupo 1'),
        },
      });

      expect(body).toHaveProperty('id');
      expect(body.label).toBe('Grupo 1');

      const group = await Payroll_Group.findOne({
        where: {
          slug: slugify('Grupo 1'),
          office_id: office.id_escritorio,
        },
      });

      expect(group).toBeDefined();
      expect(group.label).toBe('Grupo 1');
    });

    it('should return 409 if group name already exists in the current office', async () => {
      const { office } = await prepareLogin([
        { atributo: UsersClaims.PAYROLL_GROUP_MANAGER, valor: '1' },
      ]);

      await postPayrollGroup(app, {
        officeId: office.id_escritorio,
        payload: {
          label: 'Grupo 1',
          slug: slugify('Grupo 1'),
        },
      });

      const { body } = await postPayrollGroup(app, {
        officeId: office.id_escritorio,
        payload: {
          label: 'Grupo 1',
          slug: slugify('Grupo 1'),
        },
        expectStatus: HttpStatus.CONFLICT,
      });

      expect(body).toStrictEqual({
        message: BackendErrors[BackendErrorCodes.PAYROLL_GROUP_ALREADY_EXISTS].description,
        code: BackendErrorCodes.PAYROLL_GROUP_ALREADY_EXISTS,
        data: {
          slug: slugify('Grupo 1'),
        },
      });
    });
  });

  describe('GET /group/:slug', () => {
    it('should get the group by slug', async () => {
      const { office } = await prepareLogin([PAYROLL_V2_CLAIM, PAYROLL_GROUP_MANAGER], 'admin', {
        id_credor_externo: 'admin',
      });

      const [firstGroup, secondGroup] = await Promise.all([
        Payroll_Group.create({
          office_id: office.id_escritorio,
          label: 'Grupo 1',
          slug: slugify('Grupo 1'),
        }),
        Payroll_Group.create({
          office_id: office.id_escritorio,
          label: 'Grupo 2',
          slug: slugify('Grupo 2'),
        }),
      ]);

      const [profile1, profile2] = await Promise.all([
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'Perfil 1',
          priority: 3,
          permission_data: [
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: `{ "payroll_group_permissions": [ {"payroll_group_slug": "${secondGroup.slug}", "roles": ["payroll_manager"] }, {"payroll_group_slug": "xpto-xyz", "roles": [] }  ] }`,
            },
          ],
        }),
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'Perfil 2',
          priority: 2,
          permission_data: [
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: `{ "payroll_group_permissions": [ {"payroll_group_slug": "${secondGroup.slug}", "roles": []} ] }`,
            },
          ],
        }),
      ]);

      const { body: bodyGroupOne } = await getPayrollGroupBySlug(app, {
        officeId: office.id_escritorio,
        slug: slugify('Grupo 1'),
      });

      expect(bodyGroupOne).toStrictEqual({
        id: firstGroup.id,
        label: firstGroup.label,
        slug: firstGroup.slug,
        permissions: [],
      });

      const { body: bodyGroupTwo } = await getPayrollGroupBySlug(app, {
        officeId: office.id_escritorio,
        slug: slugify('Grupo 2'),
      });

      expect(bodyGroupTwo).toStrictEqual({
        id: secondGroup.id,
        label: secondGroup.label,
        slug: secondGroup.slug,
        permissions: expect.arrayContaining([
          {
            id: profile2.id,
            profileName: 'Perfil 2',
            permissions: [
              {
                payroll_group_slug: secondGroup.slug,
                roles: [],
              },
            ],
          },
          {
            id: profile1.id,
            profileName: 'Perfil 1',
            permissions: [
              {
                payroll_group_slug: secondGroup.slug,
                roles: ['payroll_manager'],
              },
            ],
          },
        ]),
      });
    });

    it('should return NOT_FOUND when slug dont exist', async () => {
      const { office } = await prepareLogin([PAYROLL_V2_CLAIM, PAYROLL_GROUP_MANAGER], 'admin', {
        id_credor_externo: 'admin',
      });

      const { body } = await getPayrollGroupBySlug(app, {
        officeId: office.id_escritorio,
        slug: slugify('Grupo 1'),
        expectStatus: HttpStatus.NOT_FOUND,
      });

      expect(body).toStrictEqual({
        message: BackendErrors[BackendErrorCodes.PAYROLL_GROUP_NOT_FOUND].description,
        code: BackendErrorCodes.PAYROLL_GROUP_NOT_FOUND,
        data: {
          slug: slugify('Grupo 1'),
        },
      });
    });
  });

  describe('GET /groups', () => {
    const simulate = ({
      profiles,
      claims,
      credor,
      office,
    }: {
      profiles?: Permission_Profile[];
      claims?: any[];
      credor: Credor;
      office: Escritorio;
    }) =>
      simulateLoggedUser({
        office,
        credor,
        permissionProfiles: profiles ? profiles : undefined,
        claims,
      });

    const createPayrollGroups = async (office: Escritorio, size: number) =>
      Promise.all(
        Array.from({ length: size }, (_, i) =>
          Payroll_Group.create({
            office_id: office.id_escritorio,
            label: `Grupo ${i + 1}`,
            slug: slugify(`Grupo ${i + 1}`),
          }),
        ),
      );

    it('should return default payroll when user has PAYROLL_GROUP_MANAGER claim', async () => {
      const { office, credor } = await prepareLogin([PAYROLL_V2_CLAIM], 'admin', {
        id_credor_externo: 'admin',
      });

      const [firstGroup] = await createPayrollGroups(office, 1);

      simulateLoggedUser({
        office,
        credor,
        claims: [PAYROLL_V2_CLAIM, PAYROLL_GROUP_MANAGER],
      });

      const { body } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(body.groupings).toHaveLength(2);
      expect(
        body.groupings.map(({ id, payroll_count, label }) => ({ id, payroll_count, label })),
      ).toEqual([
        { id: -1, payroll_count: 1, label: 'Grupo padrão' },
        { id: firstGroup.id, payroll_count: 0, label: firstGroup.label },
      ]);
    });

    it('should return default payroll when user has PAYROLL_STATUS_MANAGER claim', async () => {
      const { office, credor } = await prepareLogin([PAYROLL_V2_CLAIM], 'admin', {
        id_credor_externo: 'admin',
      });

      simulateLoggedUser({
        office,
        credor,
        claims: [PAYROLL_V2_CLAIM, PAYROLL_STATUS_MANAGER],
      });

      const { body } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(body.groupings).toHaveLength(1);
      expect(body.groupings.map(({ id, payroll_count }) => ({ id, payroll_count }))).toEqual([
        { id: -1, payroll_count: 1 },
      ]);
    });

    it('should return default payroll when user can see credditor that see default payroll', async () => {
      const { office, credor } = await prepareLogin([PAYROLL_V2_CLAIM], 'admin', {
        id_credor_externo: 'admin',
      });

      const payroll = await Payroll.create({
        office_id: office.id_escritorio,
        date: '2024-01-01',
        payout_date: '2024-01-02',
        visible: true,
        credor_id: credor.id_credor_externo,
        hash: '71553fcd-23d8-4056-a1bf-2664d0fc6e70',
        hash_metadata: {},
      });

      const canSeeCreditor = await Credor.create({
        id_credor_externo: 'credor_can_see',
        nome_credor: 'credor_can_see',
        escritorio_id_escritorio: office.id_escritorio,
        ativo: true,
      });

      await Payroll_Data.create({
        payroll_id: payroll.id,
        creditor_id: canSeeCreditor.id_credor_externo,
        ammount: 100,
      });

      simulateLoggedUser({
        office,
        credor,
        claims: [
          PAYROLL_V2_CLAIM,
          {
            atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
            valor: JSON.stringify([canSeeCreditor.id_credor_externo]),
          },
        ],
      });

      const { body } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(body.groupings).toHaveLength(1);
      expect(body.groupings.map(({ id, payroll_count }) => ({ id, payroll_count }))).toEqual([
        { id: -1, payroll_count: 1 },
      ]);
    });

    it("should return groups that doesn't have payroll if user is granular manager", async () => {
      const { office, credor } = await prepareLogin([PAYROLL_V2_CLAIM], 'admin', {
        id_credor_externo: 'admin',
      });

      const [firstGroup] = await createPayrollGroups(office, 1);

      const [profileWithManager, profileViewer] = await Promise.all([
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_5',
          priority: 3,
          permission_data: [
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: `{ "payroll_group_permissions": [ {"payroll_group_slug": "${firstGroup.slug}", "roles": ["${PayrollGroupRoles.PAYROLL_MANAGER}"] } ] }`,
            },
          ],
        }),
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_45',
          priority: 1,
          permission_data: [
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: `{ "payroll_group_permissions": [ {"payroll_group_slug": "${firstGroup.slug}", "roles": [] } ] }`,
            },
          ],
        }),
      ]);

      simulateLoggedUser({
        office,
        credor,
        permissionProfiles: [profileWithManager],
        claims: [PAYROLL_V2_CLAIM],
      });

      const { body } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(body.groupings).toHaveLength(1);
      expect(body.groupings.map(({ id, payroll_count }) => ({ id, payroll_count }))).toEqual([
        { id: firstGroup.id, payroll_count: 0 },
      ]);

      simulateLoggedUser({
        office,
        credor,
        permissionProfiles: [profileViewer],
        claims: [PAYROLL_V2_CLAIM],
      });

      const { body: bodyViewer } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(bodyViewer.groupings).toHaveLength(0);
    });

    it('should get the groups that the user has profile permission to view', async () => {
      const { office, credor } = await prepareLogin([PAYROLL_V2_CLAIM], 'admin', {
        id_credor_externo: 'admin',
      });

      const [firstGroup, secondGroup] = await createPayrollGroups(office, 2);

      await makePayroll(office.id_escritorio, '2024-01-01', {
        payroll: {
          visible: true,
          group_id: firstGroup.id,
        },
      });

      await makePayroll(office.id_escritorio, '2024-01-02', {
        payroll: {
          visible: true,
          group_id: secondGroup.id,
        },
      });

      const [profile1, _, profile3] = await Promise.all([
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_1',
          priority: 1,
          permission_data: [
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: `{ "payroll_group_permissions": [ {"payroll_group_slug": "${firstGroup.slug}", "roles": [] } ] }`,
            },
          ],
        }),
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_45',
          priority: 3,
          permission_data: [
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: `{ "payroll_group_permissions": [ {"payroll_group_slug": "${firstGroup.slug}", "roles": ["payroll_manager"] } ] }`,
            },
          ],
        }),
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_2',
          priority: 2,
          permission_data: [
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: `{ "payroll_group_permissions": [ {"payroll_group_slug": "${secondGroup.slug}", "roles": ["payroll_manager"]} ] }`,
            },
          ],
        }),
      ]);

      simulateLoggedUser({
        office,
        credor,
        permissionProfiles: [profile1],
        claims: [PAYROLL_V2_CLAIM],
      });

      const { body } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      // only has acccess to firstGroup
      expect(body.groupings).toHaveLength(1);
      expect(body.groupings.map(({ id, payroll_count }) => ({ id, payroll_count }))).toEqual([
        { id: firstGroup.id, payroll_count: 1 },
      ]);

      simulateLoggedUser({
        office,
        credor,
        permissionProfiles: [profile3],
        claims: [PAYROLL_V2_CLAIM],
      });

      const { body: bodyThree } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      // only has access to secondGroup
      expect(bodyThree.groupings).toHaveLength(1);
      expect(bodyThree.groupings.map(({ id, payroll_count }) => ({ id, payroll_count }))).toEqual([
        { id: secondGroup.id, payroll_count: 1 },
      ]);
    });

    it('should respect the granular permissions in the claim PAYROLL_GROUP_PERMISSIONS', async () => {
      const { credor, office } = await prepareLogin(
        [{ atributo: UsersClaims.PAYROLL_GROUP_MANAGER, valor: '1' }],
        'admin',
        {
          id_credor_externo: 'admin',
        },
      );

      const [firstGroup, _, thirdGroup] = await createPayrollGroups(office, 3);

      const [profile1, profile2] = await Promise.all([
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_1',
          priority: 1,
          permission_data: [
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: `{ "payroll_group_permissions": [ {"payroll_group_slug": "${firstGroup.slug}", "roles": [] } ] }`,
            },
          ],
        }),
        Permission_Profile.create({
          office_id: office.id_escritorio,
          name: 'profile_45',
          priority: 3,
          permission_data: [
            {
              name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
              value: `{ "payroll_group_permissions": [ {"payroll_group_slug": "${firstGroup.slug}", "roles": ["payroll_manager"] }, {"payroll_group_slug": "${thirdGroup.slug}", "roles": [] } ] }`,
            },
          ],
        }),
      ]);

      // create a payroll and vinculates to credor
      const anotherCredor = await createCreditors({
        officeId: office.id_escritorio,
        creditorId: uuid(),
        creditorName: 'Another Credor',
      });

      await makePayroll(office.id_escritorio, '2024-01-01', {
        payroll: {
          visible: false,
          group_id: firstGroup.id,
        },
        payrollData: [
          {
            creditor_id: credor.id_credor_externo,
            amount: 100,
            grouping: 'PJ1',
          },
          {
            creditor_id: credor.id_credor_externo,
            amount: 100,
            grouping: 'PJ2',
          },
          {
            creditor_id: anotherCredor.creditorLogged.id_credor_externo,
            amount: 115,
            grouping: 'PJ1',
          },
        ],
      });

      await makePayroll(office.id_escritorio, '2024-01-12', {
        payroll: {
          visible: true,
          group_id: thirdGroup.id,
        },
      });

      // IS granular MANAGER with visible FALSE
      // should see thirdGroup because has payroll_manager (thirdGroup dont have data)
      simulate({
        claims: [PAYROLL_V2_CLAIM],
        profiles: [profile2],
        credor,
        office,
      });

      const { body: bodyManager } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(bodyManager.groupings).toHaveLength(2);
      expect(bodyManager.groupings.map(({ id }) => id)).toEqual([firstGroup.id, thirdGroup.id]);

      // IS granular VIEWER with visible FALSE
      simulate({
        claims: [PAYROLL_V2_CLAIM],
        profiles: [profile1],
        credor,
        office,
      });

      const { body: boydViewer } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(boydViewer.groupings).toHaveLength(0);
    });

    it('should return payroll_count correctly', async () => {
      const { office, credor } = await prepareLogin(
        [{ atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: '1' }],
        'admin',
        {
          id_credor_externo: 'admin',
        },
      );

      const [firstGroup, secondGroup, thirdGroup, fourthGroup, fifthGroup, sixthGroup] =
        await createPayrollGroups(office, 6);

      const profileValue = [
        {
          payroll_group_slug: secondGroup.slug,
          roles: [],
        },
        {
          payroll_group_slug: thirdGroup.slug,
          roles: [],
        },
        {
          payroll_group_slug: fourthGroup.slug,
          roles: [PayrollGroupRoles.PAYROLL_MANAGER],
        },
        {
          payroll_group_slug: fifthGroup.slug,
          roles: [],
        },
        {
          payroll_group_slug: sixthGroup.slug,
          roles: [PayrollGroupRoles.PAYROLL_MANAGER],
        },
      ];

      const profile = await Permission_Profile.create({
        office_id: office.id_escritorio,
        name: 'profile_1',
        priority: 1,
        permission_data: [
          {
            name: UsersClaims.PAYROLL_GROUP_PERMISSIONS,
            value: `{ "payroll_group_permissions": ${JSON.stringify(profileValue)} }`,
          },
        ],
      });

      await makePayroll(office.id_escritorio, '2024-01-01', {
        payroll: {
          visible: true,
          group_id: firstGroup.id,
        },
        payrollData: [
          {
            creditor_id: credor.id_credor_externo,
            amount: 100,
            grouping: 'PJ1',
          },
        ],
      });

      // payroll without date but visible
      await makePayroll(office.id_escritorio, '2024-01-15', {
        payroll: {
          visible: true,
          group_id: firstGroup.id,
        },
      });

      // user is granular manager on this group, should see this
      await makePayroll(office.id_escritorio, '2024-01-12', {
        payroll: {
          visible: false,
          group_id: fourthGroup.id,
        },
      });

      await makePayroll(office.id_escritorio, '2024-01-09', {
        payroll: {
          visible: true,
          group_id: fourthGroup.id,
        },
      });

      // user is viewer, should not count this
      await makePayroll(office.id_escritorio, '2024-01-05', {
        payroll: {
          visible: false,
          group_id: firstGroup.id,
        },
        payrollData: [
          {
            creditor_id: credor.id_credor_externo,
            amount: 100,
            grouping: 'PJ1',
          },
        ],
      });

      await makePayroll(office.id_escritorio, '2024-01-23', {
        payroll: {
          visible: false,
          group_id: firstGroup.id,
        },
      });

      await makePayroll(office.id_escritorio, '2024-01-02', {
        payroll: {
          visible: true,
          group_id: secondGroup.id,
        },
        payrollData: [
          {
            creditor_id: credor.id_credor_externo,
            amount: 100,
            grouping: 'PJ1',
          },
        ],
      });

      simulate({
        claims: [PAYROLL_V2_CLAIM],
        credor,
        office,
        profiles: [profile],
      });

      const { body } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(body.groupings).toHaveLength(4);
      expect(body.groupings.map(({ id, payroll_count }) => ({ id, payroll_count }))).toEqual(
        expect.arrayContaining([
          { id: firstGroup.id, payroll_count: 1 },
          { id: secondGroup.id, payroll_count: 1 },
          { id: fourthGroup.id, payroll_count: 2 },
          { id: sixthGroup.id, payroll_count: 0 },
        ]),
      );
    });

    it('should return all groups when user is admin', async () => {
      const { office, credor } = await prepareLogin([
        { atributo: UsersClaims.PAYROLL_GROUP_MANAGER, valor: '1' },
      ]);

      const [firstGroup, secondGroup] = await createPayrollGroups(office, 2);

      const adminClaims = [PAYROLL_GROUP_MANAGER];

      for (const claim of adminClaims) {
        simulate({
          claims: [claim, PAYROLL_V2_CLAIM],
          credor,
          office,
        });

        const { body: adminBody } = await getPayrollGroups(app, {
          officeId: office.id_escritorio,
        });

        expect(adminBody.groupings).toHaveLength(3);
        expect(adminBody.groupings.map(({ id }) => id)).toEqual([
          -1,
          firstGroup.id,
          secondGroup.id,
        ]);
      }
    });

    it('should return the payroll groups when user has PAYROLL_V2_CLAIM and data on payroll_data', async () => {
      const { office, credor } = await prepareLogin(
        [{ atributo: UsersClaims.FEATURE_PAYROLL_V2, valor: '1' }],
        'A1',
        {
          id_credor_externo: 'A1',
        },
      );

      simulate({
        claims: [PAYROLL_V2_CLAIM],
        credor,
        office,
      });

      const [payrollGroup] = await createPayrollGroups(office, 1);
      const [defaultPayroll] = await Promise.all([
        // create a default payrol without data
        await makePayroll(office.id_escritorio, '2024-01-01', {
          payroll: {
            visible: true,
          },
          payrollData: [],
        }),
        // create some payroll with group that user has data
        await makePayroll(office.id_escritorio, '2024-01-01', {
          payroll: {
            visible: true,
            group_id: payrollGroup.id,
          },
          payrollData: [
            {
              creditor_id: credor.id_credor_externo,
              amount: 100,
              grouping: 'PJ1',
            },
          ],
        }),
      ]);

      let res = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(
        res.body.groupings.map(({ id, payroll_count, label }) => ({ id, payroll_count, label })),
      ).toEqual(
        expect.arrayContaining([
          { id: payrollGroup.id, payroll_count: 1, label: payrollGroup.label },
        ]),
      );

      await Payroll_Data.create({
        payroll_id: defaultPayroll.payroll.id,
        creditor_id: credor.id_credor_externo,
        ammount: 100,
      });
      const { body } = await getPayrollGroups(app, {
        officeId: office.id_escritorio,
      });

      expect(
        body.groupings.map(({ id, payroll_count, label }) => ({ id, payroll_count, label })),
      ).toEqual(
        expect.arrayContaining([
          { id: -1, payroll_count: 1, label: 'Grupo padrão' },
          { id: payrollGroup.id, payroll_count: 1, label: payrollGroup.label },
        ]),
      );
    });
  });
});
