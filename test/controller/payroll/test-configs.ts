import { PaymentOptionTypes, PayerConfig } from 'shared-types';
import { getPayerConfigForTest } from './test-helpers';

/**
 * Common test configurations for payment options
 */

// CNAB Configuration
export const cnabConfig = {
  type: PaymentOptionTypes.CNAB,
  payment_date: 'any_payment_date',
  payer_name: 'any_payer_name',
  payer_cnpj: 'any_payer_cnpj',
  payer_address: {
    street: 'any_street',
    number: 'any_number',
    complement: 'any_complement',
    city: 'any_city',
    zip_code: 'any_zip_code',
    state: 'any_state',
  },
  payer_account: {
    bank_code: '341' as '341',
    branch: 'any_branch',
    account: 'any_account',
    account_digit: '',
  },
};

// Itau Sheet Configuration
export const itauSheetConfig = {
  type: PaymentOptionTypes.ITAU_SHEET,
  payment_type: 123456,
};

// API Payment Options
export const kaminoSetupTest = getPayerConfigForTest(
  PaymentOptionTypes.API_KAMINO,
  { credential: 'checkout_testing_credential' },
  'kamino',
  { payer_cnpj: 'checkout_testing_cnpj', payer_name: 'checkout_testing_name' },
);

export const interSetupTest = getPayerConfigForTest(
  PaymentOptionTypes.API_INTER,
  {
    certificatePem: 'checkout_testing_certificate_pem',
    clientId: 'checkout_testing_client_id',
    clientSecret: 'checkout_testing_client_secret',
    privateKeyPem: 'checkout_testing_private_key_pem',
  },
  'inter',
);

export const starkSetupTest = getPayerConfigForTest(
  PaymentOptionTypes.API_STARK,
  {
    workflow_id: 'checkout_testing_workflow_key',
    private_key_pem: 'checkout_testing_private_key_pem',
  },
  'stark',
);

// File Type Cases

// Input Error Cases
export const inputErrorCases = [
  {
    paymentType: PaymentOptionTypes.API_KAMINO,
    payerConfig: kaminoSetupTest.payerConfig,
    secret: kaminoSetupTest.secretValueAsBase64,
  },
  {
    paymentType: PaymentOptionTypes.API_INTER,
    payerConfig: interSetupTest.payerConfig,
    secret: interSetupTest.secretValueAsBase64,
  },
  {
    paymentType: PaymentOptionTypes.API_STARK,
    payerConfig: starkSetupTest.payerConfig,
    secret: starkSetupTest.secretValueAsBase64,
  },
  {
    paymentType: PaymentOptionTypes.CNAB,
    payerConfig: cnabConfig,
    secret: null,
  },
  {
    paymentType: PaymentOptionTypes.ITAU_SHEET,
    payerConfig: itauSheetConfig,
    secret: null,
  },
];
