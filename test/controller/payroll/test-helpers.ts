import { Credor } from '@app/models/credor';
import { Escritorio } from '@app/models/escritorio';
import { Financial_Institution } from '@app/models/financial_institution';
import { Object_Workflow } from '@app/models/object_workflow';
import { Payment_Option } from '@app/models/payment_option';
import { Payroll_Payment } from '@app/models/payroll_payment';
import { Creditor_Payment_Info } from '@app/models/creditor_payment_info';
import {
  AccountType,
  ObjectWorkflowType,
  PayerConfig,
  PaymentCheckoutRequest,
  PaymentOptionTypes,
  PayrollPaymentStatus,
  PayrollStatus,
} from 'shared-types';
import { v4 as uuid } from 'uuid';
import { makePayroll } from '../../utils/massa.utils';
import { mockPaymentRequestPayroll } from '../../utils/massa.utils';
import { buildPaymentRequestWorkflow, createCreditors } from './utils';
import { PaymentRequestStatus } from 'shared-types';

/**
 * Prepares the test environment for checkout tests
 */
export const prepareCheckoutTest = async (
  office: Escritorio,
  type: ObjectWorkflowType,
  paymentOptionPayload?: {
    paymentType: PaymentOptionTypes;
    prettyName: string;
    payerConfig: PayerConfig;
  },
  payrollOpts?: { payrollData?: { grouping: string; amount: number; creditor_id: string }[] },
) => {
  const officeId = office.id_escritorio;
  const payoutDate = '2030-01-01';

  let objectWorkflows: Object_Workflow[] = [];
  if (type === ObjectWorkflowType.PAYMENT_REQUEST) {
    const { paymentRequests } = await makePaymentRequests(office);
    objectWorkflows = paymentRequests.map(({ workflow_object }) => workflow_object);
  } else {
    const creditorsToCreate = ['A11', 'A12', 'A13'];
    await Promise.all(
      creditorsToCreate.map(creditorId =>
        createCreditors({ officeId, creditorId, creditorName: creditorId }),
      ),
    );

    const payrollData = payrollOpts?.payrollData ?? [
      {
        creditor_id: 'A11',
        amount: 100,
        grouping: 'PJ1',
        workflow_object: {
          status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
        },
      },
      {
        creditor_id: 'A12',
        amount: 200,
        grouping: 'PJ1',
        workflow_object: {
          status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
        },
      },
      {
        creditor_id: 'A13',
        amount: 300,
        grouping: 'PJ2',
        workflow_object: {
          status: PayrollStatus.PENDING_RECEIVER_APPROVAL,
        },
      },
    ];
    const data = await makePayroll(officeId, payoutDate, {
      payrollData,
      payroll: {
        hash: 'any_hash',
      },
    });

    objectWorkflows = data.objectWorkflows;
  }

  const creditors = await Credor.findAll({
    where: {
      escritorio_id_escritorio: officeId,
      id_credor: objectWorkflows.map(({ object_owner }) => object_owner),
    },
  });

  if (!paymentOptionPayload) {
    return { objectWorkflows, creditors };
  }

  const { paymentType, prettyName, payerConfig } = paymentOptionPayload;
  const paymentOption = await makePaymentOption(officeId, paymentType, prettyName, payerConfig);
  return { objectWorkflows, paymentOption, creditors };
};

/**
 * Creates a payment option for testing
 */
export const makePaymentOption = async (
  office_id: number,
  type: PaymentOptionTypes,
  pretty_name: string,
  config: PayerConfig,
) => {
  return Payment_Option.create({
    office_id,
    type,
    pretty_name,
    config,
  });
};

/**
 * Creates payment requests for testing
 */
export const makePaymentRequests = async (office: Escritorio) => {
  const { payroll, config } = await mockPaymentRequestPayroll(office.id_escritorio, '2020-01-01');
  const defaultCreditors = await Credor.bulkCreate([
    {
      escritorio_id_escritorio: office.id_escritorio,
      id_credor_externo: 'A11',
      ativo: true,
      nome_credor: 'A11',
    },
    {
      escritorio_id_escritorio: office.id_escritorio,
      id_credor_externo: 'A12',
      ativo: true,
      nome_credor: 'A12',
    },
    {
      escritorio_id_escritorio: office.id_escritorio,
      id_credor_externo: 'A13',
      ativo: true,
      nome_credor: 'A13',
    },
  ]);

  const paymentRequests = await Promise.all(
    defaultCreditors.map(async credor =>
      buildPaymentRequestWorkflow(office, config, credor, {
        request_key: uuid(),
        amount: 1200,
        grouping: { Empreendimento: '1' },
        workflow_object: {
          status: PaymentRequestStatus.INVOICE_SENT,
        },
      }),
    ),
  );

  return { payroll, config, paymentRequests: paymentRequests.flat(), defaultCreditors };
};

/**
 * Creates payment information for creditors
 */
export const setupCreditorPaymentInfo = async (
  creditors: Credor[],
  financialInstitutionProps?: Partial<Financial_Institution>,
  creditorPaymentInfoProps?: Partial<Creditor_Payment_Info>,
) => {
  const [financial_institution] = await Financial_Institution.findOrCreate({
    where: {
      ispb_code: financialInstitutionProps?.ispb_code || '********',
      bank_code: financialInstitutionProps?.bank_code || '341',
      name: financialInstitutionProps?.name || 'ITAÚ UNIBANCO S.A.',
    },
  });

  await Promise.all(
    creditors.map(creditor =>
      Creditor_Payment_Info.create({
        active: true,
        document_number: creditorPaymentInfoProps?.document_number || '***********',
        branch: creditorPaymentInfoProps?.branch || '1234',
        account: creditorPaymentInfoProps?.account || '123-4',
        financial_institution_id: financial_institution.id,
        creditor_id: creditor.id_credor,
        account_type: AccountType.PAYMENT,
        pix_key: creditorPaymentInfoProps?.pix_key || '',
        ...creditorPaymentInfoProps,
      }),
    ),
  );

  return { financial_institution };
};

/**
 * Creates a standard checkout payload
 */
export const createCheckoutPayload = (
  objectWorkflows: Object_Workflow[],
  paymentOptionId?: number,
  type: ObjectWorkflowType = ObjectWorkflowType.PAYROLL_DATA,
  amount: number = 12345.67,
): PaymentCheckoutRequest => {
  return {
    payment_date: '2030-01-01',
    type,
    ...(paymentOptionId ? { payer_config_id: paymentOptionId } : {}),
    payments_to_checkout: objectWorkflows.map(workflow => ({
      object_workflow_id: workflow.id,
      amount,
    })),
  };
};

/**
 * Verifies payroll payments match expected values
 */
export const verifyPayrollPayments = async (
  payrollPaymentIds: number[],
  expectedStatus: PayrollPaymentStatus,
  expectedPayerConfig: Record<string, any>,
  includeObjectWorkflow: boolean = false,
) => {
  const findOptions: any = {
    where: {
      id: payrollPaymentIds,
    },
    order: ['id'],
  };

  if (includeObjectWorkflow) {
    findOptions.include = [
      {
        model: Object_Workflow,
        as: 'object_workflow',
      },
    ];
  }

  const payrollPayments = await Payroll_Payment.findAll(findOptions);

  expect(payrollPayments).toHaveLength(payrollPaymentIds.length);

  payrollPayments.forEach(payment => {
    expect(payment.status).toBe(expectedStatus);
    expect(payment.payer_config).toEqual(expectedPayerConfig);
  });

  return payrollPayments;
};

/**
 * Helper to create payer config test data
 */
export const getPayerConfigForTest = (
  paymentOptionType: PaymentOptionTypes,
  secretValue: Record<string, string>,
  secretNamePrefix: string,
  payerSchemaAdditional: Record<string, unknown> = {},
) => {
  const payerConfig = {
    type: paymentOptionType,
    ...payerSchemaAdditional,
    secret_name: `${secretNamePrefix}_secret_name`,
  };
  const expectedPayerConfig = {
    type: paymentOptionType,
    ...payerSchemaAdditional,
    ...secretValue,
  };
  const secretValueAsBase64 = Buffer.from(JSON.stringify(secretValue)).toString('base64');

  return {
    payerConfig,
    secretValue,
    secretValueAsBase64,
    expectedPayerConfig,
  };
};
