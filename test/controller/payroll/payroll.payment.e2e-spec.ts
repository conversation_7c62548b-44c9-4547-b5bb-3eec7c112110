import { vi, Mocked } from 'vitest';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Logger } from 'nestjs-pino';
import {
  PaymentOptionTypes,
  AccountType,
  Payment,
  PaymentsCheckoutResponse,
  PayrollPaymentStatus,
  BackendErrorCodes,
  PaymentCheckoutRequest,
  ObjectWorkflowType,
  PayerConfig,
} from 'shared-types';
import { AppModule } from '../../../src/app.module';
import { ConfigurationEnv } from '../../../src/config/configuration.env';
import { Credor } from '../../../src/models/credor';
import AWSService from '../../../src/services/aws.sdk.service';
import { GoogleGcsService } from '../../../src/services/google/gcs.service';
import { SheetDbApiService } from '../../../src/services/sheet.db.api.service';
import ZombieApiService from '../../../src/services/zombie.api.service';
import { payrollPaymentCheckout } from '../../utils/common.requests';
import { DEFAULT_GCP_CREDENTIALS } from '../../utils/massa.utils';
import { Payment_Option } from '@app/models/payment_option';
import { Creditor_Payment_Info } from '@app/models/creditor_payment_info';
import { Financial_Institution } from '@app/models/financial_institution';
import { SECRET_VAULT } from '@app/services/google/secret-manager.service';
import { Payroll_Payment } from '@app/models/payroll_payment';
import { LambdaPaymentsService } from '@app/services/baas/lambdapayments/lambda.payments.service';
import { readFileSync } from 'fs';
import { AwsSesFacade } from '../../../src/services/aws.ses.facade';
import { CLAIMS_ADMIN, PAYROLL_V2_CLAIM, PERMISSION_PAYMENTS } from './utils';
import { googleGcsServiceMock, sheetDbApiServiceMock, zombieServiceMock } from './utils/mocks';
import { OutboundWebhookService } from '@app/services/webhooks/outbound.webhook.service';
import { DocumentSignatureApiService } from '@app/document.signature/service/document.signature.api.service';
import { documentSignatureApiServiceStub } from '../../stubs/document.signature.api.service';
import { Object_Workflow } from '@app/models/object_workflow';
import {
  prepareLoginAdapter,
  setupMockLoginDomain,
  SimulateLoggedUserData,
} from '../../mocks/login.domain.mock';
import {
  prepareCheckoutTest,
  setupCreditorPaymentInfo,
  createCheckoutPayload,
  verifyPayrollPayments,
} from './test-helpers';
import {
  cnabConfig,
  starkSetupTest,
  interSetupTest,
  kaminoSetupTest,
  itauSheetConfig,
} from './test-configs';

vi.mock('../../../src/services/aws.sdk.service');
vi.mock('starkbank');

const sesFacadeMock = {
  sendMail: vi.fn(),
  sendTemplatedEmail: vi.fn(),
  sendBulkTemplatedEmail: vi.fn(),
};

const lambdaPaymentsService = {
  sendCheckoutRequest: vi.fn(),
};

const outboundWebhookService = {
  processWebhook: vi.fn().mockResolvedValue({ success: true }),
};

const secretVaultMock = {
  getSecret: vi.fn(),
};

const awsSdk = AWSService as unknown as Mocked<AWSService>;

const config = new ConfigurationEnv();

describe('Payroll Payment (E2E)', () => {
  let app: INestApplication;
  let mockLoadAuthContext: ReturnType<typeof setupMockLoginDomain>;

  beforeAll(async () => {
    Object.defineProperty(config, 'tokenValidationEnabled', { value: false });
    Object.defineProperty(awsSdk, 'ses', { value: {} });
    Object.defineProperty(config, 'GCS_ROOT_FOLDER', { value: 'temp_files/' });
    Object.defineProperty(config, 'serviceAccountAuth', { value: DEFAULT_GCP_CREDENTIALS });
    Object.defineProperty(config, 'digitalSignatureValidationEnabled', { value: false });

    awsSdk.ses.sendTemplatedEmail = vi.fn().mockReturnValue({
      promise: () => vi.fn().mockResolvedValue('OK'),
    });

    const moduleBuilder = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .overrideProvider(AWSService)
      .useValue(awsSdk)
      .overrideProvider(SheetDbApiService)
      .useValue(sheetDbApiServiceMock)
      .overrideProvider(GoogleGcsService)
      .useValue(googleGcsServiceMock)
      .overrideProvider(ZombieApiService)
      .useValue(zombieServiceMock)
      .overrideProvider(LambdaPaymentsService)
      .useValue(lambdaPaymentsService)
      .overrideProvider(DocumentSignatureApiService)
      .useValue(documentSignatureApiServiceStub)
      .overrideProvider(AwsSesFacade)
      .useValue(sesFacadeMock)
      .overrideProvider(OutboundWebhookService)
      .useValue(outboundWebhookService)
      .overrideProvider(SECRET_VAULT)
      .useValue(secretVaultMock);
    mockLoadAuthContext = setupMockLoginDomain(moduleBuilder);
    const module = await moduleBuilder.compile();

    module.useLogger(module.get(Logger));

    app = module.createNestApplication({ bodyParser: false });

    await app.init();
  });

  const prepareLogin = (
    claims: SimulateLoggedUserData['claims'] = [CLAIMS_ADMIN, PAYROLL_V2_CLAIM],
    creditor_to_create?: string,
    creditorProps?: Partial<Credor>,
  ) => prepareLoginAdapter(mockLoadAuthContext)(claims, creditor_to_create, creditorProps);

  beforeEach(() => {
    vi.resetAllMocks();
    vi.clearAllMocks();
  });

  afterAll(async () => {
    await app.close();
    await app.get('SEQUELIZE').close();
  });

  describe.each([
    {
      type: ObjectWorkflowType.PAYMENT_REQUEST,
    },
    {
      type: ObjectWorkflowType.PAYROLL_DATA,
    },
  ])('POST /checkout ($type)', ({ type }) => {
    it('should return error when exist duplicated object_workflow_id in request payload', async () => {
      const { office } = await prepareLogin([PERMISSION_PAYMENTS], 'admin');

      const duplicatedPayload = {
        payment_date: '2024-01-01',
        type,
        payments_to_checkout: [
          {
            object_workflow_id: 1,
            amount: 12345.67,
          },
          {
            object_workflow_id: 1, // Duplicated ID
            amount: 12345.67,
          },
          {
            object_workflow_id: 2,
            amount: 12345.67,
          },
        ],
      };

      const { body: checkoutResponse } = await payrollPaymentCheckout<PaymentsCheckoutResponse>(
        app,
        {
          officeId: office.id_escritorio,
          payload: duplicatedPayload,
        },
        HttpStatus.UNPROCESSABLE_ENTITY,
      );

      expect(checkoutResponse).toEqual({
        code: BackendErrorCodes.DUPLICATE_OBJECT_WORKFLOW_IN_REQUEST,
        message: 'Dados duplicado na requisição, cada item deve ser único',
      });
    });

    it('should return error when trying to pay payroll date from another office or inexistent', async () => {
      const { office } = await prepareLogin([PERMISSION_PAYMENTS], 'admin');

      const nonExistentPayload = {
        payment_date: '2024-01-01',
        type,
        payments_to_checkout: [
          {
            object_workflow_id: 9999, // Non-existent ID
            amount: 12345.67,
          },
          {
            object_workflow_id: 1,
            amount: 12345.67,
          },
          {
            object_workflow_id: 2,
            amount: 12345.67,
          },
        ],
      };

      const { body: checkoutResponse } = await payrollPaymentCheckout<PaymentsCheckoutResponse>(
        app,
        {
          officeId: office.id_escritorio,
          payload: nonExistentPayload,
        },
        HttpStatus.NOT_FOUND,
      );

      expect(checkoutResponse).toEqual({
        code: BackendErrorCodes.OBJECT_WORKFLOW_NOT_FOUND,
        message: 'Não foi possível encontrar o objeto de workflow',
      });
    });

    it('should create payroll payment correctly when payer config is empty', async () => {
      const { office } = await prepareLogin([PERMISSION_PAYMENTS], 'admin');

      const { objectWorkflows } = await prepareCheckoutTest(office, type);

      // Create a checkout payload with no payment option (empty payer config)
      const payload = createCheckoutPayload(objectWorkflows, undefined, type);

      const { body: checkoutResponse } = await payrollPaymentCheckout<PaymentsCheckoutResponse>(
        app,
        {
          officeId: office.id_escritorio,
          payload,
        },
        HttpStatus.CREATED,
      );

      // Verify the created payments
      const payrollPayments = await verifyPayrollPayments(
        checkoutResponse.payments.map(({ payroll_payment_id }) => payroll_payment_id),
        PayrollPaymentStatus.PAID,
        { type: PaymentOptionTypes.EMPTY },
      );

      expect(payrollPayments).toHaveLength(3);
      expect(checkoutResponse.has_input_error).toBeFalsy();

      // Verify each payment has the correct data
      const paymentCheckoutToCompare = payrollPayments.map(payrollPayment => ({
        object_workflow_id: payrollPayment.object_workflow_id,
        amount: payrollPayment.amount,
        status: payrollPayment.status,
        payer_config: payrollPayment.payer_config,
        creditor_payment_info: payrollPayment.creditor_payment_info,
      }));

      const expectedPayrollPayments = objectWorkflows.map(workflow => ({
        object_workflow_id: workflow.id,
        amount: '12345.67',
        status: PayrollPaymentStatus.PAID,
        payer_config: { type: PaymentOptionTypes.EMPTY },
        creditor_payment_info: {},
      }));

      expect(paymentCheckoutToCompare).toEqual(expectedPayrollPayments);
    });

    it('should save correct error if lambda fails', async () => {
      const { office } = await prepareLogin([PERMISSION_PAYMENTS], 'admin');

      // Setup test with CNAB payment option
      const { objectWorkflows, creditors, paymentOption } = await prepareCheckoutTest(
        office,
        type,
        {
          paymentType: PaymentOptionTypes.CNAB,
          prettyName: 'payment_checkout_test',
          payerConfig: { ...cnabConfig, type: PaymentOptionTypes.CNAB },
        },
      );

      // Setup creditor payment info
      await setupCreditorPaymentInfo(
        creditors,
        {
          ispb_code: '********',
          bank_code: '341',
          name: 'ITAÚ UNIBANCO S.A.',
        },
        {
          document_number: '***********',
          branch: '1234-5',
          account: '123-4',
          pix_key: '<EMAIL>',
        },
      );

      // Mock lambda error
      lambdaPaymentsService.sendCheckoutRequest.mockRejectedValueOnce(new Error('lambda_error'));

      // Create checkout payload with payment option
      const payload = createCheckoutPayload(
        [objectWorkflows[0], objectWorkflows[1]],
        paymentOption.id,
        type,
      );

      // Execute checkout
      const { body: checkoutResponse } = await payrollPaymentCheckout<PaymentsCheckoutResponse>(
        app,
        {
          officeId: office.id_escritorio,
          payload,
        },
        HttpStatus.CREATED,
      );

      // Verify payments were created with error status
      const payrollPayments = await Payroll_Payment.findAll({
        where: {
          object_workflow_id: [objectWorkflows[0].id, objectWorkflows[1].id],
        },
        order: ['id'],
      });

      expect(payrollPayments).toHaveLength(2);
      expect(checkoutResponse.has_input_error).toBeFalsy();

      // Verify error details
      payrollPayments.forEach(({ external_id, status, reason }) => {
        expect(external_id).toBe('ERROR');
        expect(status).toBe(PayrollPaymentStatus.FAILED);
        expect(reason).toEqual({
          code: 'INTERNAL_ERROR',
          message: 'lambda_error',
        });
      });
    });

    it('when lambda returns status, this status should be updated status payroll payment', async () => {
      const { office } = await prepareLogin([PERMISSION_PAYMENTS], 'payer');

      // Setup test with Stark API payment option
      const { objectWorkflows, paymentOption, creditors } = await prepareCheckoutTest(
        office,
        type,
        {
          paymentType: PaymentOptionTypes.API_STARK,
          prettyName: 'payment_checkout_test',
          payerConfig: starkSetupTest.payerConfig as PayerConfig,
        },
      );

      // Setup creditor payment info
      await setupCreditorPaymentInfo(
        creditors,
        {
          ispb_code: 'any_ispb_code',
          bank_code: 'any_code',
          name: 'any_name',
        },
        {
          document_number: '***********',
          branch: 'branch-branch_digit',
          account: 'account-account_digit',
        },
      );

      // Add an additional payment info that should be ignored
      const [financial_institution] = await Financial_Institution.findAll();
      await Creditor_Payment_Info.create({
        active: true,
        document_number: 'should_ignore',
        branch: 'should_ignore',
        account: 'should_ignore',
        financial_institution_id: financial_institution.id,
        creditor_id: creditors[0].id_credor,
        account_type: AccountType.PAYMENT,
      });

      // Mock lambda response with PENDING status
      const spySendCheckoutRequest = vitest.spyOn(lambdaPaymentsService, 'sendCheckoutRequest');
      spySendCheckoutRequest.mockImplementationOnce(
        ({
          payments,
        }: {
          payerOptions: Payment_Option;
          paymentBatchKey: string;
          payments: Payment[];
        }) => ({
          data: payments.map(({ payroll_checkout_id }) => ({
            status: PayrollPaymentStatus.PENDING,
            external_payment_id: 'XPTO',
            payroll_checkout_id,
          })),
        }),
      );

      // Mock secret retrieval
      secretVaultMock.getSecret.mockResolvedValueOnce(starkSetupTest.secretValueAsBase64);

      // Create checkout payload
      const payload = createCheckoutPayload(objectWorkflows, paymentOption.id, type);

      // Execute checkout
      const { body: checkoutResponse } = await payrollPaymentCheckout<PaymentsCheckoutResponse>(
        app,
        {
          officeId: office.id_escritorio,
          payload,
        },
        HttpStatus.CREATED,
      );

      // Verify all payments have PENDING status
      await verifyPayrollPayments(
        checkoutResponse.payments.map(({ payroll_payment_id }) => payroll_payment_id),
        PayrollPaymentStatus.PENDING,
        starkSetupTest.expectedPayerConfig,
      );
    });

    /********** Successful checkout cases  **********/
    const fileTypeCases = [
      {
        filename: 'itau_sheet.xlsx',
        contentType:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8',
        paymentType: PaymentOptionTypes.ITAU_SHEET,
        payerConfig: itauSheetConfig as PayerConfig,
        secret: null,
      },
      {
        filename: 'itau_cnab.ret',
        contentType: 'text/plain; charset=utf-8',
        paymentType: PaymentOptionTypes.CNAB,
        payerConfig: cnabConfig as PayerConfig,
        secret: null,
      },
    ];
    it.each(fileTypeCases)(
      'should return correct file for $paymentType',
      async ({ filename, contentType, paymentType, payerConfig }) => {
        const { office } = await prepareLogin([PERMISSION_PAYMENTS], 'payer');

        const { objectWorkflows, paymentOption, creditors } = await prepareCheckoutTest(
          office,
          type,
          {
            paymentType,
            prettyName: 'payment_checkout_test',
            payerConfig,
          },
        );

        await setupCreditorPaymentInfo(creditors);

        const fileMockAsBuffer = readFileSync(`test_resources/${filename}`);

        lambdaPaymentsService.sendCheckoutRequest.mockImplementationOnce(
          ({
            payments,
          }: {
            payerOptions: Payment_Option;
            paymentBatchKey: string;
            payments: Payment[];
          }) => ({
            file: fileMockAsBuffer,
            filename,
            type: contentType,
            data: payments.map(({ payroll_checkout_id }) => ({
              status: PayrollPaymentStatus.PAID,
              external_payment_id: 'XPTO',
              payroll_checkout_id,
            })),
          }),
        );

        const { body: checkoutResponse } = await payrollPaymentCheckout(
          app,
          {
            officeId: office.id_escritorio,
            payload: {
              payment_date: '2030-01-01',
              payer_config_id: paymentOption.id,
              type,
              payments_to_checkout: [
                {
                  object_workflow_id: objectWorkflows[0].id,
                  amount: 12345.67,
                },
                {
                  object_workflow_id: objectWorkflows[1].id,
                  amount: 12345.67,
                },
              ],
            },
          },
          HttpStatus.CREATED,
          contentType,
          'blob',
        );

        expect(checkoutResponse).toEqual(fileMockAsBuffer);

        const paymentCheckout = await Payroll_Payment.findAll({
          include: [
            {
              model: Object_Workflow,
              where: {
                office_id: office.id_escritorio,
                type,
                object_owner: creditors.map(({ id_credor }) => id_credor),
              },
            },
          ],
          order: ['id'],
        });

        expect(paymentCheckout).toHaveLength(2);

        const creditorsPaymentInfo = (creditorName: string, externalCreditorId: string) => ({
          external_creditor_id: externalCreditorId,
          creditor_name: creditorName,
          amount: 12345.67,
          cpf_or_cnpj: '***********',
          payment_date: '2030-01-01',
          transfer: {
            account: '123',
            account_digit: '4',
            account_type: 'payment_account',
            bank_code: '341',
            bank_ispb_code: '********',
            branch: '1234',
            type: 'account',
          },
        });

        paymentCheckout.map(({ payer_config }) => {
          expect(payer_config).toEqual(payerConfig);
        });

        const firstCreditor = creditors[0];
        const secondCreditor = creditors[1];

        const paymentsInfo = paymentCheckout.map(({ creditor_payment_info }) => {
          const { my_number, ...paymentInfo } = creditor_payment_info;

          expect(my_number).toBeTruthy();

          return paymentInfo;
        });

        const paymentsInfoUnique = new Map();
        paymentsInfo.forEach(paymentInfo => {
          paymentsInfoUnique.set(paymentInfo.external_creditor_id, paymentInfo);
        });

        expect(paymentsInfo).toEqual([
          creditorsPaymentInfo(firstCreditor.nome_credor, firstCreditor.id_credor_externo),
          creditorsPaymentInfo(secondCreditor.nome_credor, secondCreditor.id_credor_externo),
        ]);

        return Promise.resolve();
      },
    );

    const apiTypeCases = [
      {
        paymentType: PaymentOptionTypes.API_STARK,
        payerConfig: starkSetupTest.payerConfig as PayerConfig,
        secret: starkSetupTest.secretValueAsBase64,
        expectedPayerConfig: starkSetupTest.expectedPayerConfig,
      },
      {
        paymentType: PaymentOptionTypes.API_INTER,
        payerConfig: interSetupTest.payerConfig as PayerConfig,
        secret: interSetupTest.secretValueAsBase64,
        expectedPayerConfig: interSetupTest.expectedPayerConfig,
      },
      {
        paymentType: PaymentOptionTypes.API_KAMINO,
        payerConfig: kaminoSetupTest.payerConfig as PayerConfig,
        secret: kaminoSetupTest.secretValueAsBase64,
        expectedPayerConfig: kaminoSetupTest.expectedPayerConfig,
      },
    ];
    it.each(apiTypeCases)(
      'should create payroll payment correctly & return correct data',
      async ({ paymentType, payerConfig, secret, expectedPayerConfig }) => {
        const { office } = await prepareLogin([PERMISSION_PAYMENTS], 'payer');

        const { objectWorkflows, paymentOption, creditors } = await prepareCheckoutTest(
          office,
          type,
          {
            paymentType,
            prettyName: 'payment_checkout_test',
            payerConfig,
          },
        );

        const [financial_institution] = await Financial_Institution.findOrCreate({
          where: {
            ispb_code: 'any_ispb_code',
            bank_code: 'any_code',
            name: 'any_name',
          },
        });

        await Creditor_Payment_Info.create({
          active: true,
          document_number: 'should_ignore',
          branch: 'should_ignore',
          account: 'should_ignore',
          financial_institution_id: financial_institution.id,
          creditor_id: creditors[0].id_credor,
          account_type: AccountType.PAYMENT,
        });

        await Promise.all(
          creditors.map(creditor =>
            Creditor_Payment_Info.create({
              active: true,
              document_number: '***********',
              branch: 'branch-branch_digit',
              account: 'account-account_digit',
              financial_institution_id: financial_institution.id,
              creditor_id: creditor.id_credor,
              account_type: AccountType.PAYMENT,
            }),
          ),
        );

        lambdaPaymentsService.sendCheckoutRequest.mockImplementationOnce(
          ({
            payments,
          }: {
            payerOptions: Payment_Option;
            paymentBatchKey: string;
            payments: Payment[];
          }) => ({
            data: payments.map(({ payroll_checkout_id }) => ({
              created: true,
              external_payment_id: 'XPTO',
              payroll_checkout_id,
            })),
          }),
        );

        secretVaultMock.getSecret.mockResolvedValueOnce(secret);

        const { body: checkoutResponse } = await payrollPaymentCheckout<PaymentsCheckoutResponse>(
          app,
          {
            officeId: office.id_escritorio,
            payload: {
              payment_date: '2030-01-01',
              type,
              payer_config_id: paymentOption.id,
              payments_to_checkout: [
                {
                  object_workflow_id: objectWorkflows[0].id,
                  amount: 12345.67,
                },
                {
                  object_workflow_id: objectWorkflows[1].id,
                  amount: 12345.67,
                },
                {
                  object_workflow_id: objectWorkflows[2].id,
                  amount: 12345.67,
                },
              ],
            },
          },
          HttpStatus.CREATED,
        );

        const paymentCheckout = await Payroll_Payment.findAll({
          where: {
            id: checkoutResponse.payments.map(({ payroll_payment_id }) => payroll_payment_id),
          },
          order: ['id'],
        });

        expect(paymentCheckout).toHaveLength(3);
        expect(checkoutResponse.has_input_error).toBeFalsy();

        const creditorsPaymentInfo = (creditorName: string, externalCreditorId: string) => ({
          external_creditor_id: externalCreditorId,
          creditor_name: creditorName,
          amount: 12345.67,
          cpf_or_cnpj: '***********',
          payment_date: '2030-01-01',
          transfer: {
            type: 'account',
            account_type: 'payment_account',
            account: 'account',
            account_digit: 'account_digit',
            bank_code: 'any_code',
            branch: 'branch',
            branch_digit: 'branch_digit',
            bank_ispb_code: financial_institution.ispb_code,
          },
        });

        const paymentsInfo = paymentCheckout.map(({ payer_config, creditor_payment_info }) => {
          const { my_number, ...paymentInfo } = creditor_payment_info;

          expect(payer_config).toEqual(expectedPayerConfig);
          expect(my_number).toBeTruthy();

          return paymentInfo;
        });

        const paymentsInfoUnique = new Map();
        paymentsInfo.forEach(paymentInfo => {
          paymentsInfoUnique.set(paymentInfo.external_creditor_id, paymentInfo);
        });

        const paymentInfoSort = Array.from(paymentsInfoUnique.values()).sort((a, b) =>
          a.external_creditor_id.localeCompare(b.external_creditor_id),
        );

        const creditorsPaymentInfoExpected = creditors
          .map(creditor => creditorsPaymentInfo(creditor.nome_credor, creditor.id_credor_externo))
          .sort((a, b) => a.external_creditor_id.localeCompare(b.external_creditor_id));

        expect(paymentInfoSort).toEqual(creditorsPaymentInfoExpected);
      },
    );
    it.each(apiTypeCases)(
      '[$paymentType] validates that payments cannot be processed if there is any pending status, and ensures that in case of an error, the payments is not created',
      async ({ payerConfig, paymentType, secret }) => {
        const { office } = await prepareLogin([PERMISSION_PAYMENTS], 'admin');
        const { objectWorkflows, creditors, paymentOption } = await prepareCheckoutTest(
          office,
          type,
          {
            payerConfig,
            paymentType,
            prettyName: 'payment_checkout_test',
          },
        );
        await setupCreditorPaymentInfo(creditors);

        const mockWhenNeedSecret = () => {
          secretVaultMock.getSecret.mockResolvedValueOnce(secret);
          lambdaPaymentsService.sendCheckoutRequest.mockImplementationOnce(
            ({
              payments,
            }: {
              payerOptions: Payment_Option;
              paymentBatchKey: string;
              payments: Payment[];
            }) => ({
              data: payments.map(({ payroll_checkout_id }) => ({
                created: true,
                external_payment_id: 'XPTO',
                payroll_checkout_id,
              })),
            }),
          );
        };

        const sendCheckoutRequestAndValidate = async (
          payload: PaymentCheckoutRequest,
          expectedPayrollPayments: {
            object_workflow_id: number;
            status: PayrollPaymentStatus;
          }[],
          expectedStatusCode: HttpStatus = HttpStatus.CREATED,
          expectedResponse?,
        ) => {
          if (secret) mockWhenNeedSecret();
          const { body: checkoutResponse } = await payrollPaymentCheckout<PaymentsCheckoutResponse>(
            app,
            {
              officeId: office.id_escritorio,
              payload,
            },
            expectedStatusCode,
          );

          if (expectedResponse) {
            expect(checkoutResponse).toEqual(expectedResponse);
          }

          const payrollPayments = await Payroll_Payment.findAll({
            include: [
              {
                model: Object_Workflow,
                as: 'object_workflow',
                where: {
                  office_id: office.id_escritorio,
                },
              },
            ],
          });

          const payrollPaymentsCleaning = payrollPayments
            .map(p => ({
              object_workflow_id: p.object_workflow_id,
              amount: p.amount,
              status: p.status,
            }))
            .sort((a, b) => a.object_workflow_id - b.object_workflow_id);

          const expectedPayrollPaymentsSorted = expectedPayrollPayments.sort(
            (a, b) => a.object_workflow_id - b.object_workflow_id,
          );
          expect(payrollPaymentsCleaning).toEqual(expectedPayrollPaymentsSorted);
        };

        const successPaidPayload = {
          payment_date: '2030-01-01',
          type,
          payments_to_checkout: [
            {
              object_workflow_id: objectWorkflows[0].id,
              payer_config_id: paymentOption.id,
              amount: 10000.0,
            },
            {
              object_workflow_id: objectWorkflows[1].id,
              payer_config_id: paymentOption.id,
              amount: 500.0,
            },
            {
              object_workflow_id: objectWorkflows[2].id,
              payer_config_id: paymentOption.id,
              amount: 55.0,
            },
          ],
        };

        const expectedPayrollPayments = [
          {
            object_workflow_id: objectWorkflows[0].id,
            amount: '10000.00',
            status: PayrollPaymentStatus.PAID,
          },
          {
            object_workflow_id: objectWorkflows[1].id,
            amount: '500.00',
            status: PayrollPaymentStatus.PAID,
          },
          {
            object_workflow_id: objectWorkflows[2].id,
            amount: '55.00',
            status: PayrollPaymentStatus.PAID,
          },
        ];
        await sendCheckoutRequestAndValidate(successPaidPayload, expectedPayrollPayments);

        //  update to pending status to simulate a pending payment
        await Payroll_Payment.update(
          { status: PayrollPaymentStatus.PENDING },
          {
            where: {
              object_workflow_id: objectWorkflows[0].id,
            },
          },
        );

        //  expected error, because has payroll payment with pending status
        const failedPaidPayload = {
          payment_date: '2030-01-01',
          type,
          payments_to_checkout: [
            {
              object_workflow_id: objectWorkflows[0].id,
              payer_config_id: paymentOption.id,
              amount: 500.67,
            },
            {
              object_workflow_id: objectWorkflows[1].id,
              payer_config_id: paymentOption.id,
              amount: 150.67,
            },
          ],
        };

        expectedPayrollPayments[0].status = PayrollPaymentStatus.PENDING;
        const expectedResponse = {
          code: BackendErrorCodes.PAYROLL_PAYMENT_INPUT_ERROR,
          message: 'Informações da forma de pagamento invalida ou pagamentos inválidos.',
          data: {
            data: [
              {
                object_workflow_id: expect.any(Number),
                reason: {
                  code: 'INVALID_STATUS',
                  message: 'Payment is already in progress',
                },
              },
              {
                object_workflow_id: expect.any(Number),
              },
            ],
          },
        };
        await sendCheckoutRequestAndValidate(
          failedPaidPayload,
          expectedPayrollPayments,
          HttpStatus.BAD_REQUEST,
          expectedResponse,
        );

        // TRYING TO PAY THE SAME PAYROLL_CREDITOR_GROUP AGAIN
        const failedPaidPayload2 = {
          payment_date: '2030-01-01',
          type,
          payments_to_checkout: [
            {
              object_workflow_id: objectWorkflows[0].id,
              payer_config_id: paymentOption.id,
              amount: 750.5,
            },
            {
              object_workflow_id: objectWorkflows[1].id,
              payer_config_id: paymentOption.id,
              amount: 602.24,
            },
          ],
        };

        await sendCheckoutRequestAndValidate(
          failedPaidPayload2,
          expectedPayrollPayments,
          HttpStatus.BAD_REQUEST,
          expectedResponse,
        );
        return Promise.resolve();
      },
    );

    /********** Input error cases  **********/
    const allCases = [...apiTypeCases, ...fileTypeCases];
    it.each(allCases)(
      'should return input error if validations fail',
      async ({ paymentType, payerConfig, secret }) => {
        const { office } = await prepareLogin([PERMISSION_PAYMENTS], 'admin');

        const { objectWorkflows, paymentOption, creditors } = await prepareCheckoutTest(
          office,
          type,
          {
            paymentType,
            prettyName: 'payment_checkout_test',
            payerConfig,
          },
        );

        await setupCreditorPaymentInfo(creditors, undefined, {
          active: true,
          document_number: '',
          branch: '',
          account: '',
          account_type: AccountType.PAYMENT,
        });

        secretVaultMock.getSecret.mockResolvedValueOnce(secret);

        const { body: checkoutResponse } = await payrollPaymentCheckout<any>(
          app,
          {
            officeId: office.id_escritorio,
            payload: {
              payment_date: '2030-01-01',
              type,
              payer_config_id: paymentOption.id,
              payments_to_checkout: [
                {
                  object_workflow_id: objectWorkflows[0].id,
                  amount: 12345.67,
                },
                {
                  object_workflow_id: objectWorkflows[1].id,
                  amount: 12345.67,
                },
              ],
            },
          },
          HttpStatus.BAD_REQUEST,
        );

        const allErrorMessages = [
          'O número do documento é inválido',
          'O número da conta é inválido',
          'O dígito da conta é inválido',
          'O número da agência é inválido',
        ];

        expect(checkoutResponse).toEqual({
          code: BackendErrorCodes.PAYROLL_PAYMENT_INPUT_ERROR,
          message: 'Informações da forma de pagamento invalida ou pagamentos inválidos.',
          data: {
            data: [
              {
                object_workflow_id: expect.any(Number),
                reason: {
                  code: 'INPUT_ERROR',
                  message: expect.any(String),
                },
              },
              {
                object_workflow_id: expect.any(Number),
                reason: {
                  code: 'INPUT_ERROR',
                  message: expect.any(String),
                },
              },
            ],
          },
        });
        checkoutResponse.data?.data.forEach(({ reason }) => {
          expect(reason.code).toBe('INPUT_ERROR');
          reason.message.split(';').forEach(message => {
            expect(allErrorMessages).toContain(message.trim());
          });
        });
      },
    );
  });
});
