import { File, StorageType } from '@app/models/file';
import { Ocr_Extraction_Config } from '@app/ocr/models/ocr_extraction_config';
import { Ocr_Extraction_Result } from '@app/ocr/models/ocr_extraction_result';
import { Ocr_Validation_Config } from '@app/ocr/models/ocr_validation_config';
import { Ocr_Validation_Result } from '@app/ocr/models/ocr_validation_result';
import {
  OcrExtractionModel,
  OcrExtractionProvider,
  OcrExtractionType,
  ValidationConfig,
} from 'shared-types';
import uuid from 'uuid';

export async function createValidationResult(
  officeId: number,
  opts: { validationConfig?: ValidationConfig[] } = {},
) {
  const file = await createFile({ officeId, checksum: uuid() });
  const secondFile = await createFile({ officeId, checksum: uuid() });
  const extractionConfig = await createExtractionConfig({ officeId });
  const extractionResult = await createExtractionResult({
    configId: extractionConfig.id,
    fileId: file.id,
    result: {
      key: 'value',
    },
  });
  const secondExtractionResult = await createExtractionResult({
    configId: extractionConfig.id,
    fileId: secondFile.id,
    result: {},
  });
  const validationConfig = await createValidationConfig({
    officeId,
    config: opts.validationConfig,
  });
  const validationResult = await Ocr_Validation_Result.create({
    ocr_extraction_result_id: extractionResult.id,
    ocr_validation_config_id: validationConfig.id,
    result: [{ formula: 'IF(1 == 1, true, false)', success: true }],
  });

  const secondValidationResult = await Ocr_Validation_Result.create({
    ocr_extraction_result_id: secondExtractionResult.id,
    ocr_validation_config_id: validationConfig.id,
    result: [{ formula: 'IF(1 == 1, true, false)', success: true }],
  });

  return {
    validationConfig,
    validationResult,
    secondValidationResult,
    extractionConfig,
    files: [file, secondFile],
  };
}

export async function createFile({ officeId, checksum }: { officeId: number; checksum?: string }) {
  return File.create({
    office_id: officeId,
    storage_name: 'storage_name',
    storage: StorageType.S3,
    checksum: checksum ?? 'checksum',
    name: 'file',
    key: 'key',
    size: 1,
    type: 'type',
  });
}

export async function createExtractionConfig({ officeId }: { officeId: number }) {
  return Ocr_Extraction_Config.create({
    office_id: officeId,
    type: OcrExtractionType.NF,
    provider: OcrExtractionProvider.GOOGLE,
    model: OcrExtractionModel.GEMNI_PRO_1_5,
    prompt: 'prompt',
  });
}

export async function createExtractionResult({
  configId,
  fileId,
  result = {
    key: 'value',
  },
}: {
  configId: number;
  fileId: number;
  result?: object;
}) {
  return Ocr_Extraction_Result.create({
    file_id: fileId,
    ocr_extration_config_id: configId,
    result,
  });
}

export async function createValidationConfig({
  officeId,
  config = [],
}: {
  officeId: number;
  config?: ValidationConfig[];
}) {
  return Ocr_Validation_Config.create({
    office_id: officeId,
    config,
  });
}
