import { vi, Mocked } from 'vitest';
import { HttpStatus, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import bodyParser from 'body-parser';
import { Logger } from 'nestjs-pino';
import {
  BackendErrorCodes,
  GetUserManagementDateResponse,
  GetUserManagementDateRows,
  PaginatedRowResponse,
  PayoutPeriodResponse,
  PermissionProfileResponse,
  RowValues,
  SimpleTabResponse,
  TabType,
  User,
  UserManagementDiffStatus,
  UsersClaims,
} from 'shared-types';
import { AppModule } from '../../src/app.module';
import { ConfigurationEnv } from '../../src/config/configuration.env';
import { Atributos_credor } from '../../src/models/atributos_credor';
import { Credor } from '../../src/models/credor';
import AWSService from '../../src/services/aws.sdk.service';
import {
  autoProcessEndpoint,
  bulkUpsertUserCheckEndpoint,
  bulkUpsertUserConfigEndpoint,
  bulkUpsertUserPermissionCheckEndpoint,
  bulkUpsertUserPermissionQueryEndpoint,
  bulkUpsertUserQueryEndpoint,
  createPermissionProfile,
  createUserEndpoint,
  deleteCreditorsEndpoint,
  getUserEndpoint,
  updateClaims,
  userManagementUpsertDiffEndpoint,
} from '../utils/common.requests';
import {
  createCreditorsForTest,
  createTestOffice,
  mockCognitoReturnSuccess,
  mockCognitoReturnSuccessAlways,
  mockSesReturnSuccess,
  processComissionClosureFile,
  uploadFile,
  validateUserPermissions,
} from '../utils/massa.utils';
import { v4 as uuid } from 'uuid';
import { User_Identifier } from '@app/models/user_identifier';
import { SheetDbApiService } from '@app/services/sheet.db.api.service';
import { User_Permission } from '@app/models/user_permission';
import { Teams } from '@app/models/teams';
import { Permission_Profile } from '@app/models/permission_profile';
import { Creditor_Teams } from '@app/models/creditor_teams';
import { GooglePubSubService } from '@app/services/google/pubsub.service';
import {
  setupMockLoginDomain,
  simulateLoggedUserBuilder,
  SimulateLoggedUserData,
} from '../mocks/login.domain.mock';
import { CLAIMS_ADMIN, USER_CREATE } from './payroll/utils';
import { GaxiosResponse } from 'gaxios';
import { Escritorio } from '@app/models/escritorio';
import { UserDomain } from '@app/domain/user/user.domain';

vi.mock('../../src/services/aws.sdk.service');
vi.mock('starkbank');
vi.mock('../../src/document.signature/service/document.signature.api.service');

const AWS_COGNITO_ID = 1;

const loggedUserClaims = [
  { atributo: UsersClaims.USER_CREATE, valor: '1' },
  { atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' },
];

const userCreationClaims = [
  { name: UsersClaims.USER_CREATE, value: 'true' },
  { name: UsersClaims.CLOSURE_UPDATE, value: 'true' },
];

const noUserCreationClaim = [{ atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' }];

const awsSdk = AWSService as unknown as Mocked<AWSService>;

const sheetDbService = SheetDbApiService as unknown as Mocked<SheetDbApiService>;
const googlePubSubMock = {
  sendMessage: vi.fn(),
};

const config = new ConfigurationEnv();
describe('User POST (e2e)', () => {
  let app: INestApplication;
  let mockLoadAuthContext: ReturnType<typeof setupMockLoginDomain>;
  let userDomain: UserDomain;
  beforeAll(async () => {
    Object.defineProperty(config, 'tokenValidationEnabled', { value: false });
    Object.defineProperty(awsSdk, 'cognito', { value: {} });
    Object.defineProperty(awsSdk, 'ses', { value: {} });

    Object.defineProperty(sheetDbService, 'getTabs', { value: vi.fn(), configurable: true });
    Object.defineProperty(sheetDbService, 'getPayoutPeriods', {
      value: vi.fn(),
      configurable: true,
    });
    Object.defineProperty(sheetDbService, 'getRows', { value: vi.fn(), configurable: true });

    const moduleBuilder = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .overrideProvider(AWSService)
      .useValue(awsSdk)
      .overrideProvider(SheetDbApiService)
      .useValue(sheetDbService)
      .overrideProvider(GooglePubSubService)
      .useValue(googlePubSubMock);
    mockLoadAuthContext = setupMockLoginDomain(moduleBuilder);
    const module = await moduleBuilder.compile();
    module.useLogger(module.get(Logger));

    app = module.createNestApplication({ bodyParser: false });
    app.use(bodyParser.json({ limit: '50mb' }));
    app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));
    await app.init();

    userDomain = app.get<UserDomain>(UserDomain);
  });

  const simulateLoggedUser = (params: SimulateLoggedUserData) => {
    simulateLoggedUserBuilder(mockLoadAuthContext)(params);
  };

  afterAll(() => {
    vi.clearAllMocks();
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('create user', () => {
    const createCases = [
      {
        describe: 'success create because user is admin (closure update)',
        claims: [{ atributo: UsersClaims.CLOSURE_UPDATE, valor: '1' }],
        expectedHttpStatus: HttpStatus.FORBIDDEN,
        expectedTeams: [],
        expectedProfiles: [],
        expectedResponse: {
          error: 'Forbidden',
          message: 'Forbidden resource',
          statusCode: HttpStatus.FORBIDDEN,
        },
      },
      {
        description: 'success create because user is admin (user create)',
        claims: [{ atributo: UsersClaims.USER_CREATE, valor: '1' }],
        expectedHttpStatus: HttpStatus.CREATED,
        expectedTeams: ['TEAM_A', 'TEAM_B'],
        expectedProfiles: [], // because not has claim manage.permission.profile
        expectedResponse: undefined,
      },
      {
        description:
          'successfully creates a user and set permission profiles when requester has user.create and manage.permission.profiles claims',
        claims: [
          { atributo: UsersClaims.USER_CREATE, valor: '1' },
          { atributo: UsersClaims.MANAGE_PERMISSION_PROFILES, valor: '1' },
        ],
        expectedHttpStatus: HttpStatus.CREATED,
        expectedTeams: ['TEAM_A', 'TEAM_B'],
        expectedProfiles: ['ADMIN_USER', 'DEFAULT_USER'],
      },
      {
        description:
          'fails to create when requester has user.management claim, but allowed_create is false',
        claims: [
          {
            atributo: UsersClaims.USER_MANAGEMENT,
            valor: '{"teams":[],"profiles":[],"allow_create":false,"allow_delete":false}',
          },
        ],
        expectedHttpStatus: HttpStatus.FORBIDDEN,
        expectedResponse: {
          code: BackendErrorCodes.USER_MANAGEMENT_NOT_ALLOWED_CREATE,
          data: {
            claim: {
              allow_create: false,
              allow_delete: false,
              profiles: [],
              teams: [],
            },
          },
          message: 'Você não tem permissão para criar usuários',
        },
        expectedTeams: [],
        expectedProfiles: [],
      },
      {
        description:
          'successfully creates user, but only one team and one profile are added based on user.management profiles/teams scoping',
        claims: [
          {
            atributo: UsersClaims.USER_MANAGEMENT,
            valor:
              '{"teams":["TEAM_B"],"profiles":["DEFAULT_USER"],"allow_create":true,"allow_delete":false}',
          },
        ],
        expectedHttpStatus: HttpStatus.CREATED,
        expectedTeams: ['TEAM_B'],
        expectedProfiles: ['DEFAULT_USER'],
      },
      {
        description:
          'successfully creates a user, but with no teams and profiles, when user.management claim has empty allowed profiles/teams',
        claims: [
          {
            atributo: UsersClaims.USER_MANAGEMENT,
            valor: '{"teams":[],"profiles":[],"allow_create":true,"allow_delete":false}',
          },
        ],
        expectedHttpStatus: HttpStatus.CREATED,
        expectedTeams: [],
        expectedProfiles: [],
        expectedResponse: null,
      },
    ];
    it.each(createCases)(
      'validate create user request: $description',
      async ({ claims, expectedProfiles, expectedTeams, expectedHttpStatus, expectedResponse }) => {
        const office = await createTestOffice();
        const loggedUser = await Credor.create({
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: 'LOGGED_USER',
          ativo: true,
          nome_credor: 'Usuário logado',
        });

        simulateLoggedUser({
          office,
          credor: loggedUser,
          claims: [
            {
              atributo: UsersClaims.MANAGE_PERMISSION_PROFILES,
              valor: true,
            },
          ],
        });

        const { body: adminProfile } = await createPermissionProfile<PermissionProfileResponse>(
          app,
          {
            officeId: office.id_escritorio,
            payload: {
              name: 'ADMIN_USER',
              permission_data: [
                {
                  name: UsersClaims.OFFICE_CREDITORS_VIEW,
                  value: 'true',
                },
              ],
            },
          },
        );

        const { body: defaultUserProfile } =
          await createPermissionProfile<PermissionProfileResponse>(app, {
            officeId: office.id_escritorio,
            payload: {
              name: 'DEFAULT_USER',
              permission_data: [
                {
                  name: UsersClaims.PAYROLL_VIEW,
                  value: 'true',
                },
              ],
            },
          });

        simulateLoggedUser({
          office,
          credor: loggedUser,
          claims,
          teams: ['TEAM_A', 'TEAM_B'],
        });

        const response = await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: 'A1',
            name: 'A1',
            permission_profile_ids: [adminProfile.id, defaultUserProfile.id],
            teams: ['TEAM_A', 'TEAM_B'],
          },
          expectedHttpStatus,
        );

        if (expectedHttpStatus !== HttpStatus.CREATED) {
          expect(response.body).toEqual(expectedResponse);
          return Promise.resolve();
        }

        const userTeams = await Creditor_Teams.findAll({
          where: {
            creditor_id: response.body.id_credor,
          },
          include: [Teams],
        });

        const userTeamsName = userTeams.map(userTeam => userTeam.team.name);
        expect(userTeamsName).toEqual(expectedTeams);

        const userProfiles = await User_Permission.findAll({
          where: {
            creditor_id: response.body.id_credor,
          },
          include: [Permission_Profile],
        });

        const userProfilesName = userProfiles.map(
          userProfile => userProfile.permission_profile.name,
        );

        expect(userProfilesName).toEqual(expectedProfiles);
        return Promise.resolve();
      },
    );

    it('validates that when requester has user.management and creates a new user, its external_creditor_id gets added on requester creditor.view.permission claim', async () => {
      const office = await createTestOffice();
      await createCreditorsForTest(office.id_escritorio);
      const loggedUser = await Credor.create({
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: 'LOGGED_USER',
        ativo: true,
        nome_credor: 'Usuário logado',
      });

      const creditorsViewPermission = JSON.stringify(['A2', 'A3']);
      await Atributos_credor.create({
        credor_id_credor: loggedUser.id_credor,
        atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
        valor: creditorsViewPermission,
      });

      simulateLoggedUser({
        office,
        credor: loggedUser,
        claims: [
          {
            atributo: UsersClaims.USER_MANAGEMENT,
            valor: '{"teams":[],"profiles":[],"allow_create":true,"allow_delete":false}',
          },
          {
            atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
            valor: creditorsViewPermission,
          },
        ],
      });

      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: 'A10',
          name: 'A10',
        },
        HttpStatus.CREATED,
      );

      const creditorsCanSeeClaim = await Atributos_credor.findOne({
        where: {
          atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
          credor_id_credor: loggedUser.id_credor,
        },
      });

      const expectedClaim = JSON.stringify(['A2', 'A3', 'A10']);

      expect(creditorsCanSeeClaim.valor).toEqual(expectedClaim);

      return Promise.resolve();
    });
  });

  describe('When user doesnt have required permissions', () => {
    it('Returns Forbidden', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: noUserCreationClaim });
      await createUserEndpoint(app, office, {}, HttpStatus.FORBIDDEN);
      return Promise.resolve();
    });
  });

  describe('When request has missing external_creditor_id', () => {
    it('Returns Bad Request', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: loggedUserClaims });
      await createUserEndpoint(app, office, {}, HttpStatus.BAD_REQUEST);
      return Promise.resolve();
    });
  });

  describe('When creditor ID exists on office', () => {
    describe('And he already has an associated email', () => {
      it('Returns CONFLICT', async () => {
        const office = await createTestOffice();
        simulateLoggedUser({ office, claims: loggedUserClaims });

        const uploadFileResponse = await uploadFile(app, 'test_resources/comissoes_exemplo.xlsx');

        await processComissionClosureFile(
          app,
          office,
          '1990-12-01',
          uploadFileResponse.body.fullPath,
        );

        const creditorOnClosureFile = 'A69220';

        const insertedCreditor = await Credor.findOne({
          where: {
            escritorio_id_escritorio: office.id_escritorio,
            id_credor_externo: creditorOnClosureFile,
          },
        });
        insertedCreditor.email = `${office.id_escritorio}-<EMAIL>`;
        await insertedCreditor.save();

        await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: creditorOnClosureFile,
            name: 'Lucas Segers Fabro',
            email: insertedCreditor.email,
          },
          HttpStatus.CONFLICT,
        );

        return Promise.resolve();
      });

      describe('And already has an email, but its different from the request', () => {
        it('Returns CONFLICT', async () => {
          const office = await createTestOffice();
          simulateLoggedUser({ office, claims: loggedUserClaims });

          const uploadFileResponse = await uploadFile(app, 'test_resources/comissoes_exemplo.xlsx');

          await processComissionClosureFile(
            app,
            office,
            '1990-10-01',
            uploadFileResponse.body.fullPath,
          );

          const creditorOnClosureFile = 'A69220';

          const insertedCreditor = await Credor.findOne({
            where: {
              escritorio_id_escritorio: office.id_escritorio,
              id_credor_externo: creditorOnClosureFile,
            },
          });
          insertedCreditor.email = `${office.id_escritorio}-<EMAIL>`;
          await insertedCreditor.save();

          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: creditorOnClosureFile,
              name: 'Lucas Segers Fabro',
              email: `${office.id_escritorio}-<EMAIL>`,
            },
            HttpStatus.CONFLICT,
          );

          return Promise.resolve();
        }, 30000);
      });

      describe('And has no email', () => {
        it.only('Registers user successfully', async () => {
          const office = await createTestOffice();
          simulateLoggedUser({ office, claims: loggedUserClaims });

          const uploadFileResponse = await uploadFile(app, 'test_resources/2_rows.xlsx');

          await processComissionClosureFile(
            app,
            office,
            '1990-11-01',
            uploadFileResponse.body.fullPath,
          );

          const adminCreateUserReturn = { User: { Username: uuid() } };
          mockCognitoReturnSuccess(adminCreateUserReturn);
          const provider_id = adminCreateUserReturn.User.Username;

          mockSesReturnSuccess();

          const creditorOnClosureFile = 'A67112';
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: creditorOnClosureFile,
              name: 'Lucas Segers Fabro',
              email: `${office.id_escritorio}-<EMAIL>`,
              reset_password: true,
            },
            HttpStatus.CREATED,
          );

          const registeredCreditor = await Credor.findOne({
            where: {
              escritorio_id_escritorio: office.id_escritorio,
              id_credor_externo: creditorOnClosureFile,
            },
          });

          const registeredUserIdentifier = await User_Identifier.findOne({
            where: { oidc_id: AWS_COGNITO_ID, provider_id },
          });
          expect(registeredCreditor).not.toBeNull();
          expect(registeredUserIdentifier.creditor_id).toBe(registeredCreditor.id_credor);
          return Promise.resolve();
        });
      });
    });

    it('reactivate and clean deleted_at if creditor has been soft deleted', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: loggedUserClaims });
      const creditorToRecreateId = 'A67111';
      const creditorToReceiveId = 'A12345';

      const recreatedCreditorName = 'Lucas Segers Fabro';
      const recreatedCreditorEmail = `${office.id_escritorio}-<EMAIL>`;
      const recreatedCreditorPermissionType = 'closure.update';

      const uploadFileResponse = await uploadFile(
        app,
        'test_resources/10_rows_with_multiple_creditors.xlsx',
      );

      await processComissionClosureFile(
        app,
        office,
        '2022-01-01',
        uploadFileResponse.body.fullPath,
      );

      const creditorToDelete: Credor = await Credor.findOne({
        where: {
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: creditorToRecreateId,
        },
      });

      const creditorToDeletePermissions: Atributos_credor[] = await Atributos_credor.findAll({
        where: { credor_id_credor: creditorToDelete.id_credor },
      });

      expect(creditorToDelete.deleted_at).toBeNull();
      expect(creditorToDelete.email).toBeNull();
      expect(creditorToDelete.nome_credor).toBe(creditorToRecreateId);
      expect(creditorToDeletePermissions.length).toBe(1);

      await deleteCreditorsEndpoint(
        app,
        office,
        creditorToRecreateId,
        { merge_commissions_to: creditorToReceiveId },
        HttpStatus.OK,
      );

      const adminCreateUserReturn = { User: { Username: uuid() } };
      mockCognitoReturnSuccess(adminCreateUserReturn);
      const provider_id = adminCreateUserReturn.User.Username;

      mockSesReturnSuccess();

      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: creditorToRecreateId,
          name: recreatedCreditorName,
          email: recreatedCreditorEmail,
          reset_password: true,
        },
        HttpStatus.CREATED,
      );

      await updateClaims(app, {
        officeId: office.id_escritorio,
        externalCreditorId: creditorToRecreateId,
        payload: userCreationClaims,
      });

      const recreatedCreditorData = (await getUserEndpoint(app, office, creditorToRecreateId))
        .body as User;

      expect(recreatedCreditorData.active).toBeTruthy();
      expect(recreatedCreditorData.email).toBe(recreatedCreditorEmail);
      expect(recreatedCreditorData.name).toBe(recreatedCreditorName);

      expect(recreatedCreditorData.permissions.length).toBe(userCreationClaims.length);

      validateUserPermissions(recreatedCreditorData.permissions, [
        { name: UsersClaims.CLOSURE_UPDATE, value: 'true' },
        { name: UsersClaims.USER_CREATE, value: 'true' },
      ]);

      const recreatedCreditor: Credor = await Credor.findOne({
        where: {
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: creditorToRecreateId,
        },
      });
      const recreatedUserIdentifier = await User_Identifier.findOne({
        where: {
          provider_id,
          oidc_id: AWS_COGNITO_ID,
        },
      });
      expect(creditorToDelete.id_credor).toBe(recreatedCreditor.id_credor);
      expect(recreatedCreditor.deleted_at).toBeNull();
      expect(recreatedUserIdentifier.creditor_id).toBe(recreatedUserIdentifier.creditor_id);
      return Promise.resolve();
    });
  });

  describe('When creditor doesnt exist on office and doesnt yes exist on remote userpool', () => {
    it('should send invitation email when reset_password is true on request (create on cognito)', async () => {
      const office = await createTestOffice();
      const userToCreate = 'A0000003';

      const adminCreateUserReturn = { User: { Username: uuid() } };
      mockCognitoReturnSuccess(adminCreateUserReturn);
      const provider_id = adminCreateUserReturn.User.Username;

      mockSesReturnSuccess();

      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: userToCreate,
          name: 'Lucas Segers Fabro',
          email: `${office.id_escritorio}-<EMAIL>`,
          reset_password: true,
        },
        HttpStatus.CREATED,
      );

      const registeredCreditor = await Credor.findOne({
        where: {
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: userToCreate,
        },
      });
      const registeredUserIdentifier = await User_Identifier.findOne({
        where: { oidc_id: AWS_COGNITO_ID, provider_id },
      });
      expect(registeredCreditor).not.toBeNull();
      expect(awsSdk.cognito.adminCreateUser).toHaveBeenCalled();
      expect(registeredUserIdentifier.creditor_id).toBe(registeredCreditor.id_credor);
      expect(awsSdk.ses.sendBulkTemplatedEmail).toHaveBeenCalled();
      return Promise.resolve();
    });

    it('should not send invitation email when reset_password is false or not present on request (create on cognito)', async () => {
      const office = await createTestOffice();
      const userToCreate = 'A0000003';

      const adminCreateUserReturn = { User: { Username: uuid() } };
      mockCognitoReturnSuccess(adminCreateUserReturn);
      const provider_id = adminCreateUserReturn.User.Username;

      awsSdk.ses.sendBulkTemplatedEmail = vi.fn();
      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: userToCreate,
          name: 'Lucas Segers Fabro',
          email: `${office.id_escritorio}-<EMAIL>`,
        },
        HttpStatus.CREATED,
      );

      const registeredCreditor = await Credor.findOne({
        where: {
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: userToCreate,
        },
      });
      const registeredUserIdentifier = await User_Identifier.findOne({
        where: { oidc_id: AWS_COGNITO_ID, provider_id },
      });
      expect(registeredCreditor).not.toBeNull();
      expect(awsSdk.cognito.adminCreateUser).toHaveBeenCalled();
      expect(registeredUserIdentifier.creditor_id).toBe(registeredCreditor.id_credor);
      expect(awsSdk.ses.sendBulkTemplatedEmail).not.toHaveBeenCalled();
      return Promise.resolve();
    });

    it('should not create user on cognito when email is not provided on request', async () => {
      const office = await createTestOffice();
      const userToCreate = 'A0000003';

      awsSdk.cognito.adminCreateUser = vi.fn();
      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: userToCreate,
          name: 'Lucas Segers Fabro',
        },
        HttpStatus.CREATED,
      );

      const registeredCreditor = await Credor.findOne({
        where: {
          escritorio_id_escritorio: office.id_escritorio,
          id_credor_externo: userToCreate,
        },
      });
      const registeredUserIdentifier = await User_Identifier.findAll({
        where: { oidc_id: AWS_COGNITO_ID, creditor_id: userToCreate },
      });
      expect(registeredCreditor).not.toBeNull();
      expect(awsSdk.cognito.adminCreateUser).not.toHaveBeenCalled();
      expect(registeredUserIdentifier.length).toBe(0);
      return Promise.resolve();
    });

    it('Returns CONFLICT if sent email is already registered for another user', async () => {
      const office = await createTestOffice();
      const userToCreate = 'A0000003';
      const userEmail = `${office.id_escritorio}-<EMAIL>`;
      mockCognitoReturnSuccess();

      simulateLoggedUser({ office, claims: loggedUserClaims });
      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: userToCreate,
          name: 'Lucas Segers Fabro',
          email: userEmail,
        },
        HttpStatus.CREATED,
      );

      mockCognitoReturnSuccess();

      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: 'some other user',
          name: 'Bruce Banner',
          email: userEmail,
        },
        HttpStatus.CONFLICT,
      );

      return Promise.resolve();
    });
  });

  describe('Bulk', () => {
    describe('Query', () => {
      const mockTab = (id: number, planId?: number): SimpleTabResponse => ({
        id,
        name: '1',
        type: TabType.MANUAL,
        row_count: 10,
        tab_key: '1',
        created_at: '',
        updated_at: '',
        error_count: 0,
        evolved_from: null,
        root_tab: null,
        ...(planId
          ? {
              plan: {
                id: planId,
                name: 'some-plan',
              },
            }
          : {}),
      });
      const mockPayoutPeriod = (id: number, planId: number): PayoutPeriodResponse => ({
        id,
        plan_id: planId,
        locked: false,
        name: 'some-payout-period',
        payout_date: '',
        start_date: '',
        end_date: '',
        created_at: '',
        updated_at: '',
        total_panels: 0,
        visible_panels: 0,
      });
      const mockRow = (
        rows: RowValues[],
        pagination?: {
          page?: number;
          totalItems?: number;
          totalPages?: number;
        },
      ): PaginatedRowResponse => ({
        rows,
        pagination: {
          page: pagination?.page ?? 1,
          total_items: pagination?.totalItems ?? rows.length,
          total_pages: pagination?.totalPages ?? 1,
        },
      });

      describe('Users', () => {
        describe('should return user data from tab', () => {
          describe('and tab is not in a commission plan', () => {
            it('should not call payout period endpoint', async () => {
              const office = await createTestOffice();

              vi.spyOn(sheetDbService, 'getTabs').mockResolvedValueOnce([mockTab(1)]);
              vi.spyOn(sheetDbService, 'getRows')
                .mockResolvedValueOnce(
                  mockRow([
                    {
                      '1': 'A0001',
                      '2': 'João',
                      '3': '<EMAIL>',
                      '4': 'true',
                      '5': 'true',
                    },
                    {
                      '1': 'A0002',
                      '2': 'José',
                      '3': '<EMAIL>',
                      '4': 'true',
                      '5': 'true',
                    },
                  ]),
                )
                .mockResolvedValue(mockRow([], { totalItems: 2 }));
              const result = await bulkUpsertUserQueryEndpoint(
                app,
                office,
                [
                  {
                    tab_id: 1,
                    column_creditor_attributes: {
                      external_creditor_id: '1',
                      name: '2',
                      email: '3',
                      active: '4',
                      send_invite: '5',
                    },
                  },
                ],
                HttpStatus.OK,
              );

              expect(sheetDbService.getPayoutPeriods).not.toHaveBeenCalled();
              expect(result).toEqual(
                expect.arrayContaining([
                  {
                    external_creditor_id: 'A0001',
                    name: 'João',
                    email: '<EMAIL>',
                    send_invite: true,
                    active: true,
                  },
                  {
                    external_creditor_id: 'A0002',
                    name: 'José',
                    email: '<EMAIL>',
                    send_invite: true,
                    active: true,
                  },
                ]),
              );
            });

            it('should call next batch of rows', async () => {
              const office = await createTestOffice();

              vi.spyOn(sheetDbService, 'getTabs').mockResolvedValueOnce([mockTab(1)]);
              vi.spyOn(sheetDbService, 'getRows')
                .mockResolvedValueOnce(
                  mockRow(
                    [
                      {
                        '1': 'A0001',
                        '2': 'João',
                        '3': '<EMAIL>',
                        '4': 'true',
                        '5': 'true',
                      },
                    ],
                    { totalPages: 6 },
                  ),
                )
                .mockResolvedValue(mockRow([], { totalPages: 6 }));
              const result = await bulkUpsertUserQueryEndpoint(
                app,
                office,
                [
                  {
                    tab_id: 1,
                    column_creditor_attributes: {
                      external_creditor_id: '1',
                      name: '2',
                      email: '3',
                      active: '4',
                      send_invite: '5',
                    },
                  },
                ],
                HttpStatus.OK,
              );

              expect(sheetDbService.getRows).toHaveBeenCalledTimes(10);
              expect(result).toEqual(
                expect.arrayContaining([
                  {
                    external_creditor_id: 'A0001',
                    name: 'João',
                    email: '<EMAIL>',
                    send_invite: true,
                    active: true,
                  },
                ]),
              );
            });

            it('should save request on atributos escritorio', async () => {
              const office = await createTestOffice();

              vi.spyOn(sheetDbService, 'getTabs').mockResolvedValueOnce([mockTab(1)]);
              vi.spyOn(sheetDbService, 'getRows').mockResolvedValue(mockRow([], { totalItems: 2 }));

              const payload = [
                {
                  tab_id: 1,
                  column_creditor_attributes: {
                    external_creditor_id: '1',
                    name: '2',
                    email: '3',
                    active: '4',
                    send_invite: '5',
                  },
                },
              ];

              simulateLoggedUser({ office, claims: [CLAIMS_ADMIN, USER_CREATE] });
              await bulkUpsertUserQueryEndpoint(app, office, payload, HttpStatus.OK);

              const request = await bulkUpsertUserConfigEndpoint(
                app,
                office,
                'user',
                HttpStatus.OK,
              );

              expect(request).toEqual(payload);
            });

            it('should deduplicate users', async () => {
              const office = await createTestOffice();

              vi.spyOn(sheetDbService, 'getTabs').mockResolvedValueOnce([mockTab(1)]);
              vi.spyOn(sheetDbService, 'getRows')
                .mockResolvedValueOnce(
                  mockRow([
                    {
                      '1': 'A0001',
                      '2': 'João',
                      '3': '<EMAIL>',
                      '4': 'true',
                      '5': 'true',
                    },
                    {
                      '1': 'A0001',
                      '2': 'José',
                      '3': '<EMAIL>',
                      '4': 'true',
                      '5': 'true',
                    },
                    {
                      '1': 'A0001',
                      '2': 'Lucas',
                      '3': '<EMAIL>',
                      '4': 'true',
                      '5': 'true',
                    },
                  ]),
                )
                .mockResolvedValue(mockRow([], { totalItems: 2 }));

              const result = await bulkUpsertUserQueryEndpoint(
                app,
                office,
                [
                  {
                    tab_id: 1,
                    column_creditor_attributes: {
                      external_creditor_id: '1',
                      name: '2',
                      email: '3',
                      active: '4',
                      send_invite: '5',
                    },
                  },
                ],
                HttpStatus.OK,
              );

              expect(result.length).toBe(1);
              expect(result).toEqual(
                expect.arrayContaining([
                  {
                    external_creditor_id: 'A0001',
                    name: 'Lucas',
                    email: '<EMAIL>',
                    send_invite: true,
                    active: true,
                  },
                ]),
              );
            });
          });
          describe('and tab is in a commission plan', () => {
            it('should call payout period endpoint', async () => {
              const office = await createTestOffice();

              vi.spyOn(sheetDbService, 'getTabs').mockResolvedValueOnce([mockTab(1, 1)]);
              vi.spyOn(sheetDbService, 'getPayoutPeriods').mockResolvedValueOnce([
                mockPayoutPeriod(1, 1),
              ]);
              vi.spyOn(sheetDbService, 'getRows')
                .mockResolvedValueOnce(
                  mockRow([
                    {
                      '1': 'A0001',
                      '2': 'João',
                      '3': '<EMAIL>',
                      '4': 'true',
                      '5': 'true',
                    },
                    {
                      '1': 'A0002',
                      '2': 'José',
                      '3': '<EMAIL>',
                      '4': 'true',
                      '5': 'true',
                    },
                  ]),
                )
                .mockResolvedValue(mockRow([], { totalItems: 2 }));
              const result = await bulkUpsertUserQueryEndpoint(
                app,
                office,
                [
                  {
                    tab_id: 1,
                    column_creditor_attributes: {
                      external_creditor_id: '1',
                      name: '2',
                      email: '3',
                      active: '4',
                      send_invite: '5',
                    },
                  },
                ],
                HttpStatus.OK,
              );

              expect(sheetDbService.getPayoutPeriods).toHaveBeenCalled();
              expect(result).toEqual(
                expect.arrayContaining([
                  {
                    external_creditor_id: 'A0001',
                    name: 'João',
                    email: '<EMAIL>',
                    send_invite: true,
                    active: true,
                  },
                  {
                    external_creditor_id: 'A0002',
                    name: 'José',
                    email: '<EMAIL>',
                    send_invite: true,
                    active: true,
                  },
                ]),
              );
            });
          });
        });
      });

      describe('User permissions', () => {
        describe('should return user permissions data from tab', () => {
          describe('and tab is not in a commission plan', () => {
            it('should not call payout period endpoint', async () => {
              const office = await createTestOffice();

              vi.spyOn(sheetDbService, 'getTabs').mockResolvedValueOnce([mockTab(1)]);
              vi.spyOn(sheetDbService, 'getRows')
                .mockResolvedValueOnce(
                  mockRow([
                    { '1': 'A0001', '2': 'A0002', '3': 'Time 01', '4': 'Comissionado' },
                    { '1': 'A0002', '2': 'A0001', '3': 'Time 01', '4': 'Comissionado' },
                  ]),
                )
                .mockResolvedValue(mockRow([], { totalItems: 2 }));
              const result = await bulkUpsertUserPermissionQueryEndpoint(
                app,
                office,
                [
                  {
                    tab_id: 1,
                    column_creditor_permissions: {
                      external_creditor_id: '1',
                      type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                      value: '2',
                    },
                  },
                  {
                    tab_id: 1,
                    column_creditor_permissions: {
                      external_creditor_id: '1',
                      type: 'team',
                      value: '3',
                    },
                  },
                  {
                    tab_id: 1,
                    column_creditor_permissions: {
                      external_creditor_id: '1',
                      type: 'profile',
                      value: '4',
                    },
                  },
                ],
                HttpStatus.OK,
              );

              expect(sheetDbService.getPayoutPeriods).not.toHaveBeenCalled();
              expect(result).toEqual(
                expect.arrayContaining([
                  {
                    external_creditor_id: 'A0001',
                    type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                    value: 'A0002',
                  },
                  {
                    external_creditor_id: 'A0001',
                    type: 'team',
                    value: 'Time 01',
                  },
                  {
                    external_creditor_id: 'A0001',
                    type: 'profile',
                    value: 'Comissionado',
                  },
                  {
                    external_creditor_id: 'A0002',
                    type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                    value: 'A0001',
                  },
                  {
                    external_creditor_id: 'A0002',
                    type: 'team',
                    value: 'Time 01',
                  },
                  {
                    external_creditor_id: 'A0002',
                    type: 'profile',
                    value: 'Comissionado',
                  },
                ]),
              );
            });

            it('should save request on atributos escritorio', async () => {
              const office = await createTestOffice();

              vi.spyOn(sheetDbService, 'getTabs').mockResolvedValueOnce([mockTab(1)]);
              vi.spyOn(sheetDbService, 'getRows').mockResolvedValue(mockRow([], { totalItems: 2 }));

              simulateLoggedUser({ office, claims: [CLAIMS_ADMIN, USER_CREATE] });
              await bulkUpsertUserPermissionQueryEndpoint(
                app,
                office,
                [
                  {
                    tab_id: 1,
                    column_creditor_permissions: {
                      external_creditor_id: '1',
                      type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                      value: '2',
                    },
                  },
                ],
                HttpStatus.OK,
              );

              const request = await bulkUpsertUserConfigEndpoint(
                app,
                office,
                'permissions',
                HttpStatus.OK,
              );

              expect(request).toEqual([
                {
                  tab_id: 1,
                  column_creditor_permissions: {
                    external_creditor_id: '1',
                    type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                    value: '2',
                  },
                },
              ]);
            });
          });
          describe('and tab is in a commission plan', () => {
            it('should call payout period endpoint', async () => {
              const office = await createTestOffice();

              vi.spyOn(sheetDbService, 'getTabs').mockResolvedValueOnce([mockTab(1, 1)]);
              vi.spyOn(sheetDbService, 'getPayoutPeriods').mockResolvedValueOnce([
                mockPayoutPeriod(1, 1),
              ]);
              vi.spyOn(sheetDbService, 'getRows')
                .mockResolvedValueOnce(
                  mockRow([
                    { '1': 'A0001', '2': 'A0002', '3': 'Time 01', '4': 'Comissionado' },
                    { '1': 'A0002', '2': 'A0001', '3': 'Time 01', '4': 'Comissionado' },
                  ]),
                )
                .mockResolvedValue(mockRow([], { totalItems: 2 }));
              const result = await bulkUpsertUserPermissionQueryEndpoint(
                app,
                office,
                [
                  {
                    tab_id: 1,
                    column_creditor_permissions: {
                      external_creditor_id: '1',
                      type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                      value: '2',
                    },
                  },
                  {
                    tab_id: 1,
                    column_creditor_permissions: {
                      external_creditor_id: '1',
                      type: 'team',
                      value: '3',
                    },
                  },
                  {
                    tab_id: 1,
                    column_creditor_permissions: {
                      external_creditor_id: '1',
                      type: 'profile',
                      value: '4',
                    },
                  },
                ],
                HttpStatus.OK,
              );

              expect(sheetDbService.getPayoutPeriods).toHaveBeenCalled();
              expect(result).toEqual(
                expect.arrayContaining([
                  {
                    external_creditor_id: 'A0001',
                    type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                    value: 'A0002',
                  },
                  {
                    external_creditor_id: 'A0001',
                    type: 'team',
                    value: 'Time 01',
                  },
                  {
                    external_creditor_id: 'A0001',
                    type: 'profile',
                    value: 'Comissionado',
                  },
                  {
                    external_creditor_id: 'A0002',
                    type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                    value: 'A0001',
                  },
                  {
                    external_creditor_id: 'A0002',
                    type: 'team',
                    value: 'Time 01',
                  },
                  {
                    external_creditor_id: 'A0002',
                    type: 'profile',
                    value: 'Comissionado',
                  },
                ]),
              );
            });
          });
        });
      });
    });

    describe('Check', () => {
      describe('Users', () => {
        it('should return action CREATE when user does not exists', async () => {
          const office = await createTestOffice();
          const result = await bulkUpsertUserCheckEndpoint(
            app,
            office,
            [
              {
                external_creditor_id: 'TESTE 789',
                name: 'teste 789',
                email: '<EMAIL>',
                active: true,
              },
            ],
            HttpStatus.OK,
          );
          expect(result[0].action).toEqual('CREATE');
        });

        it('should return action UPDATE when user already exists and some property is different', async () => {
          const office = await createTestOffice();
          simulateLoggedUser({ office, claims: loggedUserClaims });

          mockCognitoReturnSuccess();
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: 'A0001',
              name: 'João',
              email: `joao-${office.id_escritorio}@joao.com.br`,
              reset_password: false,
            },
            HttpStatus.CREATED,
          );

          const result = await bulkUpsertUserCheckEndpoint(
            app,
            office,
            [
              {
                external_creditor_id: 'A0001',
                name: 'José',
                email: '<EMAIL>',
                active: true,
              },
            ],
            HttpStatus.OK,
          );

          expect(result[0].action).toEqual('UPDATE');
        });

        it('should return action NO_CHANGES when user already exists and all properties are equal', async () => {
          const office = await createTestOffice();
          simulateLoggedUser({ office, claims: loggedUserClaims });

          mockCognitoReturnSuccess();
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: 'A0001',
              name: 'João',
              email: `joao-${office.id_escritorio}@joao.com.br`,
              reset_password: false,
            },
            HttpStatus.CREATED,
          );

          const result = await bulkUpsertUserCheckEndpoint(
            app,
            office,
            [
              {
                external_creditor_id: 'A0001',
                name: 'Joãozin',
                email: `joao-${office.id_escritorio}@joao.com.br`,
                active: true,
              },
            ],
            HttpStatus.OK,
            {
              name: false,
              email: true,
              active: true,
            },
          );

          expect(result[0].action).toEqual('NO_CHANGES');
        });
      });

      describe('User permissions', () => {
        it('should return action CREATE when permission does not exists for the given creditor', async () => {
          const office = await createTestOffice();
          simulateLoggedUser({ office, claims: loggedUserClaims });

          mockCognitoReturnSuccess();
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: 'A0001',
              name: 'João',
              email: `joao-${office.id_escritorio}@joao.com.br`,
              reset_password: false,
            },
            HttpStatus.CREATED,
          );

          const result = await bulkUpsertUserPermissionCheckEndpoint(
            app,
            office,
            [
              {
                external_creditor_id: 'A0001',
                type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                value: 'A0001',
              },
              {
                external_creditor_id: 'A0001',
                type: 'team',
                value: 'Time 01',
              },
              {
                external_creditor_id: 'A0001',
                type: 'profile',
                value: 'Comissionado',
              },
            ],
            HttpStatus.OK,
          );

          result.forEach(element => {
            expect(element.action).toEqual('CREATE');
          });
        });

        it('should return action NO_CHANGES when permission already exists for the given creditor', async () => {
          const office = await createTestOffice();
          simulateLoggedUser({ office, claims: loggedUserClaims });

          mockCognitoReturnSuccess();
          await createUserEndpoint(
            app,
            office,
            {
              external_creditor_id: 'A0001',
              name: 'João',
              email: `joao-${office.id_escritorio}@joao.com.br`,
              reset_password: false,
            },
            HttpStatus.CREATED,
          );

          const [credor, team, profile] = await Promise.all([
            Credor.findOne({
              where: {
                escritorio_id_escritorio: office.id_escritorio,
                id_credor_externo: 'A0001',
              },
            }),
            Teams.create({
              office_id: office.id_escritorio,
              name: 'Time 01',
            }),
            Permission_Profile.create({
              office_id: office.id_escritorio,
              name: 'Comissionado',
              permission_data: [],
              priority: 0,
            }),
          ]);

          await Promise.all([
            Atributos_credor.create({
              credor_id_credor: credor.id_credor,
              atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
              valor: JSON.stringify(['A0002']),
            }),
            Creditor_Teams.create({
              creditor_id: credor.id_credor,
              team_id: team.id,
            }),
            User_Permission.create({
              permission_profile_id: profile.id,
              creditor_id: credor.id_credor,
            }),
          ]);

          const result = await bulkUpsertUserPermissionCheckEndpoint(
            app,
            office,
            [
              {
                external_creditor_id: 'A0001',
                type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                value: 'A0002',
              },
              {
                external_creditor_id: 'A0001',
                type: 'team',
                value: 'Time 01',
              },
              {
                external_creditor_id: 'A0001',
                type: 'profile',
                value: 'Comissionado',
              },
            ],
            HttpStatus.OK,
          );

          result.forEach(element => {
            expect(element.action).toEqual('NO_CHANGES');
          });
        });

        it('should return USER_NOT_EXISTS action when a given creditor does not exist', async () => {
          const office = await createTestOffice();
          simulateLoggedUser({ office, claims: loggedUserClaims });

          const result = await bulkUpsertUserPermissionCheckEndpoint(
            app,
            office,
            [
              {
                external_creditor_id: 'INVALID_CREDITOR_ID_A',
                type: UsersClaims.CREDITORS_VIEW_PERMISSION,
                value: 'A0002',
              },
              {
                external_creditor_id: 'INVALID_CREDITOR_ID_B',
                type: 'team',
                value: 'Time 01',
              },
              {
                external_creditor_id: 'INVALID_CREDITOR_ID_C',
                type: 'profile',
                value: 'Comissionado',
              },
            ],
            HttpStatus.OK,
          );

          result.forEach(element => {
            expect(element.action).toEqual('USER_NOT_EXISTS');
          });
        });
      });
    });
  });

  describe('User Management', () => {
    it('Should throw error if receiving a invalid body or duplicated user_id', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: loggedUserClaims });

      mockCognitoReturnSuccess();
      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: 'A0001',
          name: 'João',
          email: `joao-${office.id_escritorio}@enterprise.com.br`,
          reset_password: false,
        },
        HttpStatus.CREATED,
      );

      await userManagementUpsertDiffEndpoint(
        app,
        office,
        {
          rows: [
            {
              // user_id: 'A0001',
              user_name: 'João',
              email: `joao-${office.id_escritorio}@enterprise.com.br`,
              active: 'TRUE',
              people_can_see: '',
              profile: '',
              team: '',
              team_can_see: '',
            } as any, // Where is the user_id?
          ],
        },
        HttpStatus.BAD_REQUEST,
      );

      await userManagementUpsertDiffEndpoint(
        app,
        office,
        {
          rows: [
            {
              user_id: 'A0001',
              user_name: 'João',
              email: `tubérculo`, // invalid email
              active: 'TRUE',
              people_can_see: '',
              profile: '',
              team: '',
              team_can_see: '',
            },
          ],
        },
        HttpStatus.BAD_REQUEST,
      );

      await userManagementUpsertDiffEndpoint(
        app,
        office,
        {
          rows: [
            {
              user_id: 'A0001',
              user_name: 'João',
              email: `joao-${office.id_escritorio}@enterprise.com.br`,
              active: 'TRUE',
              people_can_see: '',
              profile: '',
              team: '',
              team_can_see: '',
            },
            {
              user_id: 'A0001', // Again?
              user_name: 'Joseph Seed',
              email: `joseph-${office.id_escritorio}@far.cry.br`,
              active: 'FALSE',
              people_can_see: '',
              profile: '',
              team: '',
              team_can_see: '',
            },
          ],
        },
        HttpStatus.BAD_REQUEST,
      );
    });

    it('Should return correctly the diff for new user', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: loggedUserClaims });

      mockCognitoReturnSuccess();
      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: 'A0001',
          name: 'João',
          email: `joao-${office.id_escritorio}@enterprise.com.br`,
          reset_password: false,
        },
        HttpStatus.CREATED,
      );

      const result = await userManagementUpsertDiffEndpoint(
        app,
        office,
        {
          rows: [
            {
              user_id: 'A0001',
              user_name: 'João',
              email: `joao-${office.id_escritorio}@enterprise.com.br`,
              active: 'TRUE',
              people_can_see: '',
              profile: '',
              team: '',
              team_can_see: '',
            },
            {
              user_id: 'A0002',
              user_name: 'José',
              email: `jose-${office.id_escritorio}@enterprise.com.br`,
              active: 'TRUE',
              people_can_see: '',
              profile: '',
              team: '',
              team_can_see: '',
            },
          ],
        },
        HttpStatus.OK,
      );

      expect(result).toStrictEqual({
        rows: [
          {
            user_id: 'A0001',
            status: UserManagementDiffStatus.NOT_MODIFIED,
            active: { current: 'TRUE' },
            email: { current: `joao-${office.id_escritorio}@enterprise.com.br` },
            people_can_see: { current: '' },
            profile: { current: '' },
            team: { current: '' },
            team_can_see: { current: '' },
            user_name: { current: 'João' },
          },
          {
            user_id: 'A0002',
            status: UserManagementDiffStatus.NEW,
            active: { new: 'TRUE' },
            email: { new: `jose-${office.id_escritorio}@enterprise.com.br` },
            people_can_see: { new: '' },
            profile: { new: '' },
            team: { new: '' },
            team_can_see: { new: '' },
            user_name: { new: 'José' },
          },
        ],
      });
    });

    it('Should return correctly the diff for team and profile, maintaning old values', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: loggedUserClaims });

      const profile = await Permission_Profile.create({
        office_id: office.id_escritorio,
        name: 'Comissionado',
        permission_data: [],
        priority: 0,
      });

      mockCognitoReturnSuccess();
      const credor = (
        await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: 'A0001',
            name: 'João',
            email: `joao-${office.id_escritorio}@enterprise.com.br`,
            reset_password: false,
            teams: ['Time 01'],
          },
          HttpStatus.CREATED,
        )
      ).body as Credor;

      await User_Permission.create({
        permission_profile_id: profile.id,
        creditor_id: credor.id_credor,
      });

      const result = await userManagementUpsertDiffEndpoint(
        app,
        office,
        {
          rows: [
            {
              user_id: 'A0001',
              user_name: 'Joãozinho',
              email: `joao-${office.id_escritorio}@enterprise.com.br`,
              active: 'FALSE',
              people_can_see: '',
              profile: 'Comissionado',
              team: 'Time 02',
              team_can_see: '',
            },
          ],
        },
        HttpStatus.OK,
      );

      expect(result).toStrictEqual({
        rows: [
          {
            user_id: 'A0001',
            status: UserManagementDiffStatus.MODIFIED,
            user_name: { current: 'João', new: 'Joãozinho' },
            active: { current: 'TRUE', new: 'FALSE' },
            email: { current: `joao-${office.id_escritorio}@enterprise.com.br` },
            people_can_see: { current: '' },
            profile: { current: 'Comissionado' },
            team: { current: 'Time 01', new: 'Time 02' },
            team_can_see: { current: '' },
          },
        ],
      });
    });

    it('Should return correctly the diff for the claims', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: loggedUserClaims });

      mockCognitoReturnSuccess();
      const credor = (
        await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: 'A0001',
            name: 'João',
            email: `joao-${office.id_escritorio}@enterprise.com.br`,
            reset_password: false,
            teams: ['Time 01'],
          },
          HttpStatus.CREATED,
        )
      ).body as Credor;

      await Credor.bulkCreate([
        {
          id_credor_externo: 'A0002',
          escritorio_id_escritorio: office.id_escritorio,
          nome_credor: 'Pedro',
          email: `pedro-${office.id_escritorio}@enterprise.com.br`,
        },
        {
          id_credor_externo: 'A0003',
          escritorio_id_escritorio: office.id_escritorio,
          nome_credor: 'Marcela',
          email: `marcela-${office.id_escritorio}@enterprise.com.br`,
        },
      ]);

      await Atributos_credor.create({
        credor_id_credor: credor.id_credor,
        atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
        valor: JSON.stringify(['A0002', 'A0003']),
      });
      await Atributos_credor.create({
        credor_id_credor: credor.id_credor,
        atributo: UsersClaims.TEAMS_VIEW_PERMISSION,
        valor: JSON.stringify(['Time 02']),
      });

      const result = await userManagementUpsertDiffEndpoint(
        app,
        office,
        {
          rows: [
            {
              user_id: 'A0001',
              user_name: 'Joãozinho',
              email: `joao-${office.id_escritorio}@enterprise.com.br`,
              active: 'TRUE',
              people_can_see: 'A0002',
              profile: '',
              team: 'Time 01',
              team_can_see: '',
            },
          ],
        },
        HttpStatus.OK,
      );

      expect(result).toStrictEqual({
        rows: [
          {
            user_id: 'A0001',
            status: UserManagementDiffStatus.MODIFIED,
            user_name: { current: 'João', new: 'Joãozinho' },
            active: { current: 'TRUE' },
            email: { current: `joao-${office.id_escritorio}@enterprise.com.br` },
            people_can_see: { current: 'A0002,A0003', new: 'A0002' },
            profile: { current: '' },
            team: { current: 'Time 01' },
            team_can_see: { current: 'Time 02', new: '' },
          },
        ],
      });
    });

    it('Should return correctly when there are changes on the order/format but not on the values', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: loggedUserClaims });

      const profile = await Permission_Profile.create({
        office_id: office.id_escritorio,
        name: 'profile',
        permission_data: [],
        priority: 0,
      });

      mockCognitoReturnSuccess();
      const credor = (
        await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: 'A0001',
            name: 'João',
            email: `joao-${office.id_escritorio}@enterprise.com.br`,
            reset_password: false,
            teams: ['T01', 'T02'],
          },
          HttpStatus.CREATED,
        )
      ).body as Credor;

      await Credor.bulkCreate([
        {
          id_credor_externo: 'A0002',
          escritorio_id_escritorio: office.id_escritorio,
          nome_credor: 'Pedro',
          email: `pedro-${office.id_escritorio}@enterprise.com.br`,
        },
        {
          id_credor_externo: 'A0003',
          escritorio_id_escritorio: office.id_escritorio,
          nome_credor: 'Marcela',
          email: `marcela-${office.id_escritorio}@enterprise.com.br`,
        },
      ]);

      await User_Permission.create({
        permission_profile_id: profile.id,
        creditor_id: credor.id_credor,
      });

      await Atributos_credor.create({
        credor_id_credor: credor.id_credor,
        atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
        valor: JSON.stringify(['A0002', 'A0003']),
      });
      await Atributos_credor.create({
        credor_id_credor: credor.id_credor,
        atributo: UsersClaims.TEAMS_VIEW_PERMISSION,
        valor: JSON.stringify(['T02', 'T01']),
      });

      const result = await userManagementUpsertDiffEndpoint(
        app,
        office,
        {
          rows: [
            {
              user_id: 'A0001',
              user_name: 'João',
              email: `joao-${office.id_escritorio}@enterprise.com.br`,
              active: '1',
              people_can_see: 'A0003,A0002',
              profile: 'profile',
              team: 'T02,T01',
              team_can_see: 'T01,T02',
            },
          ],
        },
        HttpStatus.OK,
      );

      expect(result).toStrictEqual({
        rows: [
          {
            user_id: 'A0001',
            status: UserManagementDiffStatus.NOT_MODIFIED,
            user_name: { current: 'João' },
            active: { current: 'TRUE' },
            email: { current: `joao-${office.id_escritorio}@enterprise.com.br` },
            people_can_see: { current: 'A0002,A0003' },
            profile: { current: 'profile' },
            team: { current: 'T01,T02' },
            team_can_see: { current: 'T02,T01' },
          },
        ],
      });
    });

    it('Should ignore unexistent teams, users and profiles', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: loggedUserClaims });

      mockCognitoReturnSuccess();
      await createUserEndpoint(
        app,
        office,
        {
          external_creditor_id: 'A0001',
          name: 'João',
          email: `joao-${office.id_escritorio}@enterprise.com.br`,
          reset_password: false,
        },
        HttpStatus.CREATED,
      );

      const result = await userManagementUpsertDiffEndpoint(
        app,
        office,
        {
          rows: [
            {
              user_id: 'A0001',
              user_name: 'João',
              email: `joao-${office.id_escritorio}@enterprise.com.br`,
              active: 'TRUE',
              people_can_see: 'whoami1,whoami2',
              profile: 'whoami',
              team: '',
              team_can_see: 'whoami,whoami',
            },
          ],
        },
        HttpStatus.OK,
      );

      expect(result).toStrictEqual({
        rows: [
          {
            user_id: 'A0001',
            status: UserManagementDiffStatus.NOT_MODIFIED,
            active: { current: 'TRUE' },
            email: { current: `joao-${office.id_escritorio}@enterprise.com.br` },
            people_can_see: { current: '' },
            profile: { current: '' },
            team: { current: '' },
            team_can_see: { current: '' },
            user_name: { current: 'João' },
          },
        ],
      });
    });

    it('Should consider fieldsToUpdate when making the diff', async () => {
      const office = await createTestOffice();
      simulateLoggedUser({ office, claims: loggedUserClaims });

      mockCognitoReturnSuccess();
      const credor = (
        await createUserEndpoint(
          app,
          office,
          {
            external_creditor_id: 'A0001',
            name: 'João',
            email: `joao-${office.id_escritorio}@enterprise.com.br`,
            reset_password: false,
            teams: ['Time 01'],
          },
          HttpStatus.CREATED,
        )
      ).body as Credor;

      await Atributos_credor.create({
        credor_id_credor: credor.id_credor,
        atributo: UsersClaims.CREDITORS_VIEW_PERMISSION,
        valor: JSON.stringify(['A0002', 'A0003']),
      });
      await Atributos_credor.create({
        credor_id_credor: credor.id_credor,
        atributo: UsersClaims.TEAMS_VIEW_PERMISSION,
        valor: JSON.stringify(['Time 02']),
      });

      const result = await userManagementUpsertDiffEndpoint(
        app,
        office,
        {
          rows: [
            {
              user_id: 'A0001',
              user_name: 'Gustavo',
              email: `gustavo-${office.id_escritorio}@enterprise.com.br`,
              active: '',
              people_can_see: '',
              profile: '',
              team: '',
              team_can_see: '',
            },
          ],
          fields_to_update: {
            user_name: true,
            email: true,
            active: false,
            people_can_see: false,
            profile: false,
            team: false,
            team_can_see: false,
          },
        },
        HttpStatus.OK,
      );

      expect(result).toStrictEqual({
        rows: [
          {
            user_id: 'A0001',
            status: UserManagementDiffStatus.MODIFIED,
            active: { current: 'TRUE' },
            user_name: { current: 'João', new: 'Gustavo' },
            email: {
              current: `joao-${office.id_escritorio}@enterprise.com.br`,
              new: `gustavo-${office.id_escritorio}@enterprise.com.br`,
            },
            people_can_see: { current: 'A0002,A0003' },
            profile: { current: '' },
            team: { current: 'Time 01' },
            team_can_see: { current: 'Time 02' },
          },
        ],
      });
    });

    describe('Auto Process', () => {
      it('should not call /rows if there are no dates', async () => {
        const office = await createTestOffice();
        simulateLoggedUser({ office, claims: loggedUserClaims });
        mockCognitoReturnSuccessAlways();
        sheetDbService.sendRequest = vi.fn().mockResolvedValueOnce({
          data: {
            dates: [],
          },
        } as GaxiosResponse<GetUserManagementDateResponse>);

        await autoProcessEndpoint(app, office);

        expect(sheetDbService.sendRequest).toHaveBeenCalledTimes(1);
        expect(sheetDbService.sendRequest).toHaveBeenNthCalledWith(
          1,
          office.client_id,
          'user_management/dates',
          null,
          'GET',
          true,
        );
      });

      it('should not call getUserManagementDiff if there are no rows', async () => {
        const office = await createTestOffice();
        simulateLoggedUser({ office, claims: loggedUserClaims });
        mockCognitoReturnSuccessAlways();
        const dates = [{ date: '2023-01-01' }];

        sheetDbService.sendRequest = vi
          .fn()
          .mockResolvedValueOnce({ data: { dates } })
          .mockResolvedValueOnce({ data: { rows: [] } });

        const getUserManagementDiffSpy = vi.spyOn(userDomain, 'getUserManagementDiff');

        await autoProcessEndpoint(app, office);
        expect(sheetDbService.sendRequest).toHaveBeenCalledTimes(2);
        expect(sheetDbService.sendRequest).toHaveBeenNthCalledWith(
          2,
          office.client_id,
          'user_management/rows',
          null,
          'GET',
          true,
          {
            date: '2023-01-01',
            only_locked_periods: false,
          },
        );
        expect(getUserManagementDiffSpy).not.toHaveBeenCalled(); // No diff should be processed if no rows are returned
      });

      it('should not call upsertUserManagementRows if no diff is found', async () => {
        const office = await createTestOffice();
        simulateLoggedUser({ office, claims: loggedUserClaims });
        mockCognitoReturnSuccessAlways();
        const rows = [
          {
            // user with modifications
            email: `joao-${office.id_escritorio}@enterprise.com.br`,
            user_id: 'A0001',
            user_name: 'João',
            active: 'true',
            team: '',
            profile: '',
            people_can_see: '',
            team_can_see: '',
          },
        ];

        await Credor.create({
          id_credor_externo: 'A0001',
          escritorio_id_escritorio: office.id_escritorio,
          nome_credor: 'João',
          email: `joao-${office.id_escritorio}@enterprise.com.br`,
        });

        sheetDbService.sendRequest = vi
          .fn()
          .mockResolvedValueOnce({
            data: {
              dates: [{ date: '2022-01-01' }],
            },
          } as GaxiosResponse<GetUserManagementDateResponse>)
          .mockResolvedValueOnce({
            data: {
              rows,
            },
          } as GaxiosResponse<GetUserManagementDateRows>);

        const getUserManagementDiffSpy = vi.spyOn(userDomain, 'getUserManagementDiff');
        const upsertUserManagementRowsSpy = vi.spyOn(userDomain, 'upsertUserManagementRows');

        await autoProcessEndpoint(app, office);

        expect(sheetDbService.sendRequest).toHaveBeenCalledTimes(2);
        expect(sheetDbService.sendRequest).toHaveBeenNthCalledWith(
          2,
          office.client_id,
          'user_management/rows',
          null,
          'GET',
          true,
          {
            date: '2022-01-01',
            only_locked_periods: false,
          },
        );
        expect(getUserManagementDiffSpy).toHaveBeenCalledWith(
          office.id_escritorio,
          expect.objectContaining({
            rows,
          }),
          expect.anything(), // user
        );
        expect(upsertUserManagementRowsSpy).not.toHaveBeenCalled(); // upsert should not be called if no diff is found
      });

      it('should process correctly last row date', async () => {
        const office = await createTestOffice();
        simulateLoggedUser({ office, claims: loggedUserClaims });
        mockCognitoReturnSuccessAlways();
        const rows = [
          {
            // user with modifications
            email: '<EMAIL>',
            user_id: 'A0001',
            user_name: 'João',
            active: 'true',
            team: '',
            profile: '',
            people_can_see: '',
            team_can_see: '',
          },
          {
            // user with no modifications
            email: `maria-${office.id_escritorio}@enterprise.com.br`,
            user_id: 'A0002',
            user_name: 'Maria',
            active: 'true',
            team: '',
            profile: '',
            people_can_see: '',
            team_can_see: '',
          },
          {
            // user that no exists on the office
            email: `pedro-${office.id_escritorio}@enterprise.com.br`,
            user_id: 'A0003',
            user_name: 'Pedro',
            active: 'true',
            team: '',
            profile: '',
            people_can_see: '',
            team_can_see: '',
          },
        ];

        await Credor.bulkCreate([
          {
            id_credor_externo: 'A0001',
            escritorio_id_escritorio: office.id_escritorio,
            nome_credor: 'João',
            email: `joao-${office.id_escritorio}@enterprise.com.br`,
          },
          {
            id_credor_externo: 'A0002',
            escritorio_id_escritorio: office.id_escritorio,
            nome_credor: 'Maria',
            email: `maria-${office.id_escritorio}@enterprise.com.br`,
          },
        ]);

        sheetDbService.sendRequest = vi
          .fn()
          .mockResolvedValueOnce({
            data: {
              dates: [
                {
                  date: '2022-01-01',
                },
                {
                  date: '2022-01-02',
                },
              ],
            },
          } as GaxiosResponse<GetUserManagementDateResponse>)
          .mockResolvedValueOnce({
            data: {
              rows,
            },
          } as GaxiosResponse<GetUserManagementDateRows>);

        const getUserManagementDiffSpy = vi.spyOn(userDomain, 'getUserManagementDiff');
        const upsertUserManagementRowsSpy = vi.spyOn(userDomain, 'upsertUserManagementRows');

        await autoProcessEndpoint(app, office);
        expect(sheetDbService.sendRequest).toHaveBeenCalledTimes(2);
        expect(sheetDbService.sendRequest).toHaveBeenNthCalledWith(
          2,
          office.client_id,
          'user_management/rows',
          null,
          'GET',
          true,
          {
            date: '2022-01-02',
            only_locked_periods: false,
          },
        );

        expect(getUserManagementDiffSpy).toBeCalledWith(
          office.id_escritorio,
          expect.objectContaining({
            rows,
          }),
          expect.anything(), // user
        );
        expect(upsertUserManagementRowsSpy).toBeCalledWith(
          office.id_escritorio,
          expect.objectContaining({
            rows: [
              { ...rows[0], active: 'TRUE' }, // João with modifications
              { ...rows[2], active: 'TRUE' }, // Pedro a no existing record should be created
            ],
          }),
          expect.anything(), // user
        );
      });
    });
  });
  afterAll(async () => {
    await Promise.resolve();
    await app.close();
    await app.get('SEQUELIZE').close();
  });
});
