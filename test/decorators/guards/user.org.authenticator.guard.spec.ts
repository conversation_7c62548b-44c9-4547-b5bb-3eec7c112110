import { ConfigurationEnv } from '@app/config/configuration.env';
import { ExecutionContext, ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { LoginDomain } from '../../../src/domain/login.domain';
import { SharedMethodsAuthorization } from '@app/decorators/guards/shared.methods.authenticator';
import jsonwebtoken from 'jsonwebtoken';
import { Oidc_Provider } from '@app/models/oidc_provider';
import { User } from '@app/models/user';
import { OidcProviderDomain } from '@app/domain/oidc.domain';
import { RouteMetrics } from '@app/prometheus.module';
import { vitest } from 'vitest';
import { TenantContextAuthenticatorGuard } from '@app/decorators/guards/tenant.context.authenticator.guard';
import { GuardErrors } from '@app/decorators/guards/types/error.codes';
import { Escritorio } from '@app/models/escritorio';
import { Request } from 'express';
import { OrgContext } from '@app/auth.contexts/tenant/org.context';
vitest.mock('starkbank');
vitest.mock('jwk-to-pem');
vitest.mock('@app/prometheus.module');

const met = new RouteMetrics();

function makeSut(jwtBody?: object) {
  const validJwt = jsonwebtoken.sign(
    { iss: 'http://localhost.splitc.com.br', client_id: 'invalid client_id', ...jwtBody },
    'shh',
    { keyid: 'valid_kid' },
  );
  const config = new ConfigurationEnv();
  const shared = new SharedMethodsAuthorization(config);
  const loginDomain = {
    loadAuthenticationContext: vitest.fn(),
  } as unknown as LoginDomain;
  const oidcProviderDomain = {
    updatedJwks: vitest.fn(),
  } as unknown as OidcProviderDomain;

  Object.defineProperty(config, 'tokenValidationEnabled', { value: true });
  Object.defineProperty(config, 'jwtIssuer', { value: 'shh' });

  const sut = new TenantContextAuthenticatorGuard(met, config);

  return {
    config,
    shared,
    sut,
    loginDomain,
    validJwt,
    oidcProviderDomain,
  };
}

function makeContext({ getRequestResponse }: { getRequestResponse: unknown }): ExecutionContext {
  return {
    switchToHttp() {
      return {
        getRequest() {
          return getRequestResponse;
        },
      };
    },
  } as ExecutionContext;
}

describe('User Org authenticator guard', () => {
  beforeEach(() => {
    vitest.clearAllMocks();
  });

  const getDefaultOidcMock = () => {
    return {
      checkIfIsServiceAccount: vitest.fn().mockReturnValue(false),
      default: false,
      getUserInfo: vitest.fn().mockReturnValue({ email: '<EMAIL>' }),
      checkIfCustomerServiceAccount: vitest.fn().mockReturnValue(false),
      org_id: 1,
    } as unknown as Oidc_Provider;
  };

  it('should throw an UnauthorizedException when !req.jwt', async () => {
    const { sut, validJwt } = makeSut();

    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt }, oidc: {} },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.REQ_JWT_OR_ID_NOT_FOUND);
    }
  });

  it('should throw an UnauthorizedException when !req.oidc', async () => {
    const { sut, validJwt } = makeSut();

    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt }, jwt: validJwt },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.REQ_JWT_OR_ID_NOT_FOUND);
    }
  });

  it('should throw an UnauthorizedException when cannot extract provider_id from jwt', async () => {
    const { sut, validJwt } = makeSut();

    const mockOidcInstance = {
      ...getDefaultOidcMock(),
      getUserInfo: vitest.fn().mockReturnValue({}),
    } as unknown as Oidc_Provider;

    const context = makeContext({
      getRequestResponse: {
        headers: { authorization: 'Bearer ' + validJwt },
        jwt: validJwt,
        oidc: mockOidcInstance,
      },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.MISSING_PROVIDER_ID);
    }
  });

  it('should throw an UnauthorizedException when user is not found', async () => {
    const { sut, validJwt } = makeSut();

    const mockOidcInstance = {
      ...getDefaultOidcMock(),
      getUserInfo: vitest.fn().mockReturnValue({ provider_id: 'any_provider_id' }),
    } as unknown as Oidc_Provider;
    vitest.spyOn(User, 'getUserByOidc').mockResolvedValueOnce(undefined);

    const context = makeContext({
      getRequestResponse: {
        headers: { authorization: 'Bearer ' + validJwt },
        jwt: validJwt,
        oidc: mockOidcInstance,
      },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.USER_NOT_FOUND);
    }
  });

  it('should true when org hasnt any tenant, allowedTenants should return empty array', async () => {
    const { sut, validJwt } = makeSut();

    const mockOidcInstance = {
      ...getDefaultOidcMock(),
      getUserInfo: vitest.fn().mockReturnValue({ provider_id: 'any_provider_id' }),
    } as unknown as Oidc_Provider;

    const mockUserInstance = { org_id: 1, is_admin: true } as unknown as User;
    vitest.spyOn(User, 'getUserByOidc').mockResolvedValueOnce(mockUserInstance);
    vitest.spyOn(Escritorio, 'findAll').mockResolvedValueOnce([]);

    const context = makeContext({
      getRequestResponse: {
        headers: { authorization: 'Bearer ' + validJwt },
        jwt: validJwt,
        oidc: mockOidcInstance,
      },
    });
    const res = await sut.canActivate(context);
    expect(res).toBeTruthy();
    const req = context.switchToHttp().getRequest<Request>();
    expect(req.tenants_ctx?.allowedTenants).toHaveLength(0);
    expect(req.tenants_ctx.getSelectedCreditor()).toBeUndefined();
    expect(req.tenants_ctx instanceof OrgContext).toBeTruthy();
  });
});
