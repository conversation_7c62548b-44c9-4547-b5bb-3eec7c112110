import { ConfigurationEnv } from '@app/config/configuration.env';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { LoginDomain } from '../../../src/domain/login.domain';
import { SharedMethodsAuthorization } from '@app/decorators/guards/shared.methods.authenticator';
import jsonwebtoken from 'jsonwebtoken';
import { Oidc_Provider } from '@app/models/oidc_provider';
import { OidcProviderDomain } from '@app/domain/oidc.domain';
import { RouteMetrics } from '@app/prometheus.module';
import { vitest } from 'vitest';
import { GuardErrors } from '@app/decorators/guards/types/error.codes';
import { Request } from 'express';
import { IMPERSONATE_OFFICE_TENANT_HEADER } from 'shared-types';
import { Credor } from '@app/models/credor';
import { TenantContextAuthenticatorGuard } from '@app/decorators/guards/tenant.context.authenticator.guard';
import { UserDefaultContext } from '@app/auth.contexts/tenant/user.default.context';
vitest.mock('starkbank');
vitest.mock('jwk-to-pem');
vitest.mock('@app/prometheus.module');

const met = new RouteMetrics();

function makeSut(jwtBody?: object) {
  const validJwt = jsonwebtoken.sign(
    { iss: 'http://localhost.splitc.com.br', client_id: 'invalid client_id', ...jwtBody },
    'shh',
    { keyid: 'valid_kid' },
  );
  const config = new ConfigurationEnv();
  const shared = new SharedMethodsAuthorization(config);
  const loginDomain = {
    loadAuthenticationContext: vitest.fn(),
  } as unknown as LoginDomain;
  const oidcProviderDomain = {
    updatedJwks: vitest.fn(),
  } as unknown as OidcProviderDomain;

  Object.defineProperty(config, 'tokenValidationEnabled', { value: true });
  Object.defineProperty(config, 'jwtIssuer', { value: 'shh' });

  const sut = new TenantContextAuthenticatorGuard(met, config);

  return {
    config,
    shared,
    sut,
    loginDomain,
    validJwt,
    oidcProviderDomain,
  };
}

function makeContext({ getRequestResponse }: { getRequestResponse: unknown }): ExecutionContext {
  return {
    switchToHttp() {
      return {
        getRequest() {
          return getRequestResponse;
        },
      };
    },
  } as ExecutionContext;
}

describe('User Default authenticator guard', () => {
  beforeEach(() => {
    vitest.clearAllMocks();
  });

  const getDefaultOidcMock = () => {
    return {
      checkIfIsServiceAccount: vitest.fn().mockReturnValue(false),
      default: true,
      getUserInfo: vitest.fn().mockReturnValue({ email: '<EMAIL>' }),
      checkIfCustomerServiceAccount: vitest.fn().mockReturnValue(false),
    } as unknown as Oidc_Provider;
  };

  it('should throw an UnauthorizedException when !req.jwt', async () => {
    const { sut, validJwt } = makeSut();
    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt }, oidc: {} },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.REQ_JWT_OR_ID_NOT_FOUND);
    }
  });

  it('should throw an UnauthorizedException when !req.oidc', async () => {
    const { sut, validJwt } = makeSut();

    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt }, jwt: validJwt },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.REQ_JWT_OR_ID_NOT_FOUND);
    }
  });

  it('should throw an UnauthorizedException when cannot extract email from jwt', async () => {
    const { sut, validJwt } = makeSut();

    const mockOidcInstance = {
      ...getDefaultOidcMock(),
      getUserInfo: vitest.fn().mockReturnValue({}),
    } as unknown as Oidc_Provider;

    const context = makeContext({
      getRequestResponse: {
        headers: { authorization: 'Bearer ' + validJwt },
        jwt: validJwt,
        oidc: mockOidcInstance,
      },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.EMAIL_IS_REQUIRED_FOR_LOGIN_DEFAULT);
    }
  });

  it('should true when hasnt any tenant, allowedTenants should return empty array', async () => {
    const { sut, validJwt } = makeSut();

    const mockOidcInstance = getDefaultOidcMock();

    vitest.spyOn(Credor, 'findAll').mockResolvedValueOnce([]);

    const context = makeContext({
      getRequestResponse: {
        headers: { authorization: 'Bearer ' + validJwt },
        jwt: validJwt,
        oidc: mockOidcInstance,
      },
    });
    const res = await sut.canActivate(context);
    expect(res).toBeTruthy();
    const req = context.switchToHttp().getRequest<Request>();
    expect(req.tenants_ctx?.allowedTenants).toHaveLength(0);
    expect(req.tenants_ctx.getSelectedCreditor()).toBeUndefined();
  });

  it('should true when has tenants, allowedTenants should return array', async () => {
    const { sut, validJwt } = makeSut();

    const mockOidcInstance = getDefaultOidcMock();

    vitest.spyOn(Credor, 'findAll').mockResolvedValueOnce([
      {
        id_credor: 1,
        id_credor_externo: 'a1',
        nome_credor: 'A2',
        escritorio_id_escritorio: 1,
        escritorio: {
          id_escritorio: 1,
          nome_escritorio: 'first office',
        },
      },
      {
        id_credor: 2,
        id_credor_externo: 'a2',
        nome_credor: 'A2',
        escritorio_id_escritorio: 2,
        escritorio: { id_escritorio: 2, nome_escritorio: 'second office' },
      },
    ] as Credor[]);

    const context = makeContext({
      getRequestResponse: {
        headers: { authorization: 'Bearer ' + validJwt, [IMPERSONATE_OFFICE_TENANT_HEADER]: 2 },
        jwt: validJwt,
        oidc: mockOidcInstance,
      },
    });
    const res = await sut.canActivate(context);
    expect(res).toBeTruthy();
    const req = context.switchToHttp().getRequest<Request>();
    expect(req.tenants_ctx?.allowedTenants).toEqual([
      {
        id: 1,
        name: 'first office',
      },
      {
        id: 2,
        name: 'second office',
      },
    ]);
    expect(req.tenants_ctx.getSelectedCreditor()).toEqual({
      id_credor: 2,
      id_credor_externo: 'a2',
      nome_credor: 'A2',
      escritorio_id_escritorio: 2,
      escritorio: {
        id_escritorio: 2,
        nome_escritorio: 'second office',
      },
    });

    expect(req.tenants_ctx instanceof UserDefaultContext).toBeTruthy();
  });
});
