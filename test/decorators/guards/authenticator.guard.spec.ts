import * as requestIp from 'request-ip'; // Importa o módulo inteiro

import { ConfigurationEnv } from '@app/config/configuration.env';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { SharedMethodsAuthorization } from '@app/decorators/guards/shared.methods.authenticator';
import jsonwebtoken from 'jsonwebtoken';
import { Oidc_Provider } from '@app/models/oidc_provider';
import jwkToPem from 'jwk-to-pem';
import { RouteMetrics } from '@app/prometheus.module';
import { vitest } from 'vitest';
import { AuthenticatorGuard } from '@app/decorators/guards/authenticator.guard';
import { mockAccessToken } from '../../utils/massa.utils';
import { GuardErrors } from '@app/decorators/guards/types/error.codes';
vitest.mock('starkbank');
vitest.mock('jwk-to-pem');
vitest.mock('@app/prometheus.module');
vitest.mock('request-ip'); // indica que esse módulo será mockado

const met = new RouteMetrics();

function makeSut(jwtBody?: object) {
  const validJwt = jsonwebtoken.sign(
    { iss: 'http://localhost.splitc.com.br', client_id: 'invalid client_id', ...jwtBody },
    'shh',
    { keyid: 'valid_kid' },
  );
  const config = new ConfigurationEnv();
  const shared = new SharedMethodsAuthorization(config);

  Object.defineProperty(config, 'tokenValidationEnabled', { value: true });
  Object.defineProperty(config, 'jwtIssuer', { value: 'shh' });

  const sut = new AuthenticatorGuard(shared, config, met);

  return {
    config,
    shared,
    sut,
    validJwt,
  };
}

function makeContext({ getRequestResponse }: { getRequestResponse: unknown }): ExecutionContext {
  return {
    switchToHttp() {
      return {
        getRequest() {
          return getRequestResponse;
        },
      };
    },
  } as ExecutionContext;
}

describe('Authenticator guard', () => {
  beforeEach(() => {
    vitest.clearAllMocks();
  });

  it('should throw an UnauthorizedException when the jwt not found', async () => {
    const { sut } = makeSut();
    const context = makeContext({
      getRequestResponse: { headers: { authorization: '' } },
    });
    try {
      await sut.canActivate(context);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.JWT_NOT_FOUND);
    }
  });

  it('should throw an UnauthorizedException when throw read jwt part', async () => {
    const { sut } = makeSut();

    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'ASD' } },
    });

    try {
      await sut.canActivate(context);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.ERROR_READ_JWT);
    }
  });

  it('should throw an UnauthorizedException when the jwt not has iss in body', async () => {
    const { sut } = makeSut();
    const accessToken = mockAccessToken();

    const context = makeContext({
      getRequestResponse: { headers: { authorization: `Bearer ${accessToken}` } },
    });

    try {
      await sut.canActivate(context);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.JWT_INVALID_ISSUER);
    }
  });

  it('should throw an error if oidc was not found', async () => {
    const { sut, validJwt } = makeSut();
    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt } },
    });

    vitest.spyOn(Oidc_Provider, 'findByIssuer').mockResolvedValueOnce(undefined);

    try {
      await sut.canActivate(context);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.OIDC_NOT_FOUND);
    }
  });

  it('should throw an error if invalid ips', async () => {
    const { sut, validJwt } = makeSut();
    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt } },
    });
    vi.mocked(requestIp.getClientIp).mockReturnValue('***********');
    vitest
      .spyOn(Oidc_Provider, 'findByIssuer')
      .mockResolvedValueOnce({ opts: { src_ip_ranges: ['127.0.0.1'] } } as any);

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.INVALID_IP);
    }
  });

  it('should throw an error if oidc.validateAccessToken returned false', async () => {
    const { sut, validJwt } = makeSut();
    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt } },
    });

    const mockOidcInstance = {
      validateAccessToken: vitest.fn().mockReturnValue(false),
    } as unknown as Oidc_Provider;

    vitest.spyOn(Oidc_Provider, 'findByIssuer').mockResolvedValueOnce(mockOidcInstance);

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.JWT_INVALID);
    }
  });

  it('should return true when all is right, validate', async () => {
    const { sut, validJwt } = makeSut();
    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt } },
    });

    const mockOidcInstance = {
      validateAccessToken: vitest.fn().mockReturnValue(true),
    } as unknown as Oidc_Provider;

    vitest.spyOn(Oidc_Provider, 'findByIssuer').mockResolvedValueOnce(mockOidcInstance);
    const response = await sut.canActivate(context);
    const request = context.switchToHttp().getRequest();
    expect(response).toBe(true);
    expect(request.jwt).toBe(validJwt);
    expect(request.oidc).toBe(mockOidcInstance);
  });
});
