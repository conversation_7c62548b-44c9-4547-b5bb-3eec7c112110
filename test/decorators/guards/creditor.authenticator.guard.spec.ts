import { ConfigurationEnv } from '@app/config/configuration.env';
import { ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { LoginDomain } from '../../../src/domain/login.domain';
import { CreditorAuthenticatorGuard } from '@app/decorators/guards/creditor.authenticator.guard';
import { SharedMethodsAuthorization } from '@app/decorators/guards/shared.methods.authenticator';
import jsonwebtoken from 'jsonwebtoken';
import { AuthenticationContextDto } from '@app/dto/authenticationContextDto';
import { OidcProviderDomain } from '@app/domain/oidc.domain';
import { RouteMetrics } from '@app/prometheus.module';
import { vitest } from 'vitest';
import { GuardErrors } from '@app/decorators/guards/types/error.codes';
import { IMPERSONATE_USER_OFFICE_ID_HEADER } from 'shared-types';
vitest.mock('starkbank');
vitest.mock('jwk-to-pem');
vitest.mock('@app/prometheus.module');

const met = new RouteMetrics();

function makeSut(jwtBody?: object) {
  const validJwt = jsonwebtoken.sign(
    { iss: 'http://localhost.splitc.com.br', client_id: 'invalid client_id', ...jwtBody },
    'shh',
    { keyid: 'valid_kid' },
  );
  const config = new ConfigurationEnv();
  const shared = new SharedMethodsAuthorization(config);
  const loginDomain = {
    loadAuthenticationContext: vitest.fn(),
  } as unknown as LoginDomain;
  const oidcProviderDomain = {
    updatedJwks: vitest.fn(),
  } as unknown as OidcProviderDomain;

  Object.defineProperty(config, 'tokenValidationEnabled', { value: true });
  Object.defineProperty(config, 'jwtIssuer', { value: 'shh' });

  const sut = new CreditorAuthenticatorGuard(loginDomain, met);

  return {
    config,
    shared,
    sut,
    loginDomain,
    validJwt,
    oidcProviderDomain,
  };
}

function makeContext({ getRequestResponse }: { getRequestResponse: unknown }): ExecutionContext {
  return {
    switchToHttp() {
      return {
        getRequest() {
          return getRequestResponse;
        },
      };
    },
  } as ExecutionContext;
}

describe('User authenticator guard', () => {
  beforeEach(() => {
    vitest.clearAllMocks();
  });

  it('should throw an error when userAuthenticationContext is falsy', async () => {
    const { sut, validJwt, loginDomain } = makeSut();
    vitest.spyOn(loginDomain, 'loadAuthenticationContext').mockResolvedValueOnce(undefined);

    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt } },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.CREDITOR_NOT_IDENTIFIED);
    }
  });

  it('should throw an error when credor is not active', async () => {
    const { sut, validJwt, loginDomain } = makeSut();

    vitest
      .spyOn(loginDomain, 'loadAuthenticationContext')
      .mockResolvedValueOnce({ credor: { ativo: false } } as AuthenticationContextDto);

    const context = makeContext({
      getRequestResponse: { headers: { authorization: 'Bearer ' + validJwt } },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.CREDITOR_NOT_IDENTIFIED);
    }
  });

  it('should throw an error when has IMPERSONATE_USER_OFFICE_ID_HEADER but not is service account', async () => {
    const { sut, validJwt, loginDomain } = makeSut();

    vitest
      .spyOn(loginDomain, 'loadAuthenticationContext')
      .mockResolvedValueOnce({ credor: { ativo: true } } as AuthenticationContextDto);

    const context = makeContext({
      getRequestResponse: {
        headers: {
          authorization: 'Bearer ' + validJwt,
          [IMPERSONATE_USER_OFFICE_ID_HEADER]: '123',
        },
      },
    });

    try {
      await sut.canActivate(context);
      expect(true).toBe(false);
    } catch (err) {
      expect(err).toBeInstanceOf(UnauthorizedException);
      expect(err.response.message).toBe(GuardErrors.NOT_SERVICE_ACCOUNT);
    }
  });

  it('should return true when identify creditor', async () => {
    const { sut, validJwt, loginDomain } = makeSut();

    vitest
      .spyOn(loginDomain, 'loadAuthenticationContext')
      .mockResolvedValueOnce({ credor: { ativo: true } } as AuthenticationContextDto);

    const context = makeContext({
      getRequestResponse: {
        headers: {
          authorization: 'Bearer ' + validJwt,
        },
      },
    });

    const result = await sut.canActivate(context);
    expect(result).toBe(true);
  });
});
