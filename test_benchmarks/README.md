## Profiling

This project is used for profiling specific application endpoints  
Each scenario has its own setup and mocking of specific services when needed


## Running a Scenario
- compile and start the application with 
- `pnpm run $SCENARIO_NAME`


## Organizing
* each scenario should have its own folder and script
* each scenario is responsible for: 
- seeding the database
- mocking external calls (you can use MSW for instance)
- running autocannon or your preferred load testing tool against a running application
* the scenario shouldnt run the application (with nest or anything else)
* the application should be ran as close as it runs on production (compile it, run)
* the application should be run possibly using a profiler like [clinic](https://clinicjs.org/heapprofiler/)
* [this package.json](./package.json) should have a command to run each scenario