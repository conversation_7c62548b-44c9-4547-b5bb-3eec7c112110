apiVersion: v1
kind: ConfigMap
metadata:
  name: bff-api-ow-cm
  namespace: apis
data:
  CUSTOMER_PROJECT_ID: splitc-customer-warehouse
  ACCEPTED_AUD: 'https://rules-applier-sync-737jd77uea-rj.a.run.app,https://api.splitc.com.br'
  ACCEPTED_EMAIL: '<EMAIL>,<EMAIL>,<EMAIL>'
  BANKING_EVENTS_ENDPOINT: 'https://api.splitc.com.br/banking/events'
  CLOUD_PROVIDER_REGION: 'sa-east-1'
  CLUSTERIZE: 'false'
  CONTA_AZUL_REDIRECT_URI: 'https://app.splitc.com.br/aplicativos/oauth-redirect-credentials?type=conta_azul'
  DATABASE_DB: 'comissionamento'
  DATABASE_HOST: 'localhost'
  DATABASE_USER: 'comissionamento-backend'
  MAX_DATABASE_CONNECTIONS: '15'
  DATABASE_CONNECTION_IDLE_TIMEOUT: '300000'
  FS_TYPE: 'S3'
  IS_TEST_ENVIRONMENT: 'false'
  JWT_ISSUER: 'https://comissionamento.splitc.com.br'
  LOG_LEVEL: 'debug'
  MATCHER_API_BASE: 'https://matcher.splitc.com.br'
  METABASE_SITE_URL: 'https://metabase.splitc.com.br'
  NODE_PORT: '8080'
  RULE_BACKUP_CONTAINER: 'splitc-comissionamento-backup-prod'
  SHEET_DB_API_BASE: 'https://sheetdb.splitc.com.br'
  AI_API_BASE: 'http://ai-api-service.apis.svc.cluster.local'
  SHEET_FILE_CONTAINER: 'splitc-comissionamento-backend-prod'
  XP_API_BASE: 'https://openapi-xpi.external.splitc.com.br'
  XP_TOKEN_ENDPOINT: 'https://auth-openapi-xpi.external.splitc.com.br/oauth2/v2.0/token'
  PROJECT_ID: 'splitc-production'
  GCS_BUCKET_NAME: 'splitc-comissionamento-backend-prod'
  NODE_ENV: 'production'
  ZOMBIE_API_BASE: 'https://zombie.splitc.com.br'
  DOCUMENT_SIGNATURE_API_BASE: 'https://documentsigner.splitc.com.br'
  SAML_FRONT_CALLBACK: 'https://app.splitc.com.br/callback/auth'
  SAML_POST_CALLBACK: 'https://api.splitc.com.br/login/saml/callback'
  SAML_AUDIENCE: 'https://api.splitc.com.br'
  JWT_AUD: 'https://api.splitc.com.br'
  OUTBOUND_WEBHOOK_PROCESSOR_ON: 'true'
  COGNITO_USER_POOL_ID: 'us-east-1_I4fNsNkqb'
  AWS_LAMBDA_CNAB_ARN: 'arn:aws:lambda:sa-east-1:166758416730:function:cnab'
  STARK_MASTER_WORKSPACE_ID: '5868802164129792'
  STARK_ORGANIZATION_ID: '6167869326884864'
  SSO_JWT_ISSUER: 'https://sso.splitc.com.br'
  SSO_JWT_KID: 182b72cf80ae9317c1ab4d866bd5119d73834b8b
  NODE_OPTIONS: '--max-old-space-size=1024'
  DISCORD_ALERT_URL: https://discord.com/api/webhooks/1379891578885111838/iEdHFq6YdfimPNKX5OCZLg8WKwqCXww1PFDvPC3DLhjKZndpjO_HI3R3VwqUVp3FiZ-s
  ASSISTANT_UI_BASE_URL: 'https://backend.assistant-api.com'
  ENABLE_PROFILER: 'true'