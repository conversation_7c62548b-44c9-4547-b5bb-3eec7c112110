apiVersion: batch/v1
kind: CronJob
metadata:
  name: bff-vacuum
  namespace: apis
spec:
  schedule: '*/10 * * * *'
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        spec:
          serviceAccountName: bff-service-account
          containers:
            - name: bff-vacuum
              image: 'us-east1-docker.pkg.dev/splitc-global/docker-images/bff-backend:production-20893' # {"$imagepolicy": "apis:bff-api-image-policy"}
              imagePullPolicy: Always
              command: ['npm', 'run', 'vacuum:start:prod']
              resources:
                requests:
                  cpu: '50m'
                  memory: '128Mi'
              envFrom:
                - secretRef:
                    name: bff-api-sc
                - configMapRef:
                    name: bff-api-cm
            - name: db
              image: gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.13.0
              ports:
                - containerPort: 3306
                  name: database
                - containerPort: 9091
                  name: cloudsqlpry-api
              args:
                - --structured-logs
                - --address=0.0.0.0
                - --port=3306
                - --max-sigterm-delay=29s
                - --auto-iam-authn
                - --private-ip
                - --prometheus
                - --quitquitquit
                - splitc-production:southamerica-east1:comissionamento-production-v4
              resources:
                requests:
                  cpu: '10m'
                  memory: '128Mi'
          restartPolicy: OnFailure
