apiVersion: v1
kind: ConfigMap
metadata:
  name: bff-api-ow-cm
  namespace: apis
data:
  CUSTOMER_PROJECT_ID: splitc-customer-warehouse-stg
  ACCEPTED_AUD: 'https://bff-backend-orexuewlia-rj.a.run.app,https://staging-api.splitc.com.br'
  ACCEPTED_EMAIL: '<EMAIL>,<EMAIL>,<EMAIL>'
  BANKING_EVENTS_ENDPOINT: 'https://staging-api.splitc.com.br/banking/events'
  BULK_COMISSAO_POOL: '40'
  CLOUD_PROVIDER_REGION: 'sa-east-1'
  CLUSTERIZE: 'false'
  CONTA_AZUL_REDIRECT_URI: 'https://staging-app.splitc.com.br/aplicativos/oauth-redirect-credentials?type=conta_azul'
  DATABASE_DB: 'comissionamento'
  DATABASE_HOST: 'localhost'
  FS_TYPE: 'S3'
  IS_TEST_ENVIRONMENT: 'true'
  JWT_ISSUER: 'https://staging-comissionamento.splitc.com.br'
  LOG_LEVEL: 'debug'
  MATCHER_API_BASE: 'https://staging-matcher.splitc.com.br'
  METABASE_SITE_URL: 'https://metabase.splitc.com.br'
  NODE_PORT: '8080'
  RULE_BACKUP_CONTAINER: 'splitc-comissionamento-backup-staging'
  SHEET_DB_API_BASE: 'https://staging-sheetdb.splitc.com.br'
  SHEET_FILE_CONTAINER: 'splitc-comissionamento-backend'
  XP_API_BASE: 'https://playground-openapi.xpi.com.br'
  PROJECT_ID: 'splitc-staging'
  GCS_BUCKET_NAME: 'splitc-comissionamento-backend'
  NODE_ENV: 'staging'
  DATABASE_USER: 'comissionamento-backend-stagin'
  ZOMBIE_API_BASE: 'https://staging-zombie.splitc.com.br'
  DOCUMENT_SIGNATURE_API_BASE: 'https://staging-documentsigner.splitc.com.br'
  SAML_FRONT_CALLBACK: 'https://staging-app.splitc.com.br/callback/auth'
  SAML_POST_CALLBACK: 'https://staging-api.splitc.com.br/login/saml/callback'
  AI_API_BASE: 'http://ai-api-service.apis.svc.cluster.local'
  OUTBOUND_WEBHOOK_PROCESSOR_ON: 'true'
  SSO_JWT_ISSUER: 'https://staging-sso.splitc.com.br'
  SSO_JWT_KID: 266f8ad8-49db-4480-aabe-0d02028a949d
  JWT_AUD: 'https://staging-sso.splitc.com.br'
  FRONT_END_URL: 'https://staging-app.splitc.com.br'
  DISCORD_ALERT_URL: https://discord.com/api/webhooks/1379891578885111838/iEdHFq6YdfimPNKX5OCZLg8WKwqCXww1PFDvPC3DLhjKZndpjO_HI3R3VwqUVp3FiZ-s
  ENABLE_PROFILER: 'true'
  ASSISTANT_UI_BASE_URL: 'https://backend.assistant-api.com'
  OUTBOUND_WH_DEBUG: 'true'
