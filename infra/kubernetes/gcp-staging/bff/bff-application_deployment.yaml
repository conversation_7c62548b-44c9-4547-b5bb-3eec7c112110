apiVersion: apps/v1
kind: Deployment
metadata:
  name: bff-api
  namespace: apis
  annotations:
    sidecar.mysql/inject: 'true'
spec:
  selector:
    matchLabels:
      app: bff-api
  replicas: 1
  template:
    metadata:
      labels:
        app: bff-api
    spec:
      serviceAccountName: bff-service-account
      containers:
        - name: bff-api
          image: 'us-east1-docker.pkg.dev/splitc-global/docker-images/bff-backend:staging-20894' # {"$imagepolicy": "apis:bff-api-image-policy-stg"}
          imagePullPolicy: Always
          resources:
            requests:
              cpu: '150m'
              memory: '256Mi'
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 2
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 10
          ports:
            - containerPort: 8080
          envFrom:
            - secretRef:
                name: bff-api-sc
            - configMapRef:
                name: bff-api-cm
        - name: db
          image: gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.13.0
          ports:
            - containerPort: 3306
          args:
            - --structured-logs
            - --address=0.0.0.0
            - --port=3306
            - --max-sigterm-delay=29s
            - --auto-iam-authn
            - splitc-staging:southamerica-east1:comissionamento-staging-v5
          resources:
            requests:
              cpu: '10m'
              memory: '128Mi'
---
apiVersion: v1
kind: Service
metadata:
  name: bff-api-lb
  namespace: apis
  annotations:
    cloud.google.com/backend-config: '{"default": "bff-backend-config"}'
spec:
  selector:
    app: bff-api
  ports:
    - port: 80
      targetPort: 8080
  type: ClusterIP
---
apiVersion: gateway.networking.k8s.io/v1beta1
kind: HTTPRoute
metadata:
  name: bff-route
  namespace: apis
spec:
  hostnames:
    - 'staging-api.splitc.com.br'
  parentRefs:
    - group: gateway.networking.k8s.io
      kind: Gateway
      name: internal-apps-external-http
      namespace: default
      sectionName: https
  rules:
    - backendRefs:
        - group: ''
          kind: Service
          name: bff-api-lb
          port: 80
          weight: 1
      matches:
        - path:
            type: PathPrefix
            value: /
    - backendRefs:
        - group: ''
          kind: Service
          name: webhooks-api-lb
          port: 80
          weight: 1
      matches:
        - path:
            type: PathPrefix
            value: /integrations
    - backendRefs:
        - group: ''
          kind: Service
          name: bff-api-ow-lb
          port: 80
          weight: 1
      matches:
        - path:
            type: PathPrefix
            value: /payroll/events/outbound-webhooks
---
apiVersion: v1
kind: List
items:
  - apiVersion: networking.gke.io/v1
    kind: HealthCheckPolicy
    metadata:
      name: bff-api-healthcheck
      namespace: apis
    spec:
      default:
        config:
          httpHealthCheck:
            port: 80
          type: TCP
      targetRef:
        group: ''
        kind: Service
        name: bff-api-lb
---
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: bff-backend-config
  namespace: apis
spec:
  timeoutSec: 200
  securityPolicy:
    name: 'kubernetes-bff-entry'
