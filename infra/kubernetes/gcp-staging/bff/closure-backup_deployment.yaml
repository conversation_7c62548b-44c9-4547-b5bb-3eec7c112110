apiVersion: apps/v1
kind: Deployment
metadata:
  name: closure-backup
  namespace: apis
  annotations:
    sidecar.mysql/inject: 'true'
spec:
  replicas: 1
  selector:
    matchLabels:
      app: closure-backup
  template:
    metadata:
      labels:
        app: closure-backup
    spec:
      serviceAccountName: bff-service-account
      containers:
        - name: closure-backup
          image: 'us-east1-docker.pkg.dev/splitc-global/docker-images/bff-backend:staging-20894' # {"$imagepolicy": "apis:bff-api-image-policy-stg"}
          imagePullPolicy: Always
          command: ['node', 'closure.backup/main.js']
          ports:
            - containerPort: 8080
          envFrom:
            - secretRef:
                name: bff-api-sc
            - configMapRef:
                name: bff-api-cm
          resources:
            requests:
              cpu: '100m'
              memory: '256Mi'
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 2
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 10
        - name: db
          image: gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.13.0
          ports:
            - containerPort: 3306
          args:
            - --structured-logs
            - --address=0.0.0.0
            - --port=3306
            - --max-sigterm-delay=29s
            - --auto-iam-authn
            - splitc-staging:southamerica-east1:comissionamento-staging-v5
          resources:
            requests:
              cpu: '10m'
              memory: '128Mi'
---
apiVersion: v1
kind: List
items:
  - apiVersion: networking.gke.io/v1
    kind: HealthCheckPolicy
    metadata:
      name: closure-backup
      namespace: apis
    spec:
      default:
        config:
          httpHealthCheck:
            port: 80
          type: TCP
      targetRef:
        group: ''
        kind: Service
        name: closure-backup
