openapi: 3.0.0
info:
  title: API SplitC
  description: ''
  version: '1.0'
servers:
  - url: https://api.splitc.com.br
    description: 'Production'

security:
  - oauth:
      - none

paths:
  /offices/{office_id}/closures/{payout_date}/all-creditors-earnings:
    summary: 'Recebimentos'
    description: 'All Creditors Earnings'
    get:
      tags:
        - Recebimentos
      summary: 'Divisões de um usuário'
      description: Get All Creditors Earnings description.
      responses:
        '200': # status code
          description: A JSON array of user names
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
      parameters:
        - in: path
          name: office_id
          schema:
            type: string
          required: true
          example: 12
        - in: path
          name: payout_date
          schema:
            type: string
          required: true
          example: '2024-01-01'
        - in: query
          name: page_size
          schema:
            type: number
          required: true
          example: 10000
        - in: query
          name: cursor
          schema:
            type: string
          required: true
          example: 1999158474

  /office/{office_id}/users:
    get:
      summary: 'Listar usuários'
      tags:
        - Gestão de Usuários
      parameters:
        - name: office_id
          in: path
          required: true
          schema:
            type: integer
          description: 'id do seu tenant'
      responses:
        '200':
          description: 'Todos os usuários listados com sucesso'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListUserResponse'

  /office/{office_id}/users/{external_creditor_id}:
    get:
      summary: 'Detalhes do usuário'
      description: 'Consulta detalhes de um usuário em específico, como por exemplo os perfis que o mesmo está associado'
      tags:
        - Gestão de Usuários
      parameters:
        - name: office_id
          in: path
          required: true
          schema:
            type: integer
          description: 'id do seu tenant'
        - name: external_creditor_id
          in: path
          required: true
          schema:
            type: integer
          description: 'identificador do usuário para seu tenant'
      responses:
        '200':
          description: 'Todos os usuários listados com sucesso'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserResponse'

  /user/office/{office_id}:
    post:
      summary: 'Criar usuários'
      description: "Cria o usuário caso não exista nenhum outro usuário com o mesmo 'external_creditor_id' já criado em seu tenant. Emails também são únicos"
      tags:
        - Gestão de Usuários
      parameters:
        - name: office_id
          in: path
          required: true
          schema:
            type: integer
          description: 'id do seu tenant'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: 'Usuário criado com sucesso'

  /user/office/{office_id}/{user_id}:
    patch:
      tags:
        - Gestão de Usuários
      summary: 'Alterar usuário'
      description: "Para alterar dados cadastrais de um usuário, ou enviar um novo convite de acesso, ou desabilitar o acesso do usuário ao SplitC. O usuário já deve ter sido criado previamente. <br> * Para inativar um usuário, basta enviar 'active' com false. <br> * Para adicionar usuário a um ou mais perfis, é só mandar os ids dos perfis em 'permission_profile_ids'"
      parameters:
        - name: office_id
          in: path
          description: 'id do seu tenant'
          required: true
          schema:
            type: integer
        - name: user_id
          in: path
          description: 'Identificador único do usuário em seu tenant. É usado para carregamento de informações que ele tem acesso, permissões, e muitas outras coisas. Análogo ao user_id'
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
        required: true
      responses:
        '200':
          description: 'Usuário alterado com sucesso'

  /offices/{office_id}/permission-profile:
    get:
      tags:
        - Gestão de Permissões
      summary: 'Listar Perfis de Acesso'
      description: ''
      parameters:
        - name: office_id
          in: path
          description: 'id do seu tenant'
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 'Perfis listados com sucesso'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ListProfilesResponse'

  /offices/{office_id}/permission-profile/{profile_id}:
    get:
      tags:
        - Gestão de Permissões
      summary: 'Consultar Perfil'
      description: 'Retorna todas as permissões e flags dentro de um perfil. Usuários desse perfil automaticamente possuem as permissões listadas nele.'
      parameters:
        - name: office_id
          in: path
          description: 'id do seu tenant'
          required: true
          schema:
            type: integer
        - name: profile_id
          in: path
          description: 'id do perfil'
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: 'Permissões do perfil listadas com sucesso'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProfileResponse'

  /offices/{office_id}/permission-profile/{profile_id}/permissions:
    get:
      tags:
        - Gestão de Permissões
      summary: 'Consultar usuários dentro de um perfil'
      description: 'Retorna todos os usuários (ids únicos, não external_creditor_id) dentro de um perfil'
      parameters:
        - name: office_id
          in: path
          description: 'id do seu tenant'
          required: true
          schema:
            type: integer
        - name: profile_id
          in: path
          description: 'id do perfil'
          required: true
          schema:
            type: integer

      responses:
        '200':
          description: 'Usuários do perfil listados com sucesso'

  /offices/{office_id}/sheet-db/tab/{tab_id}/rows/filter:
    post:
      summary: 'Consultar linhas de uma tab'
      description: 'Consulta linhas (filtradas) de uma tab. O filtro é enviado através do body.<br> Puxa linhas de uma tabela da fonte de dados caso o payout_period_id não seja enviado. Cuidado! Alterar o schema da tabela pode quebrar o contrato dessa API, já que as colunas são sempre dinâmicas'
      tags:
        - Fonte de dados/Planos de Calculo
      parameters:
        - name: office_id
          in: path
          required: true
          schema:
            type: integer
          description: 'identificador unico de seu tenant'
        - name: tab_id
          in: path
          required: true
          schema:
            type: integer
          description: 'id da tabela que deseja puxar as linhas'
        - name: rows_per_page
          in: query
          schema:
            type: integer
            description: 'quantidade de linhas a trazer por página. Por padrão é 500'
            example: 500
          required: true
        - name: page_number
          in: query
          schema:
            type: integer
          example: 1
          description: 'número da página que deseja puxar. ver a parte de Paginação das respostas'
        - name: payout_period_id
          in: query
          schema:
            type: integer
          required: false
          example: 123
          description: 'identificador único do período do qual deseja puxar as linhas (caso a tabela esteja dentro de Planos de Calculo). Para buscar IDs de período, buscar na API de Plan Groups. Caso não seja enviado, a tabela será buscada de forma atemporal (fonte de dados)'
        - name: pretty
          in: query
          schema:
            type: boolean
          required: false
          example: true
          description: 'caso verdadeiro, a resposta trará os nomes das colunas ao invés de seus identificadores. O nome da coluna também é único'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FilterTabRowsRequest'
      responses:
        '200':
          description: 'Linhas consultadas com sucesso'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FilterTabRowsResponse'
        '404':
          description: 'tabela não encontrada. Talvez este ID de tabela não exista'

#  /offices/{office_id}/creditors/${external_creditor_id}:
#    delete:
#      description: "Exlcuir um usuário. Essa operação é irreversível e dependendo do que "
#      parameters:
#      - name: office_id
#        in: path
#        required: true
#        schema:
#          type: integer
#        description: "id do seu tenant"
#      - name: external_creditor_id
#        in: path
#        required: true
#        schema:
#          type: string
#        description: "id do usuário que deseja excluir"
#      responses:
#        "200":
#          description: "Usuário excluído com sucesso"

components:
  securitySchemes:
    oauth:
      type: oauth2
      description: Client credentials flow. AccessTokens expire in 15 minutes
      flows:
        clientCredentials:
          tokenUrl: https://auth.splitc.com.br/oauth2/token
          scopes:
            none: no_scopes_needed

  schemas:
    CreateUserRequest:
      type: object
      required:
        - active
        - external_creditor_id
      properties:
        active:
          type: boolean
          description: 'Indica se usuário está ativo. Se falso, não consegue logar'
        email:
          type: string
          description: 'Email do usuário. Deve ser único e pode ser usado para notificações e para login via magic link'
        external_creditor_id:
          type: string
          description: 'Identificador único do usuário em seu tenant. É usado para carregamento de informações que ele tem acesso, permissões, e muitas outras coisas. Análogo ao user_id'
        name:
          type: string
          description: "Nome (opcional) do usuário para apresentação mais user-friendly. Caso não informado, será apresentado como 'external_creditor_id'"
        permission_profile_ids:
          type: array
          items:
            type: integer
          description: 'Lista de IDs de perfil em que o usuário deve estar dentro. Os IDs de perfil podem ser obtidos através da chamada de API de listar perfis'
        teams:
          type: array
          items:
            type: string
          description: 'Nomes dos times em que o usuário pertencerá. Pode ser um time existente ou um novo time. Caso o time enviado aqui não exista, será criado'
      description: 'Parâmetros para criação de usuário'

    GetUserResponse:
      type: object
      required:
        - active
      properties:
        active:
          type: boolean
          description: 'Indica se usuário está ativo. Se falso, não consegue logar. Para desabilitar o acesso de um usuário a plataforma, enviar FALSE'
        email:
          type: string
          description: 'Email do usuário. Deve ser único e pode ser usado para notificações e para login via magic link'
        external_creditor_id:
          type: string
          description: 'Identificador único do usuário dentro do seu tenant'
        name:
          type: string
          description: 'Nome (opcional) do usuário para apresentação mais user-friendly'
        permission_profile_ids:
          type: array
          items:
            type: integer
          description: 'Lista de IDs de perfil em que o usuário deve estar dentro. Os IDs de perfil podem ser obtidos através da chamada de API de listar perfis'
        teams:
          type: array
          items:
            type: string
          description: 'Nomes dos times em que o usuário pertencerá. Pode ser um time existente ou um novo time. Caso o time enviado aqui não exista, será criado'
        permissions:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: 'team.creditors.view'
                description: 'nome da flag de permissão (claim)'
              value:
                type: string
                example: 'true'
                description: 'valor da permissão. o formato depende de qual é a claim em questão. Pode ser booleano, um json, csv, etc. Cada claim tem seu formato esperado'
              type:
                type: string
                example: 'office | profile | user'
                description: 'indica em que nível a permissão foi concedida ao usuário <br> * Quando office, significa que vem do perfil padrão<br> * Quando profile, significa que a permissão veio do perfil<br> * Quando user, significa que a permissõa foi concedida diretamente a nível do usuário'
          description: 'Lista de permissões associadas diretamente ao usuário'
      description: 'Informações detalhadas de um usuário'

    UpdateUserRequest:
      type: object
      required:
        - active
      properties:
        active:
          type: boolean
          description: 'Indica se usuário está ativo. Se falso, não consegue logar. Para desabilitar o acesso de um usuário a plataforma, enviar FALSE'
        email:
          type: string
          description: 'Email do usuário. Deve ser único e pode ser usado para notificações e para login via magic link'
        name:
          type: string
          description: "Nome (opcional) do usuário para apresentação mais user-friendly. Caso não informado, será apresentado como 'external_creditor_id'"
        permission_profile_ids:
          type: array
          items:
            type: integer
          description: 'Lista de IDs de perfil em que o usuário deve estar dentro. Os IDs de perfil podem ser obtidos através da chamada de API de listar perfis'
        teams:
          type: array
          items:
            type: string
          description: 'Nomes dos times em que o usuário pertencerá. Pode ser um time existente ou um novo time. Caso o time enviado aqui não exista, será criado'
      description: 'Parâmetros para atualização de usuário de usuário'

    DeleteUserResponse:
      type: object
      properties:
        id_credor:
          type: integer
          description: 'id interno do usuário para o splitc'
        id_credor_externo:
          type: string
          description: 'Identificador único do usuário em seu tenant. É usado para carregamento de informações que ele tem acesso, permissões, e muitas outras coisas. Análogo ao user_id'
        nome_credor:
          type: string
          description: "Nome (opcional) do usuário para apresentação mais user-friendly. Caso não informado, será apresentado como 'external_creditor_id'"

    ListUserResponse:
      type: array
      items:
        type: object
        properties:
          name:
            type: string
          external_creditor_id:
            type: string
            description: 'Identificador único do usuário em seu tenant. É usado para carregamento de informações que ele tem acesso, permissões, e muitas outras coisas. Análogo ao user_id'
          email:
            type: string
            description: 'Email cadastrado para o usuário'
          active:
            type: boolean
            description: 'Indica o estado atual do usuario'
          teams:
            type: array
            items:
              type: string
          profiles:
            type: array
            items:
              type: object
              properties:
                id:
                  type: integer
                  example: 1
                  description: 'id do perfil'
                name:
                  type: string
                  example: 'Administradores'
                  description: 'nome do perfil'

    ListProfilesResponse:
      type: array
      items:
        type: object
        properties:
          id:
            type: integer
            description: 'Identificador único do perfil. Pode ser usado em criar ou alterar usuarios'
          name:
            type: string
            example: 'Administradores'
            description: 'Nome do perfil'
          priority:
            type: integer
            description: 'Prioridade do perfil (caso um usuário esteja em mais de um perfil)'
          creditors_count:
            type: integer
            example: 2
            description: 'Quantidade de usuários dentro desse perfil'

    GetProfileResponse:
      type: object
      properties:
        id:
          type: integer
          example: 80
          description: 'id do perfil'
        name:
          type: string
          example: 'Lider de time'
          description: 'nome do perfil'
        priority:
          type: integer
          description: 'prioridade do perfil'
        permission_data:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: 'team.creditors.view'
                description: 'nome da flag de permissão (claim)'
              value:
                type: string
                example: 'true'
                description: 'valor da permissão. o formato depende de qual é a claim em questão. Pode ser booleano, um json, csv, etc. Cada claim tem seu formato esperado'

    GetProfileUsersResponse:
      type: object
      properties:
        team_ids:
          type: array
          items:
            type: integer
        creditors_ids:
          type: array
          example: [1, 2, 3]
          description: "ids UNICOS de usuário dentro dos perfis. É o id retornado em 'id' na lista de usuários"
          items:
            type: integer

    FilterTabRowsRequest:
      type: object
      description: 'Descreve como filtrar os resultados de uma tabela. Pode ser enviado vazio para trazer todos os registros'
      properties:
        filter:
          type: array
          items:
            type: object
            properties:
              colId:
                type: string
                description: 'Identificador da coluna pela qual deseja filtrar. Para ver a estrutura de colunas de uma tabela, consultar a tabela pelo tab_id'
                example: '1'
              term:
                type: array
                description: 'Lista de termos pelos quais deseja filtrar a coluna. Deve ser um match exato'
                items:
                  type: string
                example: '2024-01-01'

    FilterTabRowsResponse:
      type: object
      description: 'Resposta de um filtro de tabela e a paginação de suas linhas'
      properties:
        rows:
          type: array
          description: 'Cada um dos registros representa uma linha na tabela. O objeto tem o identificador ou nome da coluna como chave dos objetos e o valor é o valor efetivo, sempre strings.'
          items:
            type: object
            properties:
              $IDENTIFICADOR_DA_COLUNA:
                type: string
                description: 'Identificador ou nome da coluna, dependendo se a consulta foi feita com PRETTY ou não. O valor dessa propriedade é o valor daquela coluna naquela linha'
                example: '10000'

        pagination:
          type: object
          properties:
            page:
              type: integer
              description: 'número da página retornada'
            total_pages:
              type: integer
              description: 'quantidade total de páginas que essa consulta possui'
            total_items:
              type: integer
              description: 'quantidade total de itens que sua consulta possui'
