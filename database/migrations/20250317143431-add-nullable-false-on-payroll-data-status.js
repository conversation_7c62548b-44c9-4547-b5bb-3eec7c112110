'use strict';

const TABLE_NAME = 'payroll_data';
const COLUMN_TO_EDIT = 'status';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      const sql = `
        update 
        payroll_data pd 
            inner join payroll p 
              on p.id = pd.payroll_id 
            inner join payroll_config pc
              on pc.id = p.config_id    
        set pd.status = REPLACE(JSON_EXTRACT(pc.status_flow_config, "$.attributes.initial_status"), '"' , '')
        where status is null
      `

      await queryInterface.sequelize.query(sql, {
        type: Sequelize.QueryTypes.UPDATE,
        transaction
      });

      await queryInterface.changeColumn(TABLE_NAME, COLUMN_TO_EDIT, {
        allowNull: false,
        type: Sequelize.STRING(50),
      }, { transaction });
      await transaction.commit(); 
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn(TABLE_NAME, COLUMN_TO_EDIT, {
      allowNull: true,
      type: Sequelize.STRING(50),
    });
  },
};
