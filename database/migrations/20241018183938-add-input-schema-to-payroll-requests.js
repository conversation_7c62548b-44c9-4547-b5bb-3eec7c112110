'use strict';
const TABLE_NAME = 'payroll_requests';

const NEW_COLUMN = 'input_schema';

const COLUMN_TO_UPDATE = 'inputs';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE_NAME, NEW_COLUMN, {
      type: Sequelize.JSON,
      allowNull: true,
    });
    await queryInterface.changeColumn(TABLE_NAME, COLUMN_TO_UPDATE, {
      type: Sequelize.JSON,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE_NAME, NEW_COLUMN);
    await queryInterface.changeColumn(TABLE_NAME, COLUMN_TO_UPDATE, {
      type: Sequelize.JSON,
      allowNull: false,
    });
  },
};
