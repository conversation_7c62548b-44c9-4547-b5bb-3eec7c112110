'use strict';
const TABLE_NAME = 'ocr_extraction_config';
const COLUMN_NAME = 'api_key';
module.exports = {
  async up(queryInterface, Sequelize) {
    return queryInterface.removeColumn(TABLE_NAME, COLUMN_NAME);
  },

  async down(queryInterface, Sequelize) {
    return queryInterface.addColumn(TABLE_NAME, COLUMN_NAME, {
      type: Sequelize.STRING,
      allowNull: false,
      after: 'type',
    });
  },
};
