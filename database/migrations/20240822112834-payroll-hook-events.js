'use strict';

const TABLE_NAME = 'payroll_hook_events';

const EXTERNAL_ID_FIELD = 'external_id';
const TYPE_FIELD = 'type';
const EXTERNAL_ID_INDEX_NAME = 'idx_external_id';
const EXTERNAL_ID_TYPE_CONSTRAINT_NAME = 'UQ_EXTERNAL_ID_TYPE';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(TABLE_NAME, {
      id: {
        type: Sequelize.BIGINT.UNSIGNED,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
      },
      payroll_id: {
        type: Sequelize.BIGINT.UNSIGNED,
        allowNull: false,
        references: {
          model: 'payroll',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      creditor_id: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      grouping: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      amount: {
        type: Sequelize.DECIMAL(65, 2),
        allowNull: false,
      },
      external_id: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE(6),
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE(6),
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
      },
    });

    await queryInterface.addIndex(TABLE_NAME, {
      name: EXTERNAL_ID_INDEX_NAME,
      fields: [EXTERNAL_ID_FIELD],
    });

    await queryInterface.addConstraint(TABLE_NAME, {
      type: 'UNIQUE',
      name: EXTERNAL_ID_TYPE_CONSTRAINT_NAME,
      fields: [EXTERNAL_ID_FIELD, TYPE_FIELD],
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable(TABLE_NAME);
  },
};
