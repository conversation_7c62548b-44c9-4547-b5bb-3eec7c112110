'use strict';

const TABLE_NAME = 'payroll_requests';

const COLUMN_NAME = 'request_key';

const INDEX_NAME = 'request_key_index';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addIndex(TABLE_NAME, [COLUMN_NAME], {
      name: INDEX_NAME,
      unique: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex(TABLE_NAME, INDEX_NAME);
  },
};
