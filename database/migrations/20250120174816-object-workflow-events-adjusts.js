'use strict';

const TABLE_NAME = 'object_workflow_events';
const UNIQUE_CONSTRAINT_NAME = 'object_workflow_events_object_workflow_id_status_unique';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Remove unused columns
    await queryInterface.removeColumn(TABLE_NAME, 'amount');
    await queryInterface.removeColumn(TABLE_NAME, 'object_key');

    // Remove unique constraint by dropping and re-adding office_id with the updated schema
    await queryInterface.removeColumn(TABLE_NAME, 'office_id');
    await queryInterface.addColumn(TABLE_NAME, 'office_id', {
      type: Sequelize.INTEGER(10).UNSIGNED,
      allowNull: false,
      references: {
        model: 'escritorio',
        key: 'id_escritorio',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    // new relevant columns
    await queryInterface.addColumn(TABLE_NAME, 'object_workflow_id', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'object_workflow',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
    await queryInterface.addColumn(TABLE_NAME, 'data', {
      type: Sequelize.JSON,
      allowNull: true,
    });
    return queryInterface.addConstraint(TABLE_NAME, {
      name: UNIQUE_CONSTRAINT_NAME,
      type: 'unique',
      fields: ['object_workflow_id', 'status'],
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE_NAME, 'data');
    await queryInterface.removeColumn(TABLE_NAME, 'object_workflow_id');

    await queryInterface.addColumn(TABLE_NAME, 'object_key', {
      type: Sequelize.STRING,
      allowNull: false,
    });
    await queryInterface.addColumn(TABLE_NAME, 'amount', {
      type: Sequelize.DECIMAL(65, 2),
      allowNull: false,
    });
  },
};
