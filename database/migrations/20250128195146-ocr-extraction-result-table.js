'use strict';

const TABLE_NAME = 'ocr_extraction_result';

const COLUMNS = Sequelize => ({
  id: {
    type: Sequelize.BIGINT.UNSIGNED,
    primaryKey: true,
    allowNull: false,
    autoIncrement: true,
  },
  ocr_extration_config_id: {
    type: Sequelize.BIGINT.UNSIGNED,
    allowNull: false,
    references: {
      model: 'ocr_extraction_config',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'RESTRICT',
  },
  file_id: {
    type: Sequelize.BIGINT.UNSIGNED,
    allowNull: false,
    references: {
      model: 'file',
      key: 'id',
    },
    onDelete: 'CASCADE',
  },
  result: {
    type: Sequelize.JSON,
    allowNull: false,
  },
  extracted_at: {
    type: Sequelize.DATE(6),
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
  },
});
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(TABLE_NAME, COLUMNS(Sequelize));
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable(TABLE_NAME);
  },
};
