'use strict';

const TABLE_NAME = 'payroll_data';
const TABLE_PAYMENT_KEY = 'payment_key';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TABLE \`${TABLE_NAME}\`
      ADD COLUMN \`${TABLE_PAYMENT_KEY}\` VARCHAR(255)
      GENERATED ALWAYS AS (
        CONCAT('"', \`payroll_id\`, '"."', \`creditor_id\`, '"."', COALESCE(\`grouping\`, ''), '"')
      ) STORED
      AFTER \`grouping\`
    `)

    await queryInterface.addIndex(TABLE_NAME, [TABLE_PAYMENT_KEY])
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn(TABLE_NAME, TABLE_PAYMENT_KEY)
  }
};
