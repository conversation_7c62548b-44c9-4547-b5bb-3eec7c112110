'use strict';

const TABLE_NAME = 'payroll_requests';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(TABLE_NAME, {
      id: {
        type: Sequelize.BIGINT.UNSIGNED,
        primaryKey: true,
        allowNull: false,
        autoIncrement: true,
      },
      office_id: {
        type: Sequelize.INTEGER(10).UNSIGNED,
        allowNull: false,
        references: {
          model: 'escritorio',
          key: 'id_escritorio',
        },
        onDelete: 'CASCADE',
      },
      request_key: {
        type: Sequelize.UUID,
        allowNull: false,
      },

      inputs: {
        type: Sequelize.JSON,
        allowNull: false,
      },

      status: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },

      created_by: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'credor',
          key: 'id_credor',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },

      request_owner: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'credor',
          key: 'id_credor',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },

      payroll_config_id: {
        type: Sequelize.BIGINT.UNSIGNED,
        allowNull: false,
        references: {
          model: 'payroll_config',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },

      canceled_at: {
        type: Sequelize.DATE(6),
        allowNull: true,
      },

      created_at: {
        allowNull: false,
        type: Sequelize.DATE(6),
        defaultValue: Sequelize.NOW,
      },

      updated_at: {
        allowNull: false,
        type: Sequelize.DATE(6),
        defaultValue: Sequelize.NOW,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable(TABLE_NAME);
  },
};
