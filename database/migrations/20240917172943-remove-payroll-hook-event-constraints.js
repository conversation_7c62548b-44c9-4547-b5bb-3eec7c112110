'use strict';

const TABLE_NAME = 'payroll_hook_events';
const OLD_STATUS_COLUMN = 'old_status';
const EXTERNAL_ID_TYPE_CONSTRAINT_NAME = 'UQ_EXTERNAL_ID_TYPE';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(TABLE_NAME, EXTERNAL_ID_TYPE_CONSTRAINT_NAME);
    await queryInterface.removeColumn(TABLE_NAME, 'updated_at');
    await queryInterface.addColumn(TABLE_NAME, OLD_STATUS_COLUMN, {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addConstraint(TABLE_NAME, {
      fields: ['external_id', 'type'],
      type: 'unique',
      name: EXTERNAL_ID_TYPE_CONSTRAINT_NAME,
    });
    await queryInterface.addColumn(TABLE_NAME, 'updated_at', {
      type: Sequelize.DATE(6),
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
    });
    await queryInterface.removeColumn(TABLE_NAME, OLD_STATUS_COLUMN);
  },
};
