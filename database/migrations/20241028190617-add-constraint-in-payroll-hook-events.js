'use strict';

const UNIQUE_CONSTRAINT_NAME = 'payroll_hook_events_type_external_id_status_unique';
const TABLE_NAME = 'payroll_hook_events';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint(TABLE_NAME, {
      fields: ['type', 'external_id', 'status'],
      type: 'unique',
      name: UNIQUE_CONSTRAINT_NAME,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(TABLE_NAME, UNIQUE_CONSTRAINT_NAME);
  },
};
