'use strict';

const TABLE_NAME = 'oidc_provider';
const OLD_CONSTRAINT = {
  fields: ['issuer'],
  type: 'unique',
  name: 'UQ_ISSUER',
};

const NEW_CONSTRAINT = {
  fields: ['issuer', 'client_id'],
  type: 'unique',
  name: 'issuer+client_id_UQ',
};

module.exports = {
  async up(queryInterface) {
    await queryInterface.removeConstraint(TABLE_NAME, OLD_CONSTRAINT.name);
    await queryInterface.addConstraint(TABLE_NAME, NEW_CONSTRAINT);
  },

  async down(queryInterface) {
    await queryInterface.addConstraint(TABLE_NAME, OLD_CONSTRAINT);
    await queryInterface.removeConstraint(TABLE_NAME, NEW_CONSTRAINT.name);
  },
};
