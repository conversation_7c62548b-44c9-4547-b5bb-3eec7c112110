'use strict';

const TABLE = 'payroll';
const OLD_CONSTRAINT = 'office_id_payout_date_unique';
const NEW_CONSTRAINT = 'payout_date_group_id_unique';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `ALTER TABLE comissionamento.${TABLE} DROP FOREIGN KEY \`payroll_ibfk_1\`;`
    );
    await queryInterface.removeConstraint(TABLE, OLD_CONSTRAINT); 
    await queryInterface.sequelize.query(`
      ALTER TABLE comissionamento.${TABLE} ADD CONSTRAINT \`payroll_ibfk_1\` FOREIGN KEY (office_id) REFERENCES comissionamento.escritorio(id_escritorio) ON DELETE CASCADE;
    `);
    await queryInterface.addConstraint(TABLE, {
      fields: ['group_id', 'payout_date'],
      type: 'unique',
      name: NEW_CONSTRAINT
    });
  },

  async down(queryInterface, <PERSON>quelize) {
    await queryInterface.sequelize.query(
      `ALTER TABLE comissionamento.${TABLE} DROP FOREIGN KEY \`payroll_group_id_foreign_idx\`;`, 
    );
    await queryInterface.removeConstraint(TABLE, NEW_CONSTRAINT);
    await queryInterface.sequelize.query(`
      ALTER TABLE comissionamento.${TABLE} ADD CONSTRAINT \`payroll_group_id_foreign_idx\` FOREIGN KEY (group_id) REFERENCES comissionamento.payroll_group(id) ON DELETE CASCADE;
    `);
    await queryInterface.addConstraint(TABLE, {
      fields: ['office_id', 'payout_date'],
      type: 'unique',
      name: OLD_CONSTRAINT
    });
  },
};
