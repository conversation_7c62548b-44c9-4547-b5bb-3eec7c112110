'use strict';

const TABLE_NAME = 'creditor_data';

const CreditorData = Sequelize => ({
  id: {
    type: Sequelize.BIGINT.UNSIGNED,
    primaryKey: true,
    allowNull: false,
    autoIncrement: true,
  },
  creditor_id: {
    type: Sequelize.INTEGER.UNSIGNED,
    allowNull: false,
    references: {
      model: "credor",
      key: 'id_credor',
    },
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
    unique: true,
  },
  data: {
    type: Sequelize.JSON,
    allowNull: false,
  },
  created_at: {
    type: Sequelize.DATE(6),
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
  },
  updated_at: {
    type: Sequelize.DATE(6),
    allowNull: false, 
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)'),
  },
});

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(TABLE_NAME, CreditorData(Sequelize));
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable(TABLE_NAME);
  },
};
