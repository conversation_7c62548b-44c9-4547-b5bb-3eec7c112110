'use strict';

const TABLE_NAME = 'outbound_webhook_config';
const COLUMN_TEMPLATE_HEADER = 'header_template';
const COLUMN_TEMPLATE_BODY = 'body_template';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn(TABLE_NAME, COLUMN_TEMPLATE_HEADER, {
      type: Sequelize.TEXT,
      allowNull: false,
    });
    await queryInterface.changeColumn(TABLE_NAME, COLUMN_TEMPLATE_BODY, {
      type: Sequelize.TEXT,
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn(TABLE_NAME, COLUMN_TEMPLATE_HEADER, {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.changeColumn(TABLE_NAME, COLUMN_TEMPLATE_BODY, {
      type: Sequelize.TEXT,
      allowNull: true,
    });
  },
};
