'use strict';

const TABLE_NAME = 'closure_sheet';
const COLUMN_NAME = 'additional_data';
const AFTER_COLUMN = 'file_id';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE_NAME, COLUMN_NAME, {
      after: AFTER_COLUMN,
      allowNull: true,
      type: Sequelize.JSON,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE_NAME, COLUMN_NAME);
  },
};
