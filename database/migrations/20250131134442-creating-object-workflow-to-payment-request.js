'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      // creating object_workflow
      const insertObjectWorkflowSql =
        'INSERT INTO object_workflow (office_id, object_key, `type`, status, input_data, input_schema, workflow_id, created_by, object_owner, created_at, updated_at, canceled_at) ';
      const selectToObjects = `SELECT office_id, request_key, 'payment_request', status, inputs, input_schema, payroll_config_id, created_by, request_owner, created_at, updated_at, canceled_at FROM payment_request;`;

      await queryInterface.sequelize.query(insertObjectWorkflowSql + selectToObjects, {
        type: queryInterface.sequelize.QueryTypes.INSERT,
        transaction,
      });

      // creating object_workflow_events
      const insertEventsSql =
        'INSERT INTO object_workflow_events (status, old_status, requester, created_at, office_id, object_workflow_id, `data`) ';
      const selectToEvents = `
          SELECT 
            ow.status, 
            NULL, 
            ow.created_by, 
            ow.created_at, 
            ow.office_id, 
            ow.id,  
            JSON_OBJECT(
              "id", pr.id,
              "office_id", pr.office_id,
              "request_key", pr.request_key,
              "amount", pr.amount,
              "created_by", pr.created_by,
              "request_owner", pr.request_owner,
              "payroll_config_id", pr.payroll_config_id,
              "canceled_at", pr.canceled_at,
              "created_at", pr.created_at,
              "created_at", pr.created_at,
              "grouping", pr.\`grouping\`
            )
          from object_workflow ow
          inner join payment_request pr on pr.request_key = ow.object_key;
        `;

      await queryInterface.sequelize.query(insertEventsSql + selectToEvents, {
        type: queryInterface.sequelize.QueryTypes.INSERT,
        transaction,
      });

      await transaction.commit();
    } catch {
      await transaction.rollback();
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      const selectRequestKeySql = `SELECT request_key FROM payment_request`;
      const sql = `delete from object_workflow where object_key in (${selectRequestKeySql}) and type = 'payment_request'`;
      await queryInterface.sequelize.query(sql, {
        type: queryInterface.sequelize.QueryTypes.DELETE,
      });

      await transaction.commit();
    } catch {
      await transaction.rollback();
    }
  },
};
