'use strict';

const TABLE_NAME = 'payment_request';

const STATUS = 'status';
const INPUT_SCHEMA = 'input_schema';
const INPUTS = 'inputs';
const CREATED_BY = 'created_by';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE_NAME, STATUS);
    await queryInterface.removeColumn(TABLE_NAME, INPUT_SCHEMA);
    await queryInterface.removeColumn(TABLE_NAME, INPUTS);
    await queryInterface.removeColumn(TABLE_NAME, CREATED_BY);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE_NAME, STATUS, {
      type: Sequelize.STRING(50),
      allowNull: false,
    });

    await queryInterface.addColumn(TABLE_NAME, INPUT_SCHEMA, {
      type: Sequelize.JSON,
      allowNull: false,
    });

    await queryInterface.addColumn(TABLE_NAME, INPUTS, {
      type: Sequelize.JSON,
      allowNull: false,
    });

    await queryInterface.addColumn(TABLE_NAME, CREATED_BY, {
      type: Sequelize.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'credor',
        key: 'id_credor',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  },
};
