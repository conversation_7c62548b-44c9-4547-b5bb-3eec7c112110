'use strict';

const TABLE_NAME = 'payroll_data_request';
const COLUMN_NAME = 'updated_at';

module.exports = {
  async up (queryInterface, Sequelize) {
     await queryInterface.addColumn(TABLE_NAME, COLUMN_NAME, {
      allowNull: false,
      type: Sequelize.DATE(6),
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
     });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE_NAME, COLUMN_NAME);
  }
};
