'use strict';

const TABLE_NAME = 'object_workflow';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable(TABLE_NAME, {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      office_id: {
        type: Sequelize.INTEGER(10).UNSIGNED,
        allowNull: false,
        references: {
          model: 'escritorio',
          key: 'id_escritorio',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      object_key: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM('payroll_data', 'payment_request'),
        allowNull: false,
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      amount: {
        type: Sequelize.DECIMAL(65, 2),
        allowNull: false,
      },
      workflow_id: {
        type: Sequelize.BIGINT.UNSIGNED,
        allowNull: false,
        references: {
          model: 'payroll_config',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      created_by: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'credor',
          key: 'id_credor',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      object_owner: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false,
        references: {
          model: 'credor',
          key: 'id_credor',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      grouping: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      input_data: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      input_schema: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE(6),
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
      },
      updated_at: {
        type: Sequelize.DATE(6),
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
        allowNull: false,
      },
      canceled_at: {
        type: Sequelize.DATE(6),
        allowNull: true,
      },
    });

    await queryInterface.addIndex(TABLE_NAME, ['status']);
    await queryInterface.addIndex(TABLE_NAME, ['office_id', 'created_at']);

    await queryInterface.addConstraint(TABLE_NAME, {
      fields: ['office_id', 'object_key'],
      type: 'unique',
      name: 'object_workflow_office_id_object_key_unique',
    });
  },

  down: async queryInterface => {
    await queryInterface.dropTable(TABLE_NAME);
  },
};
