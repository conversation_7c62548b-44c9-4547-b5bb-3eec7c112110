'use strict';

const TABLE_NAME = 'outbound_webhook_config';
const COLUMN_CONTEXT = 'context_schema';
const COLUMN_TEMPLATES = 'templates';
const COLUMN_NAME = 'name';
const COLUMN_TEMPLATE_HEADER = 'header_template';
const COLUMN_TEMPLATE_BODY = 'body_template';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE_NAME, COLUMN_NAME, {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'Default',
    });
    await queryInterface.addColumn(TABLE_NAME, COLUMN_TEMPLATE_HEADER, {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn(TABLE_NAME, COLUMN_TEMPLATE_BODY, {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.sequelize.query(`
      UPDATE 
          outbound_webhook_config
      SET 
          body_template = JSON_UNQUOTE(JSON_EXTRACT(templates, "$.body_template")),
          header_template = JSON_UNQUOTE(JSON_EXTRACT(templates, "$.header_template"));
    `);

    await queryInterface.removeColumn(TABLE_NAME, COLUMN_CONTEXT);
    await queryInterface.removeColumn(TABLE_NAME, COLUMN_TEMPLATES);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE_NAME, COLUMN_TEMPLATES, {
      type: Sequelize.TEXT,
      allowNull: false,
    });
    await queryInterface.addColumn(TABLE_NAME, COLUMN_CONTEXT, {
      type: Sequelize.STRING,
      allowNull: false,
    });

    await queryInterface.sequelize.query(`
      UPDATE
          outbound_webhook_config
      SET
          templates = JSON_OBJECT(
            'body_template', body_template,
            'header_template', header_template
          );
    `);

    await queryInterface.removeColumn(TABLE_NAME, COLUMN_NAME);
    await queryInterface.removeColumn(TABLE_NAME, COLUMN_TEMPLATE_HEADER);
    await queryInterface.removeColumn(TABLE_NAME, COLUMN_TEMPLATE_BODY);
  },
};
