'use strict';

const TO_CREATE_ON_ATRIBUTOS_CREDOR = `
  select
    c.id_credor, "payroll.status.manager" as atributo, "true" as valor
  from
    atributos_credor ac
  inner join credor c on
    ac.credor_id_credor = c.id_credor
  inner join escritorio e on
    e.id_escritorio = c.escritorio_id_escritorio
  inner join atributos_escritorio ae on
    ae.escritorio_id_escritorio = e.id_escritorio
  where 
    (ac.atributo = 'generic.data.import' and ac.valor = "true")
    and (
      (ac.atributo = "feature.payroll.v2" and ac.valor = "true")
      or (ae.atributo = "feature.payroll.v2" and ae.valor = "true")
    )
    and not (ac.atributo = 'payroll.status.manager' and ac.valor = "true")
`;

module.exports = {
  async up (queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.sequelize.query(`
        INSERT IGNORE into atributos_credor (credor_id_credor, atributo, valor) (
          ${TO_CREATE_ON_ATRIBUTOS_CREDOR}
        )
      `, {
        type: Sequelize.QueryTypes.INSERT,
        transaction
      })

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down (queryInterface, Sequelize) {}
};
