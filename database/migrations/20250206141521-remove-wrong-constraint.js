'use strict';

const TABLE_NAME = 'object_workflow_events';
const UNIQUE_CONSTRAINT_NAME = 'object_workflow_events_object_workflow_id_status_unique';
const FK_CONSTRAINT_NAME = 'object_workflow_events_object_workflow_id_foreign_idx';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeConstraint(TABLE_NAME, FK_CONSTRAINT_NAME, {
        transaction,
      });

      await queryInterface.removeConstraint(TABLE_NAME, UNIQUE_CONSTRAINT_NAME, {
        transaction,
      });

      await queryInterface.addConstraint(
        TABLE_NAME,
        {
          name: FK_CONSTRAINT_NAME,
          type: 'FOREIGN KEY',
          fields: ['object_workflow_id'],
          references: {
            table: 'object_workflow',
            field: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'CASCADE',
        },
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.addConstraint(TABLE_NAME, {
        name: UNIQUE_CONSTRAINT_NAME,
        type: 'unique',
        fields: ['object_workflow_id', 'status'],
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
