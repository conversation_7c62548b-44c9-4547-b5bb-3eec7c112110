'use strict';

const TABLE = 'oidc_provider';
const REFERENCE_TABLE = 'organization';
const COLUMN = 'org_id';
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE, COLUMN, {
      type: Sequelize.BIGINT.UNSIGNED,
      allowNull: true,
      after: 'id',
      references: {
        model: REFERENCE_TABLE,
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE, COLUMN);
  },
};
