'use strict';

const TABLE_NAME = 'payroll_requests';
const COLUMN_NAME = 'amount';

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE_NAME, COLUMN_NAME, {
      type: Sequelize.DECIMAL(65, 2),
      allowNull: false,
      after: 'status',
    })
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE_NAME, COLUMN_NAME)
  }
};
