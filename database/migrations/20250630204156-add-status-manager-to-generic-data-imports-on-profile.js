'use strict';

const TO_ADD_ON_PROFILE = `
  update permission_profile pp
  inner join escritorio e on e.id_escritorio = pp.office_id
  inner join atributos_escritorio ae on ae.escritorio_id_escritorio = e.id_escritorio
  set pp.permission_data = JSON_ARRAY_APPEND(pp.permission_data, '$', JSON_OBJECT('name', 'payroll.status.manager', 'value', 'true'))
  where 
    (
      JSON_CONTAINS(pp.permission_data, JSON_OBJECT('name', 'generic.data.import', 'value', 'true'))
    )
    and (
      JSON_CONTAINS(pp.permission_data, JSON_OBJECT('name', 'feature.payroll.v2', 'value', 'true'))
      or (ae.atributo = 'feature.payroll.v2' and ae.valor = "true")
    )
    and not (
      JSON_CONTAINS(pp.permission_data, JSON_OBJECT('name', 'payroll.status.manager', 'value', 'true'))
    );
`

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.sequelize.query(`
        ${TO_ADD_ON_PROFILE}
      `, {
        type: Sequelize.QueryTypes.UPDATE,
        transaction
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {},
};
