'use strict';

const TABLE_NAME = 'payment_request';
const GROUPING_COL = 'grouping';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE_NAME, GROUPING_COL, {
      type: Sequelize.JSON,
      allowNull: true,
      defaultValue: `{}`,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE_NAME, GROUPING_COL);
  },
};
