'use strict';

const TABLE_PAYROLL_REQUESTS = 'payroll_requests';
const TABLE_PAYMENT_REQUEST = 'payment_request';
const TABLE_PAYROLL_DATA_REQUEST = 'payroll_data_request';
const TABLE_PAYMENT_DATA_REQUEST = 'payment_data_request';
const TABLE_PAYROLL_PROCESS_SCHEDULE = 'payroll_process_schedule';
const TABLE_CRON = 'cron';
const TABLE_PAYROLL = 'payroll';
const COLUMN_SCHEDULE_ID = 'schedule_id';
const COLUMN_PAYROLL_REQUEST_ID = 'payroll_request_id';
const COLUMN_PAYMENT_REQUEST_ID = 'payment_request_id';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.renameTable(TABLE_PAYROLL_REQUESTS, TABLE_PAYMENT_REQUEST);
    await queryInterface.renameTable(TABLE_PAYROLL_DATA_REQUEST, TABLE_PAYMENT_DATA_REQUEST);
    await queryInterface.renameTable(TABLE_PAYROLL_PROCESS_SCHEDULE, TABLE_CRON);

    await queryInterface.renameColumn(TABLE_PAYMENT_DATA_REQUEST, COLUMN_PAYROLL_REQUEST_ID, COLUMN_PAYMENT_REQUEST_ID)

    await queryInterface.changeColumn(TABLE_PAYROLL, COLUMN_SCHEDULE_ID, {
      type: Sequelize.BIGINT.UNSIGNED,
      allowNull: true,
      references: {
        model: TABLE_CRON,
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.renameColumn(TABLE_PAYMENT_DATA_REQUEST, COLUMN_PAYMENT_REQUEST_ID, COLUMN_PAYROLL_REQUEST_ID);

    await queryInterface.renameTable(TABLE_PAYMENT_REQUEST, TABLE_PAYROLL_REQUESTS);
    await queryInterface.renameTable(TABLE_PAYMENT_DATA_REQUEST, TABLE_PAYROLL_DATA_REQUEST);
    await queryInterface.renameTable(TABLE_CRON, TABLE_PAYROLL_PROCESS_SCHEDULE);

    await queryInterface.changeColumn(TABLE_PAYROLL, COLUMN_SCHEDULE_ID, {
      type: Sequelize.BIGINT.UNSIGNED,
      allowNull: true,
      references: {
        model: TABLE_PAYROLL_PROCESS_SCHEDULE,
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  },
};
