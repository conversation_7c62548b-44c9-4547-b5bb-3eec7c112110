'use strict';

const DELETE_ALL_OBJECT_WORKFLOW_EVENTS = `
  DELETE owe.* FROM object_workflow_events owe
    INNER JOIN object_workflow ow
      ON ow.id = owe.object_workflow_id
    WHERE ow.\`type\` = 'payroll_data'
`;

const GET_PAYROLL_DATA_EVENTS_TO_CREATE = `
  SELECT
    pde.new_status, 
    pde.old_status, 
    COALESCE(c.id_credor, ow.object_owner), 
    pde.created_at, 
    ow.office_id, 
    ow.id,
    JSON_OBJECT('amount', pde.amount, 'creditor_id', pde.creditor_id, 'grouping', pde.\`grouping\`, 'metadata', pd.metadata, 'created_at', pd.created_at, 'payroll_id', pd.payroll_id, 'updated_at', pd.updated_at)
  FROM payroll_data_events pde
    INNER JOIN object_workflow ow
      ON ow.object_key = CONCAT('"', pde.payroll_id, '"."', pde.creditor_id, '"."', COALESCE(pde.\`grouping\`, ''), '"')
    LEFT JOIN payroll_data pd
      ON pd.payroll_id = pde.payroll_id
        AND pd.creditor_id = pde.creditor_id
        AND pd.\`grouping\` = pde.\`grouping\`
    LEFT JOIN credor c
      ON c.id_credor_externo = pde.requester
         AND c.escritorio_id_escritorio = ow.office_id
`;

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.sequelize.query(DELETE_ALL_OBJECT_WORKFLOW_EVENTS, {
        type: Sequelize.QueryTypes.DELETE,
        transaction
      })
  
      await queryInterface.sequelize.query(`
        INSERT into object_workflow_events (status, old_status, requester, created_at, office_id, object_workflow_id, \`data\`) (
          ${GET_PAYROLL_DATA_EVENTS_TO_CREATE}
        )
      `, {
        type: Sequelize.QueryTypes.INSERT,
        transaction
      })

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    return;
  },
};
