'use strict';

const TABLE_NAME = 'payment_request';

const CREATED_AT_COLUMN = 'created_at';
const OFFICE_ID_COLUMN = 'office_id';

const INDEX_NAME = 'idx_office_created_at';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addIndex(TABLE_NAME, {
      name: INDEX_NAME,
      fields: [OFFICE_ID_COLUMN, CREATED_AT_COLUMN],
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex(TABLE_NAME, INDEX_NAME);
  },
};
