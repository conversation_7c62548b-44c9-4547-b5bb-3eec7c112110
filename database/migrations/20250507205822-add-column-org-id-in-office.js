'use strict';

const TABLE = 'escritorio';
const REFERENCE_TABLE = 'organization';
const COLUMN = 'org_id';
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE, COLUMN, {
      type: Sequelize.BIGINT.UNSIGNED,
      allowNull: true,
      after: 'id_escritorio',
      references: {
        model: REFERENCE_TABLE,
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE, COLUMN);
  },
};
