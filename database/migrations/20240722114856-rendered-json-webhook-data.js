'use strict';

const TABLE_NAME = 'outbound_webhook_data';
const COLUMN = 'rendered';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn(TABLE_NAME, COLUMN, {
      allowNull: true,
      type: Sequelize.JSON,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn(TABLE_NAME, COLUMN, {
      allowNull: false,
      type: Sequelize.STRING,
    });
  },
};
