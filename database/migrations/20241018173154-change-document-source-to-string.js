'use strict';

const TABLE_NAME = 'document';
const COLUMN_NAME = 'source';
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn(TABLE_NAME, COLUMN_NAME, {
      type: Sequelize.STRING(255),
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn(TABLE_NAME, COLUMN_NAME, {
      type: Sequelize.ENUM('CLOSURE', 'COMMISSION_PLAN', 'PAYROLL'),
      allowNull: false,
      defaultValue: 'CLOSURE',
    });
  },
};
