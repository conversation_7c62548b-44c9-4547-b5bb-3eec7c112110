'use strict';

const TABLE_NAME = 'payroll_data';
const INDEX_UNIQUE = {
  name: 'unique_constraint',
  type: 'UNIQUE',
  fields: ['payroll_id', 'creditor_id', 'grouping'],
};

module.exports = {
  async up (queryInterface) {
    await queryInterface.addConstraint(TABLE_NAME, INDEX_UNIQUE);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.removeConstraint(TABLE_NAME, INDEX_UNIQUE);
  }
};
