'use strict';

const TABLE_NAME = 'org_user_identifier';

const COLUMNS = Sequelize => ({
  id: {
    type: Sequelize.BIGINT.UNSIGNED,
    primaryKey: true,
    allowNull: false,
    autoIncrement: true,
  },
  user_id: {
    type: Sequelize.BIGINT.UNSIGNED,
    allowNull: false,
    references: {
      model: 'user',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'RESTRICT',
  },
  provider_id: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  oidc_id: {
    type: Sequelize.BIGINT.UNSIGNED,
    allowNull: false,
    references: {
      model: 'oidc_provider',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'RESTRICT',
  },
  created_at: {
    type: Sequelize.DATE(6),
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
  },
  updated_at: {
    type: Sequelize.DATE(6),
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)'),
  },
});
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(TABLE_NAME, COLUMNS(Sequelize));
    await queryInterface.addConstraint(TABLE_NAME, {
      fields: ['oidc_id', 'provider_id'],
      type: 'unique',
      name: 'oidc_id_provider_id_unique',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable(TABLE_NAME);
  },
};
