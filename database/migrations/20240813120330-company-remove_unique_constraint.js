'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
        alter table company
        drop
        foreign key company_ibfk_1;
    `);
    await queryInterface.sequelize.query(`
        drop index UNIQUE_office_company_identifier on company;
    `);
    await queryInterface.sequelize.query(`
        drop index UNIQUE_office_name on company;
    `);
    await queryInterface.sequelize.query(`
        alter table company
            add constraint company_ibfk_1
                foreign key (office_id) references escritorio (id_escritorio)
                    on update cascade on delete cascade;
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
        create unique index UNIQUE_office_company_identifier
            on company (office_id, company_identifier);
    `);
    await queryInterface.sequelize.query(`
        create unique index UNIQUE_office_name
            on company (office_id, name);
    `);
  },
};
