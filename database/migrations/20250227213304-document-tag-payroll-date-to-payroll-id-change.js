'use strict';

const GET_DOCUMENT_TAGS_QUERY = `
  SELECT 
    dt.document_id, dt.tag_name, dt.tag_value, f.office_id, c.id_credor_externo
    FROM \`comissionamento\`.\`document_tag\` dt 
    LEFT JOIN \`comissionamento\`.\`document\` d on dt.document_id = d.id
    LEFT JOIN  \`comissionamento\`.\`credor\` c on d.creditor_id = c.id_credor
    LEFT JOIN \`comissionamento\`.\`file\` f on d.file_id = f.id
  WHERE
    d.source = 'PAYROLL'
    AND dt.tag_name = 'unique_payroll_data_key';
`;

const GET_PAYROLLS_QUERY = `
  SELECT 
    p.id, p.office_id, p.payout_date
    FROM \`comissionamento\`.\`payroll\` p;
`;

// "YYYY-MM-DD" has 10 chars
const DATE_LENGTH = 10;

const getTagSplittedValues = (tagValue, creditorId) => {
  const tagDate = tagValue.slice(0, DATE_LENGTH);
  const remaining = tagValue.slice(DATE_LENGTH + 1); // +1 skip the separator after the date

  const grouping = remaining.replace(creditorId, '').slice(1); // removes creditor_id and the separator

  return {
    date: tagDate,
    creditorId,
    grouping,
  };
};

const getTagKey = (officeId, payoutDate) => `${officeId}-${payoutDate}`;

const OBJECT_WORKFLOW_TAG_NAME = 'object_workflow_key';

module.exports = {
  async up(queryInterface, Sequelize) {
    const [documentTags, payrolls] = await Promise.all([
      queryInterface.sequelize.query(GET_DOCUMENT_TAGS_QUERY, {
        type: Sequelize.QueryTypes.SELECT,
      }),
      queryInterface.sequelize.query(GET_PAYROLLS_QUERY, {
        type: Sequelize.QueryTypes.SELECT,
      }),
    ]);

    if (!documentTags.length) return;

    const payrollIdsMap = new Map();

    payrolls.forEach(payroll =>
      payrollIdsMap.set(getTagKey(payroll.office_id, payroll.payout_date), payroll.id),
    );

    const baseInsertQuery =
      'INSERT INTO `comissionamento`.`document_tag` (document_id, tag_name, tag_value) VALUES ';

    let queryValues = '';

    documentTags.forEach((documentTag, index) => {
      const { date, creditorId, grouping } = getTagSplittedValues(
        documentTag.tag_value,
        documentTag.id_credor_externo,
      );

      const payrollId = payrollIdsMap.get(getTagKey(documentTag.office_id, date));

      // not making a nullcheck on payroll because it MUST exist (expecting migration to break if it doesn't)
      const newTagValue = `"${payrollId}"."${creditorId}"."${grouping}"`;

      // adjust the existent tag (unique_payroll_data_key)
      queryValues += `(${documentTag.document_id}, '${documentTag.tag_name}', '${newTagValue}'),`;

      // create a new tag (object_workflow_key) with the same tag value of unique_payroll_data_key
      queryValues += `(${documentTag.document_id}, '${OBJECT_WORKFLOW_TAG_NAME}', '${newTagValue}')`;

      if (index < documentTags.length - 1) {
        queryValues += ', ';
      }
    });

    const finalQuery =
      baseInsertQuery + queryValues + ' ON DUPLICATE KEY UPDATE tag_value = VALUES(tag_value);';

    return queryInterface.sequelize.query(finalQuery);
  },

  async down(queryInterface, Sequelize) {
    return;
  },
};
