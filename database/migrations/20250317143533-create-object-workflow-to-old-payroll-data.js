'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
    const sql = `
    insert into object_workflow (office_id, object_key, \`type\`, status, workflow_id, created_by, object_owner, input_data, input_schema, created_at, updated_at) (
      select 
        p.office_id, 
        CONCAT('"', p.id, '"."', pd.creditor_id, '"."', COALESCE(pd.grouping, ''), '"'), 
        'payroll_data', 
        pd.status, 
        p.config_id, 
        c.id_credor,
        c.id_credor,
        '{}', 
        '{}', 
        pd.created_at, 
        pd.updated_at 
      from 
        payroll_data pd 
        inner join payroll p on p.id = pd.payroll_id
        inner join payroll_config pc on pc.id = p.config_id
        left join credor c on c.escritorio_id_escritorio = p.office_id and c.id_credor_externo = pd.creditor_id
        where c.id_credor is not NULL
    ) ON DUPLICATE KEY UPDATE
      status=pd.status;
    `

    await queryInterface.sequelize.query(sql, {
      type: Sequelize.QueryTypes.INSERT,
    });
  },

  async down (queryInterface, Sequelize) {
    // devido a natureza do insert, o rollback não é possível
  }
};
