'use strict';

const TABLE_NAME = 'closure_sheet';
const COLUMN_TO_ADD = 'deleted_at';
const INDEX_UNIQUE = {
  name: 'UNIQUE_closure_file_origin',
  type: 'UNIQUE',
  fields: ['closure_id', 'file_id', 'origin'],
};
const FILE_FOREIGN_KEY = {
  name: 'closure_sheet_ibfk_2',
  type: 'FOREIGN KEY',
  fields: ['file_id'],
  references: {
    table: 'file',
    field: 'id',
  },
  onDelete: 'CASCADE',
  onUpdate: 'CASCADE',
};

const CLOSURE_FOREIGN_KEY = {
  name: 'closure_sheet_ibfk_1',
  type: 'FOREIGN KEY',
  fields: ['closure_id'],
  references: {
    table: 'fechamento',
    field: 'id_fechamento',
  },
  onDelete: 'CASCADE',
  onUpdate: 'CASCADE',
};

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(TABLE_NAME, FILE_FOREIGN_KEY.name);
    await queryInterface.removeConstraint(TABLE_NAME, CLOSURE_FOREIGN_KEY.name);

    await queryInterface.removeIndex(TABLE_NAME, INDEX_UNIQUE.name);

    await queryInterface.addConstraint(TABLE_NAME, FILE_FOREIGN_KEY);
    await queryInterface.addConstraint(TABLE_NAME, CLOSURE_FOREIGN_KEY);

    await queryInterface.addColumn(TABLE_NAME, COLUMN_TO_ADD, {
      type: Sequelize.DATE(6),
      allowNull: true,
    });
  },

  async down(queryInterface) {
    try {
      await queryInterface.addIndex(TABLE_NAME, INDEX_UNIQUE);
      await queryInterface.removeColumn(TABLE_NAME, COLUMN_TO_ADD);
    } catch (error) {
      console.log(error);
    }
  },
};
