'use strict';

module.exports = {
  async up(queryInterface, <PERSON>quelize) {
    await queryInterface.sequelize.query(`
      ALTER TABLE organization
      ADD COLUMN config JSON NOT NULL DEFAULT (
        JSON_OBJECT(
          'roles_config',
          JSON_ARRAY(
            JSON_OBJECT(
              'role', 'Default',
              'admin_only', false,
              'user_info_matches', JSON_ARRAY(
                JSON_OBJECT('claim', 'provider_id', 'value', '*')
              ),
              'tenants_config', JSON_ARRAY(
                JSON_OBJECT('matcher', JSO<PERSON>_ARRAY('.*'))
              )
            )
          )
        )
      )
      AFTER email_domain;
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('organization', 'config');
  },
};
