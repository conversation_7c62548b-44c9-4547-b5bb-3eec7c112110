'use strict';

const OBJECT_WORKFLOW_TABLE_NAME = 'object_workflow';

module.exports = {
  async up(queryInterface) {
    await queryInterface.removeColumn(OBJECT_WORKFLOW_TABLE_NAME, 'amount');
    await queryInterface.removeColumn(OBJECT_WORKFLOW_TABLE_NAME, 'grouping');
    await queryInterface.removeColumn(OBJECT_WORKFLOW_TABLE_NAME, 'metadata');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn(OBJECT_WORKFLOW_TABLE_NAME, 'amount', {
      type: Sequelize.DECIMAL(65, 2),
      allowNull: false,
    });
    await queryInterface.addColumn(OBJECT_WORKFLOW_TABLE_NAME, 'grouping', {
      type: Sequelize.JSON,
      allowNull: true,
    });
    await queryInterface.addColumn(OBJECT_WORKFLOW_TABLE_NAME, 'metadata', {
      type: Sequelize.JSON,
      allowNull: true,
    });
  },
};
