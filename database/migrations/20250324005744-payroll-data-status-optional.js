'use strict';

const PAYROLL_DATA_TABLE = 'payroll_data';

module.exports = {
  async up(queryInterface, Sequelize) {

      await queryInterface.changeColumn(
        PAYROLL_DATA_TABLE,
        'status',
        {
          type: Sequelize.STRING(50),
          allowNull: true,
        },
      );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn(
      PAYROLL_DATA_TABLE,
      'status',
      {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
    );
  },
};
