'use strict';

const PAYROLL_CONFIG_TABLE = 'payroll_config';
const COLUMN_NAME = 'name';

module.exports = {
  async up(queryInterface, Sequelize) {
    const tableDefinition = await queryInterface.describeTable(PAYROLL_CONFIG_TABLE);
    if (!tableDefinition[COLUMN_NAME]) {
      await queryInterface.addColumn(PAYROLL_CONFIG_TABLE, COLUMN_NAME, {
        type: Sequelize.STRING,
        allowNull: false,
        after: 'office_id',
      });
    }

    const query = `select distinct e.id_escritorio, e.nome_escritorio
    from escritorio e 
	    inner join payroll_config pc on pc.office_id = e.id_escritorio
    where pc.name = '';`;

    const offices = await queryInterface.sequelize.query(query);
    const allOffices = offices.flatMap(offices => offices);

    const promises = allOffices.map(({ id_escritorio, nome_escritorio }) => {
      const query = `update ${PAYROLL_CONFIG_TABLE} pc set pc.name = "Fluxo de Aprovação ${nome_escritorio}" WHERE pc.office_id = ${id_escritorio}`;
      return queryInterface.sequelize.query(query);
    });

    await Promise.all([
      ...promises,
      queryInterface.sequelize.query(
        `update ${PAYROLL_CONFIG_TABLE} pc set pc.name = 'Fluxo de Aprovação Padrão' WHERE pc.office_id is null`,
      ),
    ]);
  },

  async down(queryInterface, Sequelize) {
    return queryInterface.removeColumn(PAYROLL_CONFIG_TABLE, 'name');
  },
};
