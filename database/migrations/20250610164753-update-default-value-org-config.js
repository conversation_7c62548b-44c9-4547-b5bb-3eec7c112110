'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      ALTER TABLE organization
      ALTER COLUMN config SET DEFAULT (
        JSON_OBJECT(
          'role_match_type', 'first_match',
          'roles_config', JSON_ARRAY(
            JSON_OBJECT(
              'role', 'Default',
              'admin_only', false,
              'create_user', false,
              'user_info_matches', JSON_ARRAY(
                JSON_OBJECT('claim', 'provider_id', 'value', JSON_ARRAY('*'))
              ),
              'allowed_tenants_matcher', '.*'
            )
          )
        )
      );
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      ALTER TABLE organization
      ALTER COLUMN config SET DEFAULT (
        JSON_OBJECT(
          'roles_config',
          JSON_ARRAY(
            JSON_OBJECT(
              'role', 'Default',
              'admin_only', false,
              'user_info_matches', JSO<PERSON>_ARRAY(
                JSON_OBJECT('claim', 'provider_id', 'value', '*')
              ),
              'tenants_config', JSON_ARRAY(
                JSON_OBJECT('matcher', JSON_ARRAY('.*'))
              )
            )
          )
        )
      );
    `);
  },
};
