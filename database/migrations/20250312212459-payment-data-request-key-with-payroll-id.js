'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Atualiza a coluna payroll_data_key com base nos dados do JSON payroll_data_info
    await queryInterface.sequelize.query(`
      UPDATE payment_data_request
      SET payroll_data_key = 
        CONCAT(
          '"', CAST(payroll_data_info->>'$.payroll_id' AS CHAR), '"."', 
          CAST(payroll_data_info->>'$.creditor_id' AS CHAR), '"."', 
          COALESCE(CAST(payroll_data_info->>'$.grouping' AS CHAR), ''), '"'
        )
    `);
  },

  async down(queryInterface, Sequelize) {
    // Reverter a mudança não é trivial sem saber o estado anterior, então pode ser deixado em branco ou tratado de outra forma
  },
};
