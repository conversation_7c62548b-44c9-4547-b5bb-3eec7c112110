'use strict';

const OBJECT_WORKFLOW_TABLE = 'object_workflow';
const OBJECT_WORKFLOW_EVENTS_TABLE = 'object_workflow_events';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.changeColumn(
        OBJECT_WORKFLOW_TABLE,
        'created_by',
        {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        OBJECT_WORKFLOW_EVENTS_TABLE,
        'requester',
        {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: true,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await queryInterface.changeColumn(
        OBJECT_WORKFLOW_TABLE,
        'created_by',
        {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
        },
        { transaction },
      );

      await queryInterface.changeColumn(
        OBJECT_WORKFLOW_EVENTS_TABLE,
        'requester',
        {
          type: Sequelize.INTEGER.UNSIGNED,
          allowNull: false,
        },
        { transaction },
      );

      await transaction.commit();
    } catch {
      await transaction.rollback();
    }
  },
};
