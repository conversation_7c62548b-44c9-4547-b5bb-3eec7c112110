'use strict';

const TABLE_NAME = 'payroll_data_events';
const CONSTRAINT_NAME = 'UQ_EXTERNAL_ID_TYPE';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(TABLE_NAME, CONSTRAINT_NAME);
    await queryInterface.removeColumn(TABLE_NAME, 'type');
    await queryInterface.removeColumn(TABLE_NAME, 'external_id');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE_NAME, 'type', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'payroll_data_event',
    });

    await queryInterface.addColumn(TABLE_NAME, 'external_id', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addConstraint(TABLE_NAME, {
      type: 'unique',
      name: CONSTRAINT_NAME,
      fields: ['type', 'external_id'],
    });
  },
};
