'use strict';

const TABLE_NAME = 'ocr_extraction_config';

const COLUMNS = Sequelize => ({
  id: {
    type: Sequelize.BIGINT.UNSIGNED,
    primaryKey: true,
    allowNull: false,
    autoIncrement: true,
  },
  office_id: {
    type: Sequelize.INTEGER(10).UNSIGNED,
    allowNull: true,
    references: {
      model: 'escritorio',
      key: 'id_escritorio',
    },
    onDelete: 'CASCADE',
    unique: true,
  },
  type: {
    type: Sequelize.STRING(26),
    allowNull: false,
  },
  api_key: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  model: {
    type: Sequelize.STRING(26),
    allowNull: false,
  },
  provider: {
    type: Sequelize.STRING(26),
    allowNull: false,
  },
  prompt: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  limiting: {
    type: Sequelize.INTEGER,
    allowNull: true,
  },
  created_at: {
    type: Sequelize.DATE(6),
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
  },
  updated_at: {
    type: Sequelize.DATE(6),
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)'),
  },
});
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(TABLE_NAME, COLUMNS(Sequelize));
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable(TABLE_NAME);
  },
};
