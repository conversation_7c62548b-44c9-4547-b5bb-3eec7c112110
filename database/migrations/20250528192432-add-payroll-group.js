'use strict';

const TABLE = 'payroll_group';
const OFFICE_TABLE = 'escritorio';

const PayrollGroupDefinition = Sequelize => ({
  id: {
    type: Sequelize.BIGINT.UNSIGNED,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  },
  office_id: {
    type: Sequelize.INTEGER.UNSIGNED,
    allowNull: false,
    references: {
      model: OFFICE_TABLE,
      key: 'id_escritorio',
    },
    onDelete: 'CASCADE',
  },
  label: {
    type: Sequelize.STRING(100),
    allowNull: false,
  },
  slug: {
    type: Sequelize.STRING(100),
    allowNull: false,
  },
  created_at: {
    type: Sequelize.DATE(6),
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
  },
  updated_at: {
    type: Sequelize.DATE(6),
    allowNull: true,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)'),
  },
  deleted_at: {
    type: Sequelize.DATE(6),
    allowNull: true,
  },
});

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(TABLE, PayrollGroupDefinition(Sequelize));

    await queryInterface.addConstraint(TABLE, {
      type: 'unique',
      fields: ['office_id', 'slug'],
      name: 'unique_office_id_slug',
    });

    await queryInterface.addColumn('payroll', 'group_id', {
      type: Sequelize.BIGINT.UNSIGNED,
      allowNull: true,
      references: {
        model: 'payroll_group',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('payroll', 'group_id');
    await queryInterface.dropTable(TABLE);
  },
};
