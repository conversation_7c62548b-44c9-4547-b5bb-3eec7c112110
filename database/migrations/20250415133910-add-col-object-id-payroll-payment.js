'use strict';

const TABLE = 'payroll_payment';
const COLUMN = 'object_workflow_id';
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE, COLUMN, {
      type: Sequelize.INTEGER,
      allowNull: true,
      after: 'id',
      references: {
        model: 'object_workflow',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    await queryInterface.sequelize.query(
      `
      UPDATE payroll_payment pp
      INNER JOIN object_workflow ow ON ow.object_key = CONCAT('"', pp.payroll_id, '"."', pp.creditor_id, '"."', COALESCE(pp.category, ''), '"')
      SET pp.object_workflow_id = ow.id
      WHERE pp.object_workflow_id IS NULL
    `,
    );
  },

  async down(queryInterface) {
    await queryInterface.removeColumn(TABLE, COLUMN);
  },
};
