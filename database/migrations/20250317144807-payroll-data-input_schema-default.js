'use strict';

const defaultInputSchema = {
  input_schema: {
    type: 'object',
    required: ['documents'],
    properties: {
      documents: {
        $id: '/schemas/documents',
        type: 'array',
        items: {
          type: 'string',
        },
        minItems: 0,
        maxItems: 5,
      },
    },
  },
};
const PAYROLL_CONFIG_TABLE = 'payroll_config';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `UPDATE \`${PAYROLL_CONFIG_TABLE}\` SET config= '${JSON.stringify(
        defaultInputSchema,
      )}' WHERE graph_type = 'payroll' and config is NULL;`,
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `UPDATE \`${PAYROLL_CONFIG_TABLE}\` SET config= ${null} WHERE graph_type = 'payroll' and config is not NULL;`,
    );
  },
};
