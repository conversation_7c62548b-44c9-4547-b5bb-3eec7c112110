'use strict';

const MAGIC_LINK_ID = 4;

module.exports = {
  async up(queryInterface, Sequelize) {
    return queryInterface.sequelize.query(`
      UPDATE oidc_provider SET opts = JSON_SET(opts, '$.magic_link_variant', 'magic') WHERE id = ${MAGIC_LINK_ID};
    `);
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  },
};
