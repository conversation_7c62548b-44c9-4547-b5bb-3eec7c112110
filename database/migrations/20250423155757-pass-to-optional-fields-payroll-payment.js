'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('payroll_payment', 'payroll_id', {
      type: Sequelize.BIGINT.UNSIGNED,
      allowNull: true,
    });
    await queryInterface.changeColumn('payroll_payment', 'creditor_id', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.changeColumn('payroll_payment', 'object_workflow_id', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('payroll_payment', 'payroll_id', {
      type: Sequelize.BIGINT.UNSIGNED,
      allowNull: false,
    });
    await queryInterface.changeColumn('payroll_payment', 'creditor_id', {
      type: Sequelize.STRING,
      allowNull: false,
    });
    await queryInterface.changeColumn('payroll_payment', 'object_workflow_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
  },
};
