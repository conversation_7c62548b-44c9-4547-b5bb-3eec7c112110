'use strict';

const TABLE_NAME = 'user_identifier';
const PROVIDER_ID_OIDC_ID_UQ = 'UQ_PROVIDER_ID_OIDC_ID';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint(TABLE_NAME, {
      fields: ['provider_id', 'oidc_id'],
      type: 'unique',
      name: PROVIDER_ID_OIDC_ID_UQ,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(TABLE_NAME, PROVIDER_ID_OIDC_ID_UQ);
  },
};
