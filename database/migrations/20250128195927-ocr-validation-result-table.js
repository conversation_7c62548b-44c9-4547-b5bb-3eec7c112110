'use strict';

const TABLE_NAME = 'ocr_validation_result';

const COLUMNS = Sequelize => ({
  id: {
    type: Sequelize.BIGINT.UNSIGNED,
    primaryKey: true,
    allowNull: false,
    autoIncrement: true,
  },
  ocr_validation_config_id: {
    type: Sequelize.BIGINT.UNSIGNED,
    allowNull: false,
    references: {
      model: 'ocr_validation_config',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'RESTRICT',
  },
  ocr_extraction_result_id: {
    type: Sequelize.BIGINT.UNSIGNED,
    allowNull: false,
    references: {
      model: 'ocr_extraction_result',
      key: 'id',
    },
    onUpdate: 'CASCADE',
    onDelete: 'RESTRICT',
  },
  result: {
    type: Sequelize.JSON,
    allowNull: false,
  },
  runned_at: {
    type: Sequelize.DATE(6),
    allowNull: false,
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(6)'),
  },
});
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable(TABLE_NAME, COLUMNS(Sequelize));
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable(TABLE_NAME);
  },
};
