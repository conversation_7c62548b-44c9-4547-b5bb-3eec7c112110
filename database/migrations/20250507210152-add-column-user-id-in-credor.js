'use strict';

const TABLE = 'credor';
const REFERENCE_TABLE = 'user';
const COLUMN = 'user_id';
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(TABLE, COLUMN, {
      type: Sequelize.BIGINT.UNSIGNED,
      allowNull: true,
      after: 'id_credor',
      references: {
        model: REFERENCE_TABLE,
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(TABLE, COLUMN);
  },
};
