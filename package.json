{"name": "comissionamento-backend", "version": "0.0.1", "description": "", "author": "", "license": "MIT", "scripts": {"setup": "PYTHON=$(which python3.11) npx pnpm@8.15.7 install", "build": "nest build --tsc", "repl": "nest start --debug --watch --entryFile repl", "db:migrate": "sequelize db:migrate", "db:migrate:undo": "sequelize db:migrate:undo", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "tslint -p tsconfig.json -c tslint.json", "prebuild": "<PERSON><PERSON><PERSON> dist", "predb:migrate": "node database/database-creation.js", "prepare": "husky install", "start:debug:local": "node ./node_modules/@nestjs/cli/bin/nest.js start --debug --watch", "start:debug": "nest start --debug --watch", "start:dev": "nest start --watch", "vacuum:start": "nest start --entryFile vacuum/main", "vacuum:start:dev": "nest start --watch --entryFile vacuum/main", "vacuum:start:prod": "node vacuum/main", "start:subscription:debug": "nest start --debug --watch --entryFile closure.backup/main", "start:subscription:prod": "node dist/src/closure.backup/main", "start:outbound-wb:debug": "nest start --debug --watch --entryFile outbound.webhook/main", "start:outbound-wb:prod": "node dist/src/outbound.webhook/main", "start:prod:container": "node main", "start:prod": "node dist/main", "start": "node ./src/main", "test:e2e": "TZ=UTC TEST_TYPE=e2e vitest run", "test:unit": "TZ=UTC TEST_TYPE=unit vitest run", "test": "TZ=UTC vitest run", "postinstall": "node scripts/fixAntlr4Entrypoints.js", "run_sonar": "./circle-sonar.sh"}, "dependencies": {"@aws-sdk/client-lambda": "^3.409.0", "@clerk/backend": "^1.21.4", "@duckdb/node-api": "1.2.1-alpha.16", "@google-cloud/bigquery": "^7.4.0", "@google-cloud/opentelemetry-cloud-trace-exporter": "^2.4.1", "@google-cloud/profiler": "^6.0.3", "@google-cloud/pubsub": "^4.11.0", "@google-cloud/secret-manager": "^5.0.0", "@google-cloud/sql": "^0.15.0", "@google-cloud/storage": "^7.7.0", "@google-cloud/vertexai": "^1.9.2", "@googleapis/drive": "^8.14.0", "@msgpack/msgpack": "^2.7.2", "@nestjs/common": "^11.0.13", "@nestjs/core": "^11.0.13", "@nestjs/microservices": "^11.0.13", "@nestjs/platform-express": "^11.0.13", "@nestjs/schedule": "^5.0.1", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.56.0", "@opentelemetry/instrumentation": "^0.57.2", "@opentelemetry/resources": "^1.30.1", "@opentelemetry/sdk-trace-base": "^1.30.1", "@opentelemetry/sdk-trace-node": "^1.30.1", "@opentelemetry/semantic-conventions": "^1.28.0", "@types/json-schema": "^7.0.15", "@types/msgpack5": "^3.4.6", "@willsoto/nestjs-prometheus": "^6.0.0", "aes-js": "^3.1.2", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "allof-merge": "^0.6.6", "antlr4": "^4.13.1", "aws-sdk": "^2.1213.0", "axios": "^1.8.4", "big.js": "^6.1.1", "body-parser": "^1.20.3", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.5", "express": "^5.1.0", "fast-xml-parser": "^5.0.8", "gaxios": "^6.7.1", "google-auth-library": "^9.11.0", "googleapis-common": "^7.2.0", "graphology": "^0.25.4", "graphology-types": "^0.24.7", "handlebars": "^4.7.8", "json-stable-stringify": "^1.0.1", "jsonpath": "^1.1.1", "jsonwebtoken": "^8.5.1", "jszip": "^3.10.1", "jwk-to-pem": "^2.0.5", "knex": "^3.1.0", "mathjs": "^10.3.0", "microdiff": "^1.3.0", "msgpack5": "^6.0.2", "mysql2": "^2.3.3", "nanomatch": "^1.2.13", "nestjs-cls": "^5.4.2", "nestjs-pino": "^4.4.0", "pino": "^9.2.0", "pino-http": "^8.2.0", "prom-client": "^15.1.1", "reflect-metadata": "^0.1.13", "request-ip": "^2.1.3", "request-promise": "^4.2.6", "rimraf": "^3.0.2", "rxjs": "^7.5.5", "samlify": "^2.8.11", "sequelize": "^5.22.5", "sequelize-typescript": "^1.1.0", "shared-types": "git+https://****************************************:<EMAIL>/splitc-com-br/shared-types.git#v14.23.0", "source-map-support": "^0.5.21", "starkbank": "~2.16.0", "starkbank-ecdsa": "^1.1.5", "svix": "^1.24.0", "undici": "^7.6.0", "uuid": "^3.4.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.0/xlsx-0.20.0.tgz", "yup": "^0.28.5", "zod": "^3.23.8", "zod-validation-error": "^1.5.0"}, "devDependencies": {"@faker-js/faker": "^8.3.1", "@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.3", "@nestjs/testing": "^11.0.13", "@smithy/util-stream": "^1.1.0", "@sonar/scan": "^4.2.8", "@swc/core": "^1.11.13", "@types/big.js": "^6.1.2", "@types/express": "^5.0.1", "@types/jsonpath": "^0.2.4", "@types/jsonwebtoken": "^8.5.8", "@types/multer": "^1.4.7", "@types/node": "^22.13.5", "@types/supertest": "^2.0.11", "@types/uuid": "^3.4.10", "@types/yup": "^0.26.37", "@vitest/coverage-v8": "^3.0.9", "aws-sdk-client-mock": "^4.1.0", "busboy": "^1.6.0", "csv-parser": "^3.0.0", "husky": "^7.0.4", "isomorphic-fetch": "^3.0.0", "jose": "^4.15.7", "jwk-to-pem": "^2.0.5", "msw": "^1.2.1", "pino-pretty": "^8.1.0", "prettier": "^3.5.3", "sequelize-cli": "^6.4.1", "supertest": "^7.1.0", "ts-loader": "^6.2.2", "ts-node": "^8.10.2", "tsconfig-paths": "^3.14.1", "tslint": "^6.1.3", "tslint-config-prettier": "^1.18.0", "typescript": "^4.8.4", "unplugin-swc": "^1.5.1", "vitest": "^3.0.9"}, "packageManager": "pnpm@8.15.8+sha512.d1a029e1a447ad90bc96cd58b0fad486d2993d531856396f7babf2d83eb1823bb83c5a3d0fc18f675b2d10321d49eb161fece36fe8134aa5823ecd215feed392"}