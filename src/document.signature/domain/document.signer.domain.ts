import { Injectable, Logger } from '@nestjs/common';
import {
  BackendErrorCodes,
  BackendSignRequest,
  CreatePacketItemRequest,
  CreatePacketSignerItem,
  CreatePacketsRequest,
  CreatePacketsResponse,
  DeletePacketsRequest,
  GetPacketsQueryParams,
  ListPacketsResponse,
  NotifyPacketsRequest,
  PacketCreditors,
  PacketResponse,
  PacketTagRequest,
  PacketTagTypes,
  PatchPacketRequest,
  PatchPacketResponse,
  AllPacketsRequest,
} from 'shared-types';
import { OfficeRepository } from '../../domain/repository/officeRepository';
import { AuthenticationContextDto } from '../../dto/authenticationContextDto';
import { Creditor_Teams } from '../../models/creditor_teams';
import { Credor } from '../../models/credor';
import { Permission_Profile } from '../../models/permission_profile';
import { Teams } from '../../models/teams';
import { Team_Permission } from '../../models/team_permission';
import { User_Permission } from '../../models/user_permission';
import { CustomException } from '../../utils/error.utils';
import { MapUtils } from '../../utils/map.utils';
import { DocumentSignatureApiService } from '../service/document.signature.api.service';

export type AllowedUsersType = string[] | '*';

@Injectable()
export class DocumentSignerDomain {
  private readonly logger = new Logger(DocumentSignerDomain.name);

  constructor(
    private readonly officeRepository: OfficeRepository,
    private readonly documentSignatureApiService: DocumentSignatureApiService,
  ) {}

  public async getPacket(officeId: number, packetId: number): Promise<PacketResponse> {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR);
    const packet = await this.documentSignatureApiService.getPacket({
      clientId: office.client_id,
      packetId,
    });

    this.logger.debug({ packet }, 'packet found');
    return packet;
  }

  public async patchPacket({
    officeId,
    packetId,
    body,
  }: {
    officeId: number;
    packetId: number;
    body: PatchPacketRequest;
  }): Promise<PatchPacketResponse> {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) {
      throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR);
    }

    const { creditors, enrichedTags } = await this.getPatchCreditorsAndTags(officeId, body.tags);
    const payload = {
      ...body,
      creditors,
      tags: enrichedTags,
    };

    return this.documentSignatureApiService.patchPacket({
      clientId: office.client_id,
      packetId,
      body: payload,
    });
  }

  private async getPatchCreditorsAndTags(officeId: number, tags: PacketTagRequest[]) {
    if (!tags) return {};

    const definedTeams = Boolean(tags.find(tag => tag.tag_type === PacketTagTypes.team));
    const enrichedTags = !definedTeams ? await this.enrichTagsWithTeams(officeId, tags) : tags;
    const creditors = await this.getAllCreditorsFromPacketTags(officeId, enrichedTags);

    return { creditors, enrichedTags };
  }

  private async getAllCreditorsFromPacketTags(
    officeId: number,
    tags: PacketTagRequest[] = [],
  ): Promise<PacketCreditors[]> {
    const teamsSearch: string[] = [];
    const profilesSearch: string[] = [];

    tags.forEach(tag => {
      const { tag_value } = tag;
      if (tag.tag_type === PacketTagTypes.team) {
        teamsSearch.push(tag_value);
        return;
      }

      profilesSearch.push(tag_value);
    });

    const creditorByTeam = await Creditor_Teams.findAll({
      include: [
        {
          model: Teams,
          where: {
            office_id: officeId,
            name: teamsSearch,
          },
        },
        {
          model: Credor,
          attributes: ['id_credor_externo', 'email', 'nome_credor'],
        },
      ],
    });

    const creditorByProfile = await Permission_Profile.findAll({
      where: {
        office_id: officeId,
        id: profilesSearch,
      },
      attributes: ['id'],
      include: [
        {
          model: User_Permission,
          attributes: ['id'],
          include: [
            {
              model: Credor,
              attributes: ['id_credor_externo', 'email', 'nome_credor'],
            },
          ],
        },
        {
          model: Team_Permission,
          attributes: ['id'],
          include: [
            {
              model: Teams,
              attributes: ['id'],
              include: [
                {
                  model: Credor,
                  attributes: ['id_credor_externo', 'email', 'nome_credor'],
                },
              ],
            },
          ],
        },
      ],
    });

    const creditorsMap = new Map<string, PacketCreditors>();
    creditorByProfile.forEach(profile => {
      profile.user_permissions.forEach(({ credor }) => {
        creditorsMap.set(credor.id_credor_externo, {
          email: credor.email,
          creditor_id: credor.id_credor_externo,
          name: credor.nome_credor,
        });
      });

      profile.team_permissions.forEach(teamPermission => {
        teamPermission.teams.creditors.forEach(creditor => {
          creditorsMap.set(creditor.id_credor_externo, {
            email: creditor.email,
            creditor_id: creditor.id_credor_externo,
            name: creditor.nome_credor,
          });
        });
      });
    });

    // If no teams are found, return all creditors
    if (!creditorByTeam.length) return Array.from(creditorsMap.values());

    // If no profiles are found, return all teams creditors
    if (!profilesSearch.length)
      return creditorByTeam.map(({ creditor }) => ({
        email: creditor.email,
        creditor_id: creditor.id_credor_externo,
        name: creditor.nome_credor,
      }));

    // If both teams and profiles are found, return the intersection of both
    const creditorsIntersection: PacketCreditors[] = [];
    creditorByTeam.forEach(({ creditor }) => {
      if (creditorsMap.has(creditor.id_credor_externo)) {
        creditorsIntersection.push({
          email: creditor.email,
          creditor_id: creditor.id_credor_externo,
          name: creditor.nome_credor,
        });
      }
    });
    return creditorsIntersection;
  }

  public async getPackets(
    officeId: number,
    filters: GetPacketsQueryParams,
  ): Promise<ListPacketsResponse> {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR);
    this.logger.debug({ office_id: officeId, filters });
    const packets = await this.documentSignatureApiService.getPackets(office.client_id, filters);

    this.logger.debug({ packets }, 'packets found');
    return packets;
  }

  public async getSigners(officeId: number, packetId: number) {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) {
      throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR);
    }

    const signers = await this.documentSignatureApiService.getSigners({
      clientId: office.client_id,
      packetId,
    });

    return signers;
  }

  public async signPacket({
    officeId,
    packetId,
    user,
    payload,
    ipAddress,
  }: {
    officeId: number;
    packetId: number;
    user: AuthenticationContextDto;
    payload: BackendSignRequest;
    ipAddress: string;
  }): Promise<void> {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) {
      throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR);
    }

    const originalUserId = user.getUserForAudit().credor.id_credor_externo;
    const externalUserId = user.credor.id_credor_externo;

    await this.documentSignatureApiService.signPacket({
      clientId: office.client_id,
      packetId,
      originalUserId,
      externalUserId,
      body: { ...payload, ip_address: ipAddress, timestamp: new Date().toISOString() },
    });
  }

  public async createPackets({
    officeId,
    body,
  }: {
    officeId: number;
    body: CreatePacketsRequest;
  }): Promise<CreatePacketsResponse> {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) {
      throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR);
    }

    const teamsMapByProfile = await this.getTeamsFromProfileTags(
      officeId,
      body.items.map(item => item.tags).flat(),
    );

    const itemsWithCreditors = await Promise.all(
      body.items.map(async (item): Promise<CreatePacketItemRequest> => {
        const allTags = this.generateTagsWithTeams(item.tags, teamsMapByProfile);

        const signers: CreatePacketSignerItem[] = await this.getAllCreditorsFromPacketTags(
          officeId,
          allTags,
        );

        return {
          ...item,
          tags: allTags,
          signers,
        };
      }),
    );
    return this.documentSignatureApiService.createPackets(office.client_id, {
      items: itemsWithCreditors,
    });
  }

  public async deletePackets(
    officeId: number,
    payload: DeletePacketsRequest,
    user: AuthenticationContextDto,
  ): Promise<void> {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) {
      throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR);
    }

    await this.documentSignatureApiService.deletePackets(
      office.client_id,
      payload.packet_ids,
      user,
    );
  }

  public async deleteAllPackets(
    payload: AllPacketsRequest,
    user: AuthenticationContextDto,
  ): Promise<void> {
    await this.documentSignatureApiService.deleteAllPackets(payload, user);
  }

  public async notifyPackets(officeId: number, payload: NotifyPacketsRequest): Promise<void> {
    const office = await this.officeRepository.getByID(officeId);
    await this.documentSignatureApiService.notifyPackets(
      office.client_id,
      payload.packet_ids,
      payload.notification_language,
    );
  }

  public async notifyAllPackets(
    payload: AllPacketsRequest,
    user: AuthenticationContextDto,
  ): Promise<void> {
    await this.documentSignatureApiService.notifyAllPackets(user.escritorio.client_id, payload);
  }

  async enrichTagsWithTeams(
    officeId: number,
    tags: PacketTagRequest[],
  ): Promise<PacketTagRequest[]> {
    const teamsMapByProfile = await this.getTeamsFromProfileTags(officeId, tags);
    return this.generateTagsWithTeams(tags, teamsMapByProfile);
  }

  /**
   * Returning all tags enriched with team tags from profile tags
   */
  private generateTagsWithTeams(
    tags: PacketTagRequest[] = [],
    teamsMapByProfileId: Map<number, Team_Permission[]>,
  ) {
    const tagsMap = new Map<string, PacketTagRequest>();

    tags.forEach(tag => {
      tagsMap.set(`${tag.tag_type}-${tag.tag_value}`, tag);

      // if tag is a profile, we need to get all teams from this profile
      const teams = teamsMapByProfileId.get(Number(tag.tag_value)) || [];
      teams.forEach(({ teams: team }) => {
        const teamTag = {
          tag_type: PacketTagTypes.team,
          tag_value: team.name,
        };

        tagsMap.set(`${teamTag.tag_type}-${teamTag.tag_value}`, teamTag);
      });
    });

    return Array.from(tagsMap.values());
  }

  /**
   * Retrieves teams associated with profile tags for a given office.
   */
  private async getTeamsFromProfileTags(
    officeId: number,
    tags: PacketTagRequest[] = [],
  ): Promise<Map<number, Team_Permission[]>> {
    const profileTags = tags.filter(({ tag_type }) => tag_type === PacketTagTypes.profile);

    const teamsPermission = await Team_Permission.findAll({
      include: [
        {
          model: Permission_Profile,
          where: {
            id: profileTags.map(tag => Number(tag.tag_value)),
            office_id: officeId,
          },
        },
        {
          model: Teams,
        },
      ],
    });

    return MapUtils.groupBy<number, Team_Permission>(teamsPermission, 'permission_profile_id');
  }
}
