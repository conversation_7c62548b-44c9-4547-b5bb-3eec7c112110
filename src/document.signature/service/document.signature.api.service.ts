import { HttpException, HttpStatus, Inject, Injectable, Logger, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import type { Request } from 'express';
import { GaxiosError, GaxiosOptions } from 'gaxios';
import { ClsService } from 'nestjs-cls';
import {
  addGlobalHeaders,
  AllPacketsRequest,
  CreatePacketsRequest,
  CreatePacketsResponse,
  GetPacketsQueryParams,
  GetSignedDocumentsRequest,
  GetSignedDocumentsResponse,
  ListPacketsResponse,
  ORIGINAL_USER_HEADER,
  PacketResponse,
  PatchPacketRequest,
  PatchPacketResponse,
  POLICY_AGREEMENT_MANAGER_HEADER,
  ReconcileSignerRequest,
  SignRequest,
  UsersClaims,
} from 'shared-types';
import { ConfigurationEnv } from '../../config/configuration.env';
import { AuthenticationContextDto } from '../../dto/authenticationContextDto';
import { GoogleApiService } from '../../services/google.api.service';

@Injectable({
  scope: Scope.REQUEST,
})
export class DocumentSignatureApiService {
  protected readonly logger = new Logger(DocumentSignatureApiService.name);
  private readonly apiBase: string;

  constructor(
    private readonly gService: GoogleApiService,
    private readonly config: ConfigurationEnv,
    private readonly cls: ClsService,
    @Inject(REQUEST) private readonly request: Request,
  ) {
    this.apiBase = this.config.documentSignatureUrl;
  }

  async createPackets(
    clientId: string,
    body: CreatePacketsRequest,
  ): Promise<CreatePacketsResponse> {
    try {
      const requestUrl = `${this.apiBase}/offices/${clientId}/packets`;

      const res = await this.httpClient<CreatePacketsResponse>(requestUrl, {
        method: 'POST',
        body,
      });

      return res.data;
    } catch (err) {
      this.logger.error({ ...err }, 'error calling document signature');
      throw new HttpException(
        { msg: 'error communicating to document signature', data: err.response?.data },
        err.code,
      );
    }
  }

  async deletePackets(
    clientId: string,
    packet_ids: number[],
    user: AuthenticationContextDto,
  ): Promise<void> {
    const [isAdmin] = user.findSessionClaim(UsersClaims.POLICY_AGREEMENT_MANAGER);
    const requestUrl = `${this.apiBase}/offices/${clientId}/packets`;
    await this.httpClient(requestUrl, {
      method: 'DELETE',
      body: { packet_ids },
      headers: this.addHeaders({
        loggedUser: user.credor.id_credor_externo,
        isPolicyAgreementManager: isAdmin,
      }),
    });
  }

  async deleteAllPackets(
    payload: AllPacketsRequest,
    user: AuthenticationContextDto,
  ): Promise<void> {
    const [isAdmin] = user.findSessionClaim(UsersClaims.POLICY_AGREEMENT_MANAGER);
    const requestUrl = `${this.apiBase}/offices/${user.escritorio.client_id}/packets/all`;
    await this.httpClient(requestUrl, {
      method: 'DELETE',
      body: payload,
      headers: this.addHeaders({
        loggedUser: user.credor.id_credor_externo,
        isPolicyAgreementManager: isAdmin,
      }),
    });
  }

  async notifyPackets(
    clientId: string,
    packet_ids: number[],
    notification_language: string,
  ): Promise<void> {
    const user = this.request.user;
    const [isAdmin] = user.findSessionClaim(UsersClaims.POLICY_AGREEMENT_MANAGER);
    const requestUrl = `${this.apiBase}/offices/${clientId}/packets/notify`;
    await this.httpClient(requestUrl, {
      method: 'POST',
      body: { packet_ids, notification_language },
      headers: this.addHeaders({
        loggedUser: user.credor.id_credor_externo,
        isPolicyAgreementManager: isAdmin,
      }),
    });
  }

  async notifyAllPackets(clientId: string, payload: AllPacketsRequest): Promise<void> {
    const user = this.request.user;
    const [isAdmin] = user.findSessionClaim(UsersClaims.POLICY_AGREEMENT_MANAGER);
    const requestUrl = `${this.apiBase}/offices/${clientId}/packets/notify/all`;
    await this.httpClient(requestUrl, {
      method: 'POST',
      body: payload,
      headers: this.addHeaders({
        loggedUser: user.credor.id_credor_externo,
        isPolicyAgreementManager: isAdmin,
      }),
    });
  }

  async patchPacket({
    clientId,
    packetId,
    body,
  }: {
    clientId: string;
    packetId: number;
    body: PatchPacketRequest;
  }): Promise<PatchPacketResponse> {
    const user = this.request.user;
    const [isAdmin] = user.findSessionClaim(UsersClaims.POLICY_AGREEMENT_MANAGER);
    const requestUrl = `${this.apiBase}/offices/${clientId}/packets/${packetId}`;
    const res = await this.httpClient<PatchPacketResponse>(requestUrl, {
      method: 'PATCH',
      body,
      headers: this.addHeaders({
        loggedUser: user.credor.id_credor_externo,
        isPolicyAgreementManager: isAdmin,
      }),
    });

    return res.data;
  }

  async getSignedDocuments(
    clientId: string,
    params: GetSignedDocumentsRequest,
  ): Promise<GetSignedDocumentsResponse> {
    try {
      const requestUrl = `${this.apiBase}/offices/${clientId}/documents/signed`;

      const res = await this.httpClient<GetSignedDocumentsResponse>(requestUrl, {
        method: 'GET',
        params,
      });

      return res.data;
    } catch (err) {
      this.logger.error({ ...err }, 'error calling document signature');
      throw new HttpException(
        { msg: 'error communicating to document signature', data: err.response?.data },
        err.code,
      );
    }
  }

  public async getPackets(
    clientId: string,
    params: GetPacketsQueryParams,
  ): Promise<ListPacketsResponse> {
    const user = this.request.user;

    const requestUrl = `${this.apiBase}/offices/${clientId}/packets`;
    const [isAdmin] = user.findSessionClaim(UsersClaims.POLICY_AGREEMENT_MANAGER);
    const res = await this.httpClient<ListPacketsResponse>(requestUrl, {
      method: 'GET',
      params,
      headers: this.addHeaders({
        loggedUser: user.credor.id_credor_externo,
        isPolicyAgreementManager: isAdmin,
      }),
    });

    return res.data;
  }

  public async getPacket({ clientId, packetId }: { clientId: string; packetId: number }) {
    const user = this.request.user;

    const requestUrl = `${this.apiBase}/offices/${clientId}/packets/${packetId}`;
    const [isAdmin] = user.findSessionClaim(UsersClaims.POLICY_AGREEMENT_MANAGER);
    const res = await this.httpClient<PacketResponse>(requestUrl, {
      method: 'GET',
      headers: this.addHeaders({
        loggedUser: user.credor.id_credor_externo,
        isPolicyAgreementManager: isAdmin,
      }),
    });

    return res.data;
  }

  public async httpClient<T>(url: string, { body, headers = {}, method, params }: GaxiosOptions) {
    this.logger.debug('getting auth context started');
    const client = await this.gService.getIdentityTokenClient(this.apiBase);
    this.logger.debug('getting id token client finished');

    this.logger.debug('filling global headers');
    addGlobalHeaders({
      headers,
      data: {
        userLocale: this.request.user.userLocale,
        officeLocale: this.request.user.officeLocale,
        officeName: this.request.user.escritorio.nome_escritorio,
      },
    });

    const originalUser = headers[ORIGINAL_USER_HEADER];
    this.logger.debug({ url, body, originalUser }, 'OUTGOING REQUEST');

    try {
      const res = await client.request<T>({
        url,
        headers: {
          'x-correlation-id': this.cls.get('correlationId') ?? null,
          'Content-Type': 'application/json',
          ...headers,
        },
        method,
        data: method !== 'GET' ? body : undefined,
        params,
      });

      return res;
    } catch (error) {
      let errStatus = HttpStatus.INTERNAL_SERVER_ERROR;
      if (error instanceof GaxiosError) {
        errStatus = error.status ?? HttpStatus.INTERNAL_SERVER_ERROR;
      }

      this.logger.error({ ...error }, 'error calling document signature');
      throw new HttpException(
        { msg: 'error communicating to document-signature-api', data: error.response?.data },
        errStatus,
      );
    }
  }

  private addHeaders({
    loggedUser,
    isPolicyAgreementManager,
  }: {
    loggedUser: string;
    isPolicyAgreementManager?: boolean;
  }) {
    const headers = {};

    headers[ORIGINAL_USER_HEADER] = loggedUser;
    headers[POLICY_AGREEMENT_MANAGER_HEADER] = String(!!isPolicyAgreementManager);
    return headers;
  }

  public async getSigners({ clientId, packetId }: { clientId: string; packetId: number }) {
    const user = this.request.user;
    const [isAdmin] = user.findSessionClaim(UsersClaims.POLICY_AGREEMENT_MANAGER);
    const requestUrl = `${this.apiBase}/offices/${clientId}/packets/${packetId}/signers`;
    const res = await this.httpClient(requestUrl, {
      method: 'GET',
      headers: this.addHeaders({
        loggedUser: user.credor.id_credor_externo,
        isPolicyAgreementManager: isAdmin,
      }),
    });

    return res.data;
  }

  public async signPacket({
    clientId,
    packetId,
    externalUserId,
    originalUserId,
    body,
  }: {
    clientId: string;
    packetId: number;
    externalUserId: string;
    originalUserId: string;
    body: SignRequest;
  }): Promise<void> {
    const requestUrl = `${
      this.apiBase
    }/offices/${clientId}/packets/${packetId}/signers/${encodeURIComponent(externalUserId)}/sign`;
    await this.httpClient(requestUrl, {
      method: 'POST',
      body,
      headers: this.addHeaders({
        loggedUser: originalUserId,
      }), // only the impersonated user can sign, so we need to pass the original user id
    });
  }

  public async reconcileSigners({
    clientId,
    body,
  }: {
    clientId: string;
    body: ReconcileSignerRequest[];
  }): Promise<void> {
    const requestUrl = `${this.apiBase}/offices/${clientId}/packets/signers/reconcile`;
    await this.httpClient(requestUrl, {
      method: 'POST',
      body,
    });
  }
}
