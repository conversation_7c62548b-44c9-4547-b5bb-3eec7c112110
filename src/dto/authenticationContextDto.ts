import { ForbiddenException } from '@nestjs/common';
import { Transaction } from 'sequelize';
import {
  BackendErrorCodes,
  claimMap,
  DEFAULT_LANGUAGE,
  PlanGroupPermission,
  RequesterContext,
  UsersClaims,
} from 'shared-types';
import { z } from 'zod';
import { backofficeList } from '../controller/backoffice/constants';
import { AtributosEscritorio } from '../models/atributosescritorio';
import { Atributos_credor } from '../models/atributos_credor';
import { Creditor_Teams } from '../models/creditor_teams';
import { Credor } from '../models/credor';
import { Escritorio } from '../models/escritorio';
import { Oidc_Provider } from '../models/oidc_provider';
import { Permission_Profile } from '../models/permission_profile';
import { Teams } from '../models/teams';
import { Team_Permission } from '../models/team_permission';
import { User_Permission } from '../models/user_permission';
import { intersectionStr } from '../utils/array.utils';
import { CustomException } from '../utils/error.utils';
import { findParsedClaim, mergePermissions } from '../utils/permission.utils';
import { normalizeCreditor } from '../utils/string.utils';

type ClaimsType = {
  [K in UsersClaims]: [z.infer<(typeof claimMap)[K]['schema']> | null, boolean];
};

type ClaimKey = keyof ClaimsType;

export class AuthenticationContextDto {
  accessToken: string;
  credor: Credor;
  escritorio: Escritorio;
  claims: Atributos_credor[];
  teams: string[];
  isServiceAccount: boolean;
  userLocale: string;
  officeLocale: string;

  private isImpersonating: boolean = false;
  private originalUser: AuthenticationContextDto;

  private visibleCreditorIds: Set<string>;
  private canSeeAnyone: boolean = false;

  private userProfiles: Permission_Profile[];

  private readonly _provider: Oidc_Provider | null;

  private cachedClaims: Map<ClaimKey, ClaimsType[ClaimKey]>;

  private userProfileNamesString: string;

  constructor({
    accessToken,
    creditor,
    office,
    userClaims,
    officeClaims,
    creditorPermProfiles,
    teamsPermProfiles,
    teams,
    provider,
    isServiceAccount,
  }: {
    accessToken: string;
    creditor: Credor;
    office: Escritorio;
    userClaims: Atributos_credor[];
    officeClaims: AtributosEscritorio[];
    creditorPermProfiles: Permission_Profile[];
    teamsPermProfiles: Permission_Profile[];
    teams: string[];
    provider?: Oidc_Provider;
    isServiceAccount?: boolean;
  }) {
    this.accessToken = accessToken;
    this.credor = creditor;
    this.escritorio = office;
    this.teams = teams;
    this.isServiceAccount = isServiceAccount;
    this._provider = provider ?? null;
    this.userProfiles = [...creditorPermProfiles, ...teamsPermProfiles];
    this.cachedClaims = new Map();

    this.officeLocale = this.resolveLanguageFromClaims(officeClaims);

    const finalPermissions = mergePermissions({
      creditorPermProfiles,
      officeClaims,
      teamsPermProfiles,
      userClaims,
    });
    this.claims = finalPermissions.map(
      perm =>
        ({
          credor_id_credor: creditor?.id_credor,
          atributo: perm.atributo,
          valor: perm.valor,
        }) as Atributos_credor,
    );

    this.userLocale = this.resolveLanguageFromClaims(this.claims);
  }

  private resolveLanguageFromClaims(claims: (Atributos_credor | AtributosEscritorio)[]): string {
    const [claimValue, hasClaim] = findParsedClaim(claims, UsersClaims.LANGUAGE_PREFERENCE);

    return hasClaim ? claimValue : DEFAULT_LANGUAGE;
  }

  public findSessionClaim<T extends UsersClaims>(claim: T) {
    if (this.cachedClaims.has(claim)) return this.cachedClaims.get(claim) as ClaimsType[T];

    const searchedClaim = findParsedClaim(this.claims, claim);

    this.cachedClaims.set(claim, searchedClaim);

    return searchedClaim as ClaimsType[T];
  }

  public getProvider(): Oidc_Provider | null {
    return this._provider;
  }

  public authorizeGroupedPlanAccess(
    group_id: number,
    permission: PlanGroupPermission | 'can_unlock_periods',
    type?: 'calc' | 'sources',
  ): boolean {
    const [planGroupPermissions] = findParsedClaim(this.claims, UsersClaims.PLAN_GROUP_PERMISSIONS);
    const [isAdmin] = findParsedClaim(this.claims, UsersClaims.GENERIC_DATA_IMPORT);
    if (isAdmin) return true;
    if (!planGroupPermissions) return false;
    const planGroupPermission = planGroupPermissions.plan_permissions.find(
      p => p.group_id === group_id,
    );

    if (!planGroupPermission) return false;
    if (permission === 'can_unlock_periods') return planGroupPermission.can_unlock_periods;

    return planGroupPermission[type].includes(permission);
  }

  /**
   * @returns a set with all creditors the current user can see, or '*' if no restrictions apply
   */
  private async loadVisibleCreditors(): Promise<void> {
    if (this.visibleCreditorIds) return;

    const [canUpdateClosure] = this.findSessionClaim(UsersClaims.CLOSURE_UPDATE);
    const [canImportGenericData] = this.findSessionClaim(UsersClaims.GENERIC_DATA_IMPORT);
    const [creditorsView] = this.findSessionClaim(UsersClaims.OFFICE_CREDITORS_VIEW);

    const canSeeEveryone =
      !!canUpdateClosure || !!creditorsView || !!canImportGenericData || !!this.isServiceAccount;
    if (canSeeEveryone) {
      this.canSeeAnyone = true;
      return;
    }

    const final = new Set<string>([this.credor.id_credor_externo]);

    const [canSee, successTeamCreditors] = this.findSessionClaim(UsersClaims.TEAM_CREDITORS_VIEW);
    const canSeeTeamsHeIsInto = canSee && successTeamCreditors;

    let teamsCanSee: string[] = canSeeTeamsHeIsInto ? this.teams : []; // all teams can see

    // can see other teams
    const [otherTeams, canSeeOtherTeams] = this.findSessionClaim(UsersClaims.TEAMS_VIEW_PERMISSION);
    if (canSeeOtherTeams && otherTeams?.length) teamsCanSee = [...teamsCanSee, ...otherTeams];

    if (teamsCanSee?.length) {
      const teamsUserCanSee = await Teams.findAll({
        attributes: ['id'],
        where: {
          office_id: this.escritorio.id_escritorio,
          name: teamsCanSee,
        },
      });

      const creditorsInsideThoseTeams = await Creditor_Teams.findAll({
        include: [
          {
            model: Credor,
            attributes: ['id_credor_externo'],
          },
        ],
        where: {
          team_id: teamsUserCanSee.map(t => t.id),
        },
      });

      creditorsInsideThoseTeams.forEach(creditor => final.add(creditor.creditor.id_credor_externo));
    }

    const [otherPeople, canSeeOtherPeople] = this.findSessionClaim(
      UsersClaims.CREDITORS_VIEW_PERMISSION,
    );
    if (canSeeOtherPeople && otherPeople?.length > 0) {
      otherPeople.forEach(externalCreditorId => final.add(normalizeCreditor(externalCreditorId)));
    }

    this.canSeeAnyone = false;
    this.visibleCreditorIds = final;
  }

  public async getVisibleCreditors(): Promise<Set<string> | '*'> {
    await this.loadVisibleCreditors();
    return this.canSeeAnyone ? '*' : this.visibleCreditorIds;
  }

  public hasClaims(claims: UsersClaims[]): boolean {
    return claims.some(claim => {
      const [, hasClaim] = this.findSessionClaim(claim);
      return hasClaim;
    });
  }

  public async canSeeCreditor(externalCreditorId: string): Promise<boolean> {
    await this.loadVisibleCreditors();
    if (this.canSeeAnyone) return true;
    if (!this.visibleCreditorIds) return false;
    return this.visibleCreditorIds.has(normalizeCreditor(externalCreditorId));
  }

  public canSeeCreditorloaded(externalCreditorId: string): boolean {
    if (this.canSeeAnyone) return true;
    if (!this.visibleCreditorIds) return false;
    return this.visibleCreditorIds.has(externalCreditorId);
  }

  public async loadPermissions(): Promise<void> {
    return this.loadVisibleCreditors();
  }

  public async canSeeEverything(): Promise<boolean> {
    await this.loadVisibleCreditors();
    return this.canSeeAnyone;
  }

  /**
   * if this is set, the current instance is considered to be impersonating another user
   * @param original the original user, for audit purposes
   */
  public setOriginalUser(original: AuthenticationContextDto) {
    this.isImpersonating = true;
    this.originalUser = original;
  }

  public getUserForAudit(): AuthenticationContextDto {
    return this.isImpersonating ? this.originalUser : this;
  }

  public getUserProfiles(): Permission_Profile[] {
    return this.userProfiles ?? [];
  }

  public getUserProfileNames(): string {
    if (this.userProfileNamesString) return this.userProfileNamesString;

    this.userProfileNamesString = this.getUserProfiles()
      .map(p => p.name)
      .join(',');

    return this.userProfileNamesString;
  }

  static BuildFromOffice(office: Escritorio, claims: AtributosEscritorio[]) {
    return new this({
      office,
      officeClaims: claims,
      accessToken: '',
      creditor: null,
      creditorPermProfiles: [],
      teams: [],
      teamsPermProfiles: [],
      userClaims: [],
    });
  }

  static async BuildFromCreditor(
    creditor: Credor,
    accessToken: string,
    provider?: Oidc_Provider,
    transaction?: Transaction,
  ) {
    const [teams, userClaims, officeClaims] = await Promise.all([
      Teams.findAll({
        attributes: ['id', 'name'],
        where: { office_id: creditor.escritorio_id_escritorio },
        include: [{ attributes: [], model: Credor, where: { id_credor: creditor.id_credor } }],
        transaction,
      }),
      Atributos_credor.findAll({
        where: { credor_id_credor: creditor.id_credor },
        transaction,
      }),
      AtributosEscritorio.findAll({
        where: { escritorio_id_escritorio: creditor.escritorio_id_escritorio },
        transaction,
      }),
    ]);

    let office = creditor.escritorio;
    if (!office) {
      office = await Escritorio.findByPk(creditor.escritorio_id_escritorio, { transaction });
    }

    const { creditorPermProfiles, teamsPermProfiles } = await this.getPermissionProfiles(
      office.id_escritorio,
      creditor.id_credor,
      teams.map(t => t.id),
      transaction,
    );

    const isServiceAccount = !!provider?.checkIfIsServiceAccount(accessToken);

    return new AuthenticationContextDto({
      accessToken,
      creditor,
      office,
      userClaims,
      officeClaims,
      creditorPermProfiles,
      teamsPermProfiles,
      teams: teams.map(t => t.name),
      provider,
      isServiceAccount,
    });
  }

  static HasBackofficeEmailPattern(email: string) {
    const splitedEmail = email?.split('@');
    if (!splitedEmail || splitedEmail.length !== 2) return false;

    return splitedEmail[1] === 'splitc.com.br' || backofficeList.has(email);
  }

  private static async getPermissionProfiles(
    officeId: number,
    creditorId: number,
    teamIds: number[],
    transaction?: Transaction,
  ) {
    const creditorPermProfiles = await User_Permission.findAll({
      attributes: ['permission_profile_id'],
      where: { creditor_id: creditorId },
      transaction,
    }).then(result => {
      if (!result?.length) return [];
      return Permission_Profile.findAll({
        where: { id: result.map(p => p.permission_profile_id), office_id: officeId },
        include: [Permission_Profile],
        transaction,
      });
    });

    let teamsPermProfiles: Permission_Profile[] = [];
    if (teamIds?.length >= 0) {
      teamsPermProfiles = await Team_Permission.findAll({
        attributes: ['permission_profile_id'],
        where: { team_id: teamIds },
        transaction,
      }).then(result => {
        if (!result?.length) return [];
        return Permission_Profile.findAll({
          where: { id: result.map(p => p.permission_profile_id), office_id: officeId },
          include: [Permission_Profile],
          transaction,
        });
      });
    }

    return { creditorPermProfiles, teamsPermProfiles };
  }

  public getAllowedTeamsToInsert(
    requestedTeams: string[] = [],
    currentTeams: string[] = [],
  ): string[] {
    const canSeeInsertAll = this.hasClaims([UsersClaims.CLOSURE_UPDATE, UsersClaims.USER_CREATE]);
    if (canSeeInsertAll) return requestedTeams;

    const [userManagement, successUserManagement] = this.findSessionClaim(
      UsersClaims.USER_MANAGEMENT,
    );

    if (!successUserManagement || !userManagement.teams?.length) return currentTeams;

    const teams = requestedTeams?.filter(reqTeam => userManagement.teams.includes(reqTeam)) ?? [];
    const currentTeamsExcludeClaimTeams =
      currentTeams?.filter(team => !userManagement.teams.includes(team)) ?? [];

    return currentTeams?.length ? [...currentTeamsExcludeClaimTeams, ...teams] : teams;
  }

  public async getAllowedProfilesToInsert(
    requestedProfileIds: number[] = [],
    currentProfiles: number[] = [],
  ): Promise<number[]> {
    const isAdmin = this.hasClaims([UsersClaims.CLOSURE_UPDATE]);
    if (isAdmin) return requestedProfileIds;

    const [userCreate] = this.findSessionClaim(UsersClaims.USER_CREATE);
    if (userCreate) {
      const [managePermissionProfiles] = this.findSessionClaim(
        UsersClaims.MANAGE_PERMISSION_PROFILES,
      );

      return managePermissionProfiles ? requestedProfileIds : currentProfiles;
    }

    const [userManagement, successUserManagement] = this.findSessionClaim(
      UsersClaims.USER_MANAGEMENT,
    );
    if (!successUserManagement || !userManagement.profiles?.length) return currentProfiles;

    const claimProfiles = await Permission_Profile.findAll({
      where: {
        office_id: this.escritorio.id_escritorio,
        name: userManagement.profiles,
      },
    });

    const claimProfileIds = claimProfiles.map(p => p.id);

    const profiles =
      requestedProfileIds?.filter(reqProfileId => claimProfileIds.includes(reqProfileId)) ?? [];

    const currentProfilesExcludeClaimTeams =
      currentProfiles?.filter(profile => !claimProfileIds.includes(profile)) ?? [];
    return currentProfiles ? [...currentProfilesExcludeClaimTeams, ...profiles] : profiles;
  }

  public async canSeeAndManageUser(opts: {
    validateCanCreate?: boolean;
    validateCanDelete?: boolean;
    userId?: string;
  }): Promise<boolean> {
    const [userManagementClaim] = this.findSessionClaim(UsersClaims.USER_MANAGEMENT);
    const isAdmin = this.hasClaims([UsersClaims.CLOSURE_UPDATE, UsersClaims.USER_CREATE]);
    if (isAdmin) return;

    if (opts.validateCanDelete && !userManagementClaim?.allow_delete) {
      throw new CustomException(BackendErrorCodes.USER_MANAGEMENT_NOT_ALLOWED_DELETE, {
        claim: userManagementClaim,
      });
    }

    if (opts.validateCanCreate && !userManagementClaim?.allow_create) {
      throw new CustomException(BackendErrorCodes.USER_MANAGEMENT_NOT_ALLOWED_CREATE, {
        claim: userManagementClaim,
      });
    }

    if (!opts.userId) return;

    const canSeeViewUser = await this.canSeeCreditor(opts.userId);
    if (canSeeViewUser) return;

    throw new CustomException(BackendErrorCodes.CREDITOR_NOT_FOUND, {
      external_creditor_id: opts.userId,
    });
  }

  public async userCanRemove(externalCreditorId: string): Promise<void> {
    const shouldPreventUserDeletion = this.hasClaims([UsersClaims.PREVENT_USER_DELETION]);
    if (shouldPreventUserDeletion) {
      throw new ForbiddenException('User does not have permission to delete users');
    }
    await this.canSeeAndManageUser({ validateCanDelete: true, userId: externalCreditorId });
  }

  public getCreditorsOnCreditorsViewPermission(): string[] {
    const [creditorsViewPermission, hasClaim] = this.findSessionClaim(
      UsersClaims.CREDITORS_VIEW_PERMISSION,
    );
    if (!hasClaim) return [];
    return creditorsViewPermission ?? [];
  }

  public addCreditorInCreditorsViewPermission(externalCreditorId: string): string[] {
    const creditors = this.getCreditorsOnCreditorsViewPermission();
    const setCreditors = new Set([...creditors, externalCreditorId]);
    return Array.from(setCreditors.values());
  }

  public isBackoffice(): boolean {
    const provider = this.getProvider();
    if (!provider || !provider.default) return false;
    return AuthenticationContextDto.HasBackofficeEmailPattern(this.credor.email);
  }

  public makePaymentRequesterContext(receiver: string): RequesterContext {
    const [requesterIsManager, hasClaim] = this.findSessionClaim(
      UsersClaims.PAYROLL_STATUS_MANAGER,
    );
    const isStatusManager = hasClaim && requesterIsManager;

    return {
      can_see_receiver: this.canSeeCreditorloaded(receiver),
      is_payroll_status_manager: isStatusManager,
      is_receiver: this.credor?.id_credor_externo === receiver,
      profiles: this.getUserProfileNames(),
      user_id: this.credor?.id_credor_externo,
    };
  }

  public async filterCreditorsThatUserCanSee(creditors?: string[]) {
    const normalizedCreditors = creditors?.map(normalizeCreditor);
    const visibleCreditors = await this.getVisibleCreditors();

    if (visibleCreditors === '*') return normalizedCreditors;

    const allowedToSeeArr = Array.from(visibleCreditors.values());
    return intersectionStr([allowedToSeeArr, normalizedCreditors ?? allowedToSeeArr]);
  }
}
