import { Document_tag } from '../models/document_tag';

export class DocumentsValidationsDto {
  private successful_documents: number;
  private failure_documents: number;

  constructor(document_tags: Document_tag[] = []) {
    this.successful_documents = 0;
    this.failure_documents = 0;
    for (const { document } of document_tags) {
      if (!document.file || !document.file?.ocr_extraction_results.length) continue;

      const allValidationResults = document.file?.ocr_extraction_results
        .flatMap(({ ocr_validation_results }) => ocr_validation_results)
        .flatMap(({ result }) => result);

      const noHasValidationResult = allValidationResults.length === 0;
      if (noHasValidationResult) continue;

      const isValid = allValidationResults?.every(({ success }) => success);
      this.successful_documents += isValid ? 1 : 0;
      this.failure_documents += isValid ? 0 : 1;
    }
  }

  get documents_validations() {
    const isValid = this.successful_documents + this.failure_documents > 0;
    return isValid
      ? {
          successful_documents: this.successful_documents,
          failure_documents: this.failure_documents,
        }
      : undefined;
  }
}
