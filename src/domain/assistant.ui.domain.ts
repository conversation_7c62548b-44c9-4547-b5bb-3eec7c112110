import { AuthenticationContextDto } from '../dto/authenticationContextDto';
import { SheetDbApiService } from '../services/sheet.db.api.service';
import { ForbiddenException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { AiChatTokenResponse, ChatTokenRequestSchemaDto } from 'shared-types';
import { ConfigurationEnv } from '../config/configuration.env';
import { tryParseJson } from '../utils/fetch.utils';

interface GenerateWorkspaceIdProps {
  dto: ChatTokenRequestSchemaDto;
  officeId: number;
  credorId: number;
}

@Injectable()
export class AssistantUiDomain {
  private readonly logger = new Logger(AssistantUiDomain.name);

  constructor(
    private readonly config: ConfigurationEnv,
    private readonly sheetDbApiService: SheetDbApiService,
  ) {}

  async getToken(
    dto: ChatTokenRequestSchemaDto,
    authCtx: AuthenticationContextDto,
  ): Promise<AiChatTokenResponse> {
    if (dto.type === 'plan') {
      const plan = await this.sheetDbApiService.getCommissionPlan(
        authCtx.escritorio.client_id,
        dto.planId,
      );

      if (!plan) {
        this.logger.error({ planId: dto.planId }, 'plan not found');
        throw new NotFoundException();
      }
    }

    const workspaceId = this.generateWorkspaceId({
      officeId: authCtx.escritorio.id_escritorio,
      credorId: authCtx.credor.id_credor,
      dto,
    });

    const res = await fetch(`${this.config.assistantUiBaseUrl}/v1/auth/tokens`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.config.assistantUiApiKey}`,
        'Aui-User-Id': workspaceId,
        'Aui-Workspace-Id': workspaceId,
      },
    });

    const result = await tryParseJson<{ token: string }>(res);

    if (!res.ok || result.success === false || !result.data.token) {
      this.logger.error(result, 'Error during assistant ui token generation');
      throw new ForbiddenException();
    }

    return {
      token: result.data.token,
    };
  }

  /**
   * Generate a workspace id for assistant ui.
   * The assistant ui API requires the workspace ID to be alpha-numeric. So, some characters are not allowed. always verify before changing this method
   * @example
   * ✅ `PLAN-CHAT_${escritorioId}_${credorId}`
   * ❌ `PLAN#CHAT#${escritorioId}#${credorId}`
   */
  private generateWorkspaceId({ dto, officeId, credorId }: GenerateWorkspaceIdProps): string {
    const prefix = 'PLAN-CHAT';

    if (dto.type === 'plan') {
      return `${prefix}_${officeId}_${dto.planId}_${credorId}`;
    }

    return `${prefix}_${officeId}_${credorId}`;
  }
}
