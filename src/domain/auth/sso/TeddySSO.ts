import { Atributos_credor } from '../../../models/atributos_credor';
import { Credor } from '../../../models/credor';
import {
  BadRequestException,
  ForbiddenException,
  InternalServerErrorException,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { SSOConfig } from '.';
import { ConfigurationEnv } from '../../../config/configuration.env';
import { JwtPart, JwtUtils } from '../../../utils/jwt.utils';
import { UserInfoSSO } from './UserInfoSSO';
import { UsersClaims } from 'shared-types';
import { SSOLoginRequestDTO } from '../../../dto/login.request.dto';

type TeddyResponse = {
  usuario: string;
  bitrix_id: string;
  whitelabel?: {
    theme: {
      palette: { primary: { main: string }; secondary: { main: string }; type: 'light' | 'dark' };
    };
    logo_url?: string | undefined | null;
  };
};

type TeddyAdditionalData = {
  override_domain: string;
  tenant_id: number | undefined;
};

export class TeddySSO extends UserInfoSSO {
  private readonly log = new Logger(TeddySSO.name);
  private response: TeddyResponse;
  constructor(cfg: SSOConfig, appConfig: ConfigurationEnv, req: SSOLoginRequestDTO) {
    super(cfg, appConfig, req);
  }

  protected async requestUserInfo(token: string): Promise<Record<string, string>> {
    if (this.config.type !== 'teddy' || !this.config.config.bearer) {
      this.log.error({ ...this.config }, 'invalid sso config for teddy');
      throw new InternalServerErrorException('invalid SSO config');
    }

    let overrideDomain: string = undefined;
    let tenantId: number | undefined;
    if (this.request?.additional_data) {
      const data = this.readAdditionalData();
      overrideDomain = data?.override_domain;
      tenantId = data?.tenant_id;
    }

    if (overrideDomain) {
      overrideDomain = overrideDomain.replace('https://', '');
      const isWhitelisted = this.config.config.domain_whitelist.some(w =>
        w.includes(overrideDomain),
      );
      if (!isWhitelisted) {
        this.log.warn(
          {
            domain: overrideDomain,
            allowed: this.config.config.domain_whitelist,
          },
          'teddy sso: tried to login with unauthorized domain',
        );
        throw new ForbiddenException();
      }
    }

    const finalUrl = `https://${overrideDomain ?? this.config.config.endpoint}/info-user`;

    const jwt = JwtUtils.readJwtPart(token, JwtPart.BODY);
    const { email } = jwt;

    if (!email) throw new Error(`something wrong with teddy JWT. ${jwt}`);

    let headers: HeadersInit = {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
    if (tenantId) {
      headers['x-tenant-id'] = String(tenantId);
      headers.origin = overrideDomain ?? this.config.config.endpoint;
    }

    this.log.debug(
      {
        headers: { ...headers, Authorization: undefined },
        user: {
          email,
        },
        endpoint: finalUrl,
      },
      `OUTGOING REQUEST - Teddy SSO`,
    );
    const res = await fetch(finalUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({}),
    });
    if (!res.ok) {
      this.log.error({ statusCode: res.status }, `INCOMING RESPONSE - Teddy SSO`);
      throw new UnauthorizedException(`couldnt get userinfo`);
    }

    const responseBody: TeddyResponse = await res.json();
    if (!responseBody.usuario) {
      this.log.error(
        {
          body: responseBody,
        },
        `INCOMING RESPONSE - Teddy SSO`,
      );
      throw new UnauthorizedException(`partner didnt recognize user`);
    }

    this.response = responseBody;
    this.log.debug(responseBody, 'INCOMING RESPONSE - Teddy SSO');

    return { email, internalId: responseBody.bitrix_id ? String(responseBody.bitrix_id) : null };
  }

  public async postLogin(user: Credor) {
    if (!this.response?.whitelabel) return;
    if (this.response.whitelabel.logo_url) {
      this.log.debug({ url: this.response.whitelabel.logo_url }, 'teddy sso - updating logo');
      await Atributos_credor.upsert({
        credor_id_credor: user.id_credor,
        atributo: UsersClaims.OFFICE_LOGO_URL,
        valor: this.response.whitelabel.logo_url,
      });
    }

    if (this.response.whitelabel.theme) {
      await Atributos_credor.upsert({
        credor_id_credor: user.id_credor,
        atributo: UsersClaims.THEME_CONFIG,
        valor: JSON.stringify(this.response.whitelabel.theme),
      });
    }
  }

  private readAdditionalData(): TeddyAdditionalData | undefined {
    try {
      if (!this.request.additional_data) return undefined;
      const teddyAdditionalDataBuf = Buffer.from(this.request.additional_data, 'base64');
      const teddyAdditionalDataString = teddyAdditionalDataBuf.toString('utf-8');
      const teddyAdditionalDataJson: TeddyAdditionalData = JSON.parse(teddyAdditionalDataString);
      return teddyAdditionalDataJson;
    } catch (err) {
      this.log.error(
        {
          additional_data: this.request.additional_data,
        },
        'invalid teddy sso. additional data must be json',
      );
      throw new BadRequestException();
    }
  }
}
