import { InternalServerErrorException, Logger, UnauthorizedException } from '@nestjs/common';
import { SSOBase, TokensResponse } from './SSOBase';
import { ConfigurationEnv } from '../../../config/configuration.env';
import { JwtUtils, JwtPart } from '../../../utils/jwt.utils';
import { PublicKeySSOConfig, SSOConfig } from '.';
import jsonwebtoken from 'jsonwebtoken';

export class PublicKeySSO extends SSOBase {
  private readonly log = new Logger(PublicKeySSO.name);
  constructor(cfg: SSOConfig, appConfig: ConfigurationEnv) {
    super(cfg, appConfig, null);
  }

  async emitTokens(token: string): Promise<TokensResponse> {
    if (this.config.type !== 'public_key') {
      this.log.error('config mismatch. tried to do JWKS sso with another config type');
      throw new InternalServerErrorException();
    }

    if (!this.config.config.certificate_pem) {
      this.log.error(
        'invalid configuration. expecting "certificate_pem" to have a valid public cert',
      );
      throw new InternalServerErrorException();
    }

    const parsed = Buffer.from(this.config.config.certificate_pem, 'base64').toString('utf-8');
    this.log.debug({ pem: this.config.config.certificate_pem, parsed }, 'validation with cert');

    try {
      jsonwebtoken.verify(token, parsed);
    } catch (err) {
      this.log.error(err, 'invalid token');
      throw new UnauthorizedException();
    }

    const userInfo = this.extractUserinfoFromJwt(token, this.config);
    this.log.debug(userInfo, 'extracted user info');

    if (!userInfo.email) {
      this.log.error('need to know user email, but couldnt find on token');
      throw new InternalServerErrorException();
    }

    return {
      internalId: userInfo.creditor_id,
      internalName: userInfo.name,
      email: userInfo.email,
    };
  }

  private extractUserinfoFromJwt(
    jwt: string,
    cfg: PublicKeySSOConfig,
  ): PublicKeySSOConfig['config']['userInfoExtractor'] {
    const jwtClaims = JwtUtils.readJwtPart(jwt, JwtPart.BODY);
    const userInfo: PublicKeySSOConfig['config']['userInfoExtractor'] = {};

    if (cfg.config.userInfoExtractor.creditor_id) {
      userInfo.creditor_id = jwtClaims[cfg.config.userInfoExtractor.creditor_id];
    }

    if (cfg.config.userInfoExtractor.email) {
      userInfo.email = jwtClaims[cfg.config.userInfoExtractor.email];
    }

    if (cfg.config.userInfoExtractor.name) {
      userInfo.name = jwtClaims[cfg.config.userInfoExtractor.name];
    }

    return userInfo;
  }
}
