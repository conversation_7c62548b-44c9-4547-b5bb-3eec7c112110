import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { Transaction } from 'sequelize';
import { GuardErrors } from '../../decorators/guards/types/error.codes';
import { Permission_Profile } from '../../models/permission_profile';
import { User_Permission } from '../../models/user_permission';
import { sequelizeConnection } from '../../database.provider';
import { Teams } from '../../models/teams';
import { Creditor_Teams } from '../../models/creditor_teams';
import { OrgContext } from '../../auth.contexts/tenant/org.context';

@Injectable()
export class OrganizationDomain {
  private readonly logger = new Logger(OrganizationDomain.name);

  constructor() {}

  public async loginIntoTenant(userOrg: OrgContext): Promise<void> {
    /* skip when user isnt admin and create_use is false in org.config */

    const creditor = userOrg.getSelectedCreditor();
    if (!creditor) {
      this.logger.warn(
        'logged-in not identify creditor, invalid tenant id or no has creditor in tenant',
      );
      throw new UnauthorizedException(GuardErrors.USER_HASNT_ALLOWED_TENANT);
    }
    const credorAlreadyExists = creditor.id_credor;
    if (credorAlreadyExists || !userOrg.shouldCreateCreditor) return;

    creditor.ativo = true;
    await sequelizeConnection.transaction(async (transaction: Transaction) => {
      const newCreditor = await creditor.save({ transaction });
      this.logger.debug(
        {
          creditorId: newCreditor.id_credor,
          initialTeams: userOrg.initialTeams,
          initialProfiles: userOrg.initialProfiles,
        },
        'created new creditor',
      );

      const [teams, profiles] = await Promise.all([
        Teams.findAll({
          where: {
            office_id: newCreditor.escritorio_id_escritorio,
            name: userOrg.initialTeams ?? [],
          },
          transaction,
        }),
        Permission_Profile.findAll({
          where: {
            office_id: newCreditor.escritorio_id_escritorio,
            name: userOrg.initialProfiles ?? [],
          },
          transaction,
        }),
      ]);

      /* adds existing teams and profiles to the new creditor */
      const creditorTeamsToCreate = teams.map(team => ({
        creditor_id: newCreditor.id_credor,
        team_id: team.id,
      }));
      const creditorPermissionsToCreate = profiles.map(profile => ({
        permission_profile_id: profile.id,
        creditor_id: newCreditor.id_credor,
      }));

      const bulkCreatePromises = [];
      if (creditorTeamsToCreate.length)
        bulkCreatePromises.push(Creditor_Teams.bulkCreate(creditorTeamsToCreate, { transaction }));

      if (creditorPermissionsToCreate.length)
        bulkCreatePromises.push(
          User_Permission.bulkCreate(creditorPermissionsToCreate, { transaction }),
        );

      if (!bulkCreatePromises.length) return;

      await Promise.all(bulkCreatePromises);
    });
  }
}
