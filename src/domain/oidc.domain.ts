import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { col, fn, Op, Transaction, where } from 'sequelize';
import { UsersClaims } from 'shared-types';
import { Credor } from '../models/credor';
import { Escritorio } from '../models/escritorio';
import { Oidc_Provider, UserInfo } from '../models/oidc_provider';
import { AtributosEscritorioRepository } from './repository/atributosEscritorioRepository';
import { CreditorRepository } from './repository/creditorRepository';
import { OidcProviderRepository } from './repository/oidc.provider.repository';
import { normalizeCreditor } from '../utils/string.utils';
import { UserIdentifierRepository } from './repository/user.identifier.repository';
import { User_Identifier } from '../models/user_identifier';
import { OrganizationRepository } from './repository/organization.repository';
interface CreateCreditorParams extends Omit<UserInfo, 'office_id'> {
  office_id: number;
  oidc_id: number;
}

@Injectable()
export class OidcProviderDomain {
  private readonly logger = new Logger(OidcProviderDomain.name);
  constructor(
    private readonly oidcProviderRepository: OidcProviderRepository,
    private readonly officeClaimsRepository: AtributosEscritorioRepository,
    private readonly credorRepository: CreditorRepository,
    private readonly userIdentifier: UserIdentifierRepository,
    private readonly organizationRepository: OrganizationRepository,
  ) {}

  public async updateAllProviders() {
    const allOidcProviders = await Oidc_Provider.findAll({
      where: { discovery_url: { [Op.notIn]: [''] } },
    });

    const promises = allOidcProviders.map(oidc => oidc.updateOidcMetadata());
    await Promise.all(promises);
  }

  private async getOidcProvidersOnOrganization(email: string) {
    const organization = await this.organizationRepository.getByEmail(email);
    if (!organization) return [];

    const providers = await this.oidcProviderRepository.findAllOidcProviders({
      org_id: organization.id,
    });

    return providers;
  }

  async consultOrganizationOidcProvidersByEmail(email: string): Promise<Oidc_Provider[]> {
    const organizationProviders = await this.getOidcProvidersOnOrganization(email);
    return organizationProviders;
  }

  async consultOidcProvidersByEmail(email: string): Promise<Oidc_Provider[]> {
    const creditorsByEmail = await Credor.findAll({
      where: { email, deleted_at: null, ativo: true },
    });
    const defaultProviders = await this.getDefaultProviders();
    if (!creditorsByEmail?.length) {
      return defaultProviders;
    }

    const officeIds = creditorsByEmail.map(c => c.escritorio_id_escritorio);

    const customProviders = await Oidc_Provider.findAll({
      where: {
        [Op.and]: [
          where(fn('JSON_UNQUOTE', fn('JSON_EXTRACT', col('opts'), '$.office_id')), {
            [Op.in]: officeIds,
          }),
          { default: false },
        ],
      },
      attributes: ['id', 'pretty_name', 'discovery_data', 'client_id', 'opts', 'slug'],
    });

    return [...customProviders, ...defaultProviders];
  }

  filterOidcByEmailClaim(providers: Oidc_Provider[], emailConsulted: string) {
    const providersFiltered = providers.filter(provider =>
      provider.checkIfEmailIsAllowed(emailConsulted),
    );

    return providersFiltered;
  }

  async getDefaultProviders() {
    const defaultProviders = await this.oidcProviderRepository.getDefaultOidcProviders({
      attributes: ['id', 'pretty_name', 'discovery_data', 'client_id', 'opts', 'slug'],
    });

    return defaultProviders;
  }

  public async getCreditorByOidc(
    userInfo: UserInfo,
    oidc: Oidc_Provider,
    opts?: { transaction: Transaction },
  ): Promise<Credor> {
    if (!oidc.opts?.identifier_type) {
      throw new UnauthorizedException();
    }

    let userIdentifier: User_Identifier;
    if (oidc.opts.identifier_type === 'provider_id') {
      userIdentifier = await User_Identifier.findOne({
        where: {
          oidc_id: oidc.id,
          provider_id: userInfo.provider_id,
        },
        transaction: opts?.transaction,
      });
    }

    const officeId = oidc.opts?.office_id;
    return Credor.identifyCreditorFromUserInfo(userInfo, oidc.opts.identifier_type, {
      officeId,
      userIdentifier,
      transaction: opts?.transaction,
    });
  }

  public async getOrCreateCreditor(creditorInfo: UserInfo, oidc: Oidc_Provider): Promise<Credor> {
    const creditor = await this.getCreditorByOidc(creditorInfo, oidc);

    const shouldCreateCreditor = oidc.opts?.office_id && oidc.opts.create_user && !creditor;
    if (shouldCreateCreditor) {
      return this.createCreditor({
        ...creditorInfo,
        office_id: oidc.opts.office_id,
        oidc_id: oidc.id,
      });
    }

    const shouldRemoveDeletedAt = creditor?.deleted_at && oidc.opts?.create_user;
    if (shouldRemoveDeletedAt) {
      creditor.deleted_at = null;
      creditor.ativo = true;
      await creditor.save();
      return creditor;
    }

    return creditor;
  }

  private async createCreditor(creditorData: CreateCreditorParams): Promise<Credor> {
    const normalizedCreditorId = normalizeCreditor(creditorData.creditor_id);
    const normalizedEmail = Credor.normalizeEmail(creditorData.email);

    let creditor = await Credor.findOne({
      where: {
        escritorio_id_escritorio: creditorData.office_id,
        id_credor_externo: normalizedCreditorId,
      },
    });

    if (!creditor) {
      const creditorWithSameEmail = await Credor.findOne({
        where: {
          email: normalizedEmail,
        },
      });

      creditor = await Credor.create({
        escritorio_id_escritorio: creditorData.office_id,
        id_credor_externo: normalizedCreditorId,
        ativo: true,
        email: creditorWithSameEmail ? null : normalizedEmail,
        nome_credor: creditorData.name,
      });
    }

    await Promise.all([
      this.userIdentifier.upsert({
        creditorId: creditor.id_credor,
        oidcId: creditorData.oidc_id,
        providerId: creditorData.provider_id,
      }),
      creditor.reload({
        include: [{ model: Escritorio }],
      }),
    ]);

    return creditor;
  }
}
