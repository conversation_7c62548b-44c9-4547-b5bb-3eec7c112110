import { SplitcModel } from '../../../../types/extended-sequelize';
import { CustomException } from '../../../../utils/error.utils';
import { Transaction } from 'sequelize';
import { Object_Workflow } from '../../../../models/object_workflow';
import { ObjectWorkflowEventsReasons, ObjectWorkflowStatusInputs } from 'shared-types';
import { Payroll_Config } from '../../../../models/payroll_config';
import { AuthenticationContextDto } from '../../../../dto/authenticationContextDto';

export interface InitWorkflowParams {
  models: InitWorkflowModel[];
  reason: ObjectWorkflowEventsReasons;
  trx: Transaction;
}

export interface InitWorkflowModel {
  model: SplitcModel;
  /**
   * this flag is used to determine if the model has changed
   * workflow service will use this flag to determine if to return object workflow to initial status
   * and create the corresponding event
   */
  modelHasChanged?: boolean;
}

export interface BulkStatusTransitionResult {
  success: SuccessfulTransition[];
  failures: FailedTransition[];
}

export interface SuccessfulTransition {
  object_workflow: Object_Workflow;
  new_status: string;
}

export interface FailedTransition {
  object_workflow: Object_Workflow;
  error: CustomException;
}

export interface ObjectWorkflowWithTargetStatus {
  obj_workflow: Object_Workflow;
  new_status: string;
  hook_inputs?: ObjectWorkflowStatusInputs[];
}

export interface StatusWorkflowFactoryOpts {
  workflow: Payroll_Config;
  requester: AuthenticationContextDto;
  officeId: number;
}

export interface MissingStatuses {
  code: string;
  name: string;
}
