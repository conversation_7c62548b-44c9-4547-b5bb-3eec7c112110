import {
  AskForInputHookConfig,
  DispatchWebhookSyncParams,
  DispatchWebhookSyncResponse,
} from 'shared-types';
import { PayrollHookBase } from '../payroll.hook.base';
import { PayrollHookFactoryOpts } from '../payroll.hook.interface';
import { JsonSchemaUtils } from '../../../../utils/json.schema.utils';

export class AskForInputHook extends PayrollHookBase<AskForInputHookConfig> {
  constructor(opts: PayrollHookFactoryOpts) {
    super(opts);
  }

  async dispatchSync(
    params: DispatchWebhookSyncParams<AskForInputHookConfig>,
  ): Promise<DispatchWebhookSyncResponse> {
    return JsonSchemaUtils.validateInputsSchema(params.hook_inputs, params.hook.input_schema);
  }
}
