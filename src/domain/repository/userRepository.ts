import crypto from 'node:crypto';
import AWSService from '../../services/aws.sdk.service';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigurationEnv } from '../../config/configuration.env';
import {
  AdminDeleteUserAttributesResponse,
  AdminGetUserResponse,
  AdminUpdateUserAttributesResponse,
} from 'aws-sdk/clients/cognitoidentityserviceprovider';
import { DefaultException } from '../../exception/default.exception.handler';
import { Credor } from '../../models/credor';
import { User_Identifier } from '../../models/user_identifier';

import { Oidc_Provider } from '../../models/oidc_provider';
import { Transaction } from 'sequelize';
import { SECRET_VAULT, SecretVault } from '../../services/google/secret-manager.service';
import { CustomException } from '../../utils/error.utils';
import { BackendErrorCodes } from 'shared-types';
import CognitoIdentityServiceProvider = require('aws-sdk/clients/cognitoidentityserviceprovider');
import { generatePassword } from '../../utils/user.utils';
import e = require('express');
import { UserIdentifierRepository } from './user.identifier.repository';
export const cognitoAttrs = {
  MAX_LOGIN_ATTEMPTS: 'custom:max_attempts',
  CURRENT_LOGIN_ATTEMPTS: 'custom:current_attempts',
  PASSWORD_EXPIRATION_DATE: 'custom:pw_expiration_date',
  PASSWORD_EXPIRATION_DAYS: 'custom:pw_expiration_days',
};
export const AllCognitoAttrs = new Set(Object.values(cognitoAttrs));
export const CognitoDateFormat = 'yyyy-MM-dd HH:mm:ss OOOO';

export const NON_EXISTING_USER_ERROR = 'UserNotFoundException';

const ALIAS_EXISTS_COGNITO_EXCEPTION = 'AliasExistsException';

interface CreateOAuthUserParams {
  creditor: Credor;
  tempPassword?: string;
  transaction?: Transaction;
}

@Injectable()
export class UserRepository {
  private readonly logger = new Logger(UserRepository.name);

  constructor(
    private readonly awsSdk: AWSService,
    private readonly config: ConfigurationEnv,
    private readonly userIdentifierRepo: UserIdentifierRepository,
    @Inject(SECRET_VAULT) private readonly vault: SecretVault,
  ) {}

  async createOAuthUser({
    creditor,
    tempPassword,
    transaction,
  }: CreateOAuthUserParams): Promise<CognitoIdentityServiceProvider.UserType> {
    const userName = creditor.email;
    const normalizedEmail = Credor.normalizeEmail(creditor.email);
    const { data: remoteUser } = await this.getRemoteUser(userName);
    if (remoteUser) {
      creditor.email = normalizedEmail;
      await Promise.all([
        this.userIdentifierRepo.upsert(
          {
            creditorId: creditor.id_credor,
            oidcId: this.config.userAndPasswordOidcId,
            providerId: remoteUser.Username,
          },
          transaction,
        ),
        creditor.save({ transaction }),
      ]);
      return remoteUser;
    }

    const cognitoUserCreated = await this.awsSdk.cognito
      .adminCreateUser({
        UserPoolId: this.config.cognitoUserPool,
        Username: userName,
        DesiredDeliveryMediums: [],
        MessageAction: 'SUPPRESS',
        TemporaryPassword: tempPassword,
        UserAttributes: [
          { Name: 'email', Value: userName },
          { Name: 'email_verified', Value: 'true' },
        ],
      })
      .promise();

    // setting user password to avoid "Force change password" status on cognito
    const strongPassword = generatePassword(16, true, true, true, true);
    await this.changePassword(userName, strongPassword);

    return cognitoUserCreated.User;
  }

  async deleteOAuthUser({
    creditor,
    user,
    transaction,
  }: {
    creditor: Credor;
    user?: AdminGetUserResponse;
    transaction?: Transaction;
  }) {
    const delUser = user ?? (await this.getRemoteUser(creditor.email)).data;
    if (!delUser) {
      this.logger.warn(
        {
          creditor,
          email: creditor.email,
        },
        'User to delete does not exist on cognito',
      );
      return;
    }

    try {
      this.logger.debug({ username: delUser.Username }, 'deleting user on cognito');
      await this.awsSdk.cognito
        .adminDeleteUser({
          UserPoolId: this.config.cognitoUserPool,
          Username: delUser.Username,
        })
        .promise();
    } catch (e) {
      this.logger.error(
        {
          creditor,
        },
        'Failed to delete creditor from cognito',
      );
      throw new DefaultException(`Failed to delete creditor ${creditor.id_credor} from cognito`);
    }

    await User_Identifier.destroy({
      where: {
        oidc_id: this.config.userAndPasswordOidcId,
        creditor_id: creditor.id_credor,
      },
      transaction,
    });
  }

  async getRemoteUser(
    userName: string,
  ): Promise<{ success: boolean; data?: AdminGetUserResponse }> {
    try {
      const res = await this.awsSdk.cognito
        .adminGetUser({
          UserPoolId: this.config.cognitoUserPool,
          Username: userName,
        })
        .promise();

      return { success: true, data: res };
    } catch (err) {
      return { success: false };
    }
  }

  async removeUserAttrs(
    userName: string,
    attrNames: string[],
  ): Promise<AdminDeleteUserAttributesResponse> {
    // todo: check for error treatment (user doesnt exist)
    return this.awsSdk.cognito
      .adminDeleteUserAttributes({
        UserAttributeNames: attrNames,
        UserPoolId: this.config.cognitoUserPool,
        Username: userName,
      })
      .promise();
  }

  async addUserAttrs(
    userName: string,
    attrs: Array<{ Name: string; Value: string }>,
  ): Promise<AdminUpdateUserAttributesResponse> {
    // todo: check for error treatment (user doesnt exist)
    return this.awsSdk.cognito
      .adminUpdateUserAttributes({
        UserAttributes: attrs,
        UserPoolId: this.config.cognitoUserPool,
        Username: userName,
      })
      .promise();
  }

  async confirmUserEmail(email: string) {
    return this.awsSdk.cognito
      .adminUpdateUserAttributes({
        UserPoolId: this.config.cognitoUserPool,
        Username: email,
        UserAttributes: [
          { Name: 'email', Value: email },
          { Name: 'email_verified', Value: 'true' },
        ],
      })
      .promise();
  }

  async updateEmail(creditor: Credor, newEmail: string) {
    return this.awsSdk.cognito
      .adminUpdateUserAttributes({
        UserPoolId: this.config.cognitoUserPool,
        Username: creditor.email,
        UserAttributes: [
          { Name: 'email', Value: Credor.normalizeEmail(newEmail) || '' },
          { Name: 'email_verified', Value: 'true' },
        ],
      })
      .promise()
      .catch(err => {
        if (err.code === ALIAS_EXISTS_COGNITO_EXCEPTION) {
          this.logger.error(
            {
              creditor,
              newEmail,
            },
            'Failed to update creditor email on cognito because there is already an user with this email',
          );
          throw new CustomException(BackendErrorCodes.EMAIL_ALREADY_REGISTERED, {
            email: newEmail,
            external_creditor_id: creditor.id_credor_externo,
          });
        }
      });
  }

  async changePassword(email: string, newPassword: string) {
    const { data: user } = await this.getRemoteUser(email);

    return await this.awsSdk.cognito
      .adminSetUserPassword({
        UserPoolId: this.config.cognitoUserPool,
        Username: user ? user.Username : email,
        Password: newPassword,
        Permanent: true,
      })
      .promise();
  }

  async loginWithPassword(
    email: string,
    password: string,
  ): Promise<{ IdToken: string; AccessToken: string; RefreshToken: string }> {
    const { data: user } = await this.getRemoteUser(email);

    const oidc = await Oidc_Provider.findOne({
      where: {
        id: this.config.userAndPasswordOidcId,
      },
    });
    if (!oidc) {
      this.logger.error(
        {
          loginMethodId: this.config.userAndPasswordOidcId,
        },
        'Failed to get cognito OIDC from database',
      );
      throw new DefaultException('Cognito OIDC not found');
    }

    try {
      const client_secret = await this.vault.getSecret(oidc.secret_name);

      const username = user ? user.Username : email;
      const message = username + oidc.client_id;
      const secretHash = crypto
        .createHmac('SHA256', client_secret)
        .update(message)
        .digest('base64');

      const params = {
        AuthFlow: 'ADMIN_USER_PASSWORD_AUTH',
        ClientId: oidc.client_id,
        UserPoolId: this.config.cognitoUserPool,
        AuthParameters: {
          USERNAME: username,
          PASSWORD: password,
          SECRET_HASH: secretHash,
        },
      };
      const { AuthenticationResult } = await this.awsSdk.cognito
        .adminInitiateAuth(params)
        .promise();
      const { IdToken, AccessToken, RefreshToken } = AuthenticationResult;
      return {
        IdToken,
        AccessToken,
        RefreshToken,
      };
    } catch (e) {
      this.logger.error(
        {
          err: e,
          email,
        },
        'Failed to log in user to cognito',
      );
      throw new DefaultException(e);
    }
  }
}
