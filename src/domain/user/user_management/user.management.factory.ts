import { ConfigurationEnv } from '../../../config/configuration.env';
import { DocumentSignerDomain } from '../../../document.signature/domain/document.signer.domain';
import { ReconcileSignersService } from '../../../document.signature/service/reconcile.signers.service';
import { AtributosEscritorioRepository } from '../../repository/atributosEscritorioRepository';
import { CreditorRepository } from '../../repository/creditorRepository';
import { OnboardingTokensRepository } from '../../repository/onboarding.tokens.repository';
import { UserIdentifierRepository } from '../../repository/user.identifier.repository';
import { UserRepository } from '../../repository/userRepository';
import CreditorNotificationService from '../../../services/creditor.notification.service';
import { UserManagementUpsertDto } from 'shared-types';
import { UserManagementService } from './user.management.service';
import { TeamsRepository } from '../../repository/teamsRepository';
import { Injectable } from '@nestjs/common';
import { AuthenticationContextDto } from '../../../dto/authenticationContextDto';

@Injectable()
export class UserManagementServiceFactory {
  constructor(
    private readonly teamsRepository: TeamsRepository,
    private readonly creditorRepository: CreditorRepository,
    private readonly userRepository: UserRepository,
    private readonly onboardingTokensRepository: OnboardingTokensRepository,
    private readonly officeAttributesRepository: AtributosEscritorioRepository,
    private readonly userIdentifierRepository: UserIdentifierRepository,
    private readonly notification: CreditorNotificationService,
    private readonly configurationEnv: ConfigurationEnv,
    private readonly documentSignerDomain: DocumentSignerDomain,
    private readonly reconcileSignersService: ReconcileSignersService,
  ) {}

  public async buildFromRows(
    officeId: number,
    loggedUser: AuthenticationContextDto,
    { rows, fields_to_update }: UserManagementUpsertDto,
  ): Promise<UserManagementService> {
    const service = new UserManagementService(
      officeId,
      this.teamsRepository,
      this.creditorRepository,
      this.userRepository,
      this.onboardingTokensRepository,
      this.officeAttributesRepository,
      this.userIdentifierRepository,
      this.notification,
      this.configurationEnv,
      this.documentSignerDomain,
      this.reconcileSignersService,
      rows,
      fields_to_update,
      loggedUser,
    );

    await service.fillMaps();
    return service;
  }
}
