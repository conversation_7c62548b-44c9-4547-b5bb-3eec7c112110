import { Logger } from '@nestjs/common';
import { Transaction } from 'sequelize';
import {
  BackendErrorCodes,
  GetUserManagementDateRows,
  ReconcileSignerRequest,
  UserBulkUpsertRequest,
  UserManagementColumns,
  UserManagementDiffResponse,
  UserManagementDiffRow,
  UserManagementDiffStatus,
  UsersClaims,
} from 'shared-types';
import { ConfigurationEnv } from '../../../config/configuration.env';
import { sequelizeConnection } from '../../../database.provider';
import { DocumentSignerDomain } from '../../../document.signature/domain/document.signer.domain';
import { ReconcileSignersService } from '../../../document.signature/service/reconcile.signers.service';
import { AuthenticationContextDto } from '../../../dto/authenticationContextDto';
import { Atributos_credor } from '../../../models/atributos_credor';
import { Creditor_Teams } from '../../../models/creditor_teams';
import { Credor } from '../../../models/credor';
import { Permission_Profile } from '../../../models/permission_profile';
import { Teams } from '../../../models/teams';
import { UserPermissionCreationAttributes, User_Permission } from '../../../models/user_permission';
import CreditorNotificationService from '../../../services/creditor.notification.service';
import { stringAsBool } from '../../../utils/boolean.utils';
import { CustomException } from '../../../utils/error.utils';
import { MapUtils } from '../../../utils/map.utils';
import { SetUtils } from '../../../utils/set.utils';
import { getArrayFromString, normalizeCreditor } from '../../../utils/string.utils';
import { AtributosEscritorioRepository } from '../../repository/atributosEscritorioRepository';
import { CreditorRepository } from '../../repository/creditorRepository';
import { OnboardingTokensRepository } from '../../repository/onboarding.tokens.repository';
import { TeamsRepository } from '../../repository/teamsRepository';
import { UserIdentifierRepository } from '../../repository/user.identifier.repository';
import { UserRepository } from '../../repository/userRepository';
import { UsersUpsert } from '../users.upsert';

export const TRUE_VALUE = 'TRUE';
export const FALSE_VALUE = 'FALSE';

type FieldsToUpdate = Record<keyof Omit<UserManagementColumns, 'user_id'>, boolean>;

export class UserManagementService {
  private readonly logger = new Logger(UserManagementService.name);

  private creditorsMap: Map<string, Credor>;

  // team_name -> Teams
  private teamsMap: Map<string, Teams>;

  // id_credor -> Teams[]
  private creditorTeamsMap: Map<number, Teams[]>;

  // profile_name -> Permission_Profile
  private profilesMap: Map<string, Permission_Profile>;

  // id_credor -> Permission_Profile[]
  private userPermissionsMap: Map<number, Permission_Profile[]>;

  // id_credor -> Atributos_credor[]
  private userClaimsMap: Map<number, Map<UsersClaims, Atributos_credor[]>>;

  private referencedCreditorIds: Set<string> = new Set();

  constructor(
    private readonly officeId: number,
    private readonly teamsRepository: TeamsRepository,
    private readonly creditorRepository: CreditorRepository,
    private readonly userRepository: UserRepository,
    private readonly onboardingTokensRepository: OnboardingTokensRepository,
    private readonly officeAttributesRepository: AtributosEscritorioRepository,
    private readonly userIdentifierRepository: UserIdentifierRepository,
    private readonly notification: CreditorNotificationService,
    private readonly configurationEnv: ConfigurationEnv,
    private readonly documentSignerDomain: DocumentSignerDomain,
    private readonly reconcileSignersService: ReconcileSignersService,
    private readonly rows: GetUserManagementDateRows['rows'],
    private readonly fieldsToUpdate: FieldsToUpdate | undefined,
    private readonly loggedUser: AuthenticationContextDto,
  ) {
    const userIds: Set<string> = new Set();
    const userEmails: Set<string> = new Set();

    rows.forEach(r => {
      r.user_id = normalizeCreditor(r.user_id);
      this.referencedCreditorIds.add(r.user_id);

      if (userIds.has(r.user_id))
        throw new CustomException(BackendErrorCodes.USER_MGMT_DUPLICATED_USER_ID);

      if (r.email && userEmails.has(r.email))
        throw new CustomException(BackendErrorCodes.USER_MGMT_DUPLICATED_EMAIL);

      userIds.add(r.user_id);
      userEmails.add(r.email);

      getArrayFromString(r.people_can_see).forEach(p => {
        this.referencedCreditorIds.add(p);
      });
    });

    this.rows = rows;
  }

  public async fillMaps(transaction?: Transaction) {
    const creditors = await Credor.findAll({
      where: {
        escritorio_id_escritorio: this.officeId,
        id_credor_externo: Array.from(this.referencedCreditorIds),
      },
      include: [
        {
          model: Teams,
          attributes: ['id', 'name'],
        },
        {
          model: Permission_Profile,
          attributes: ['id', 'name'],
        },
      ],
      transaction,
    });

    this.creditorsMap = MapUtils.arrayToMapLambda(creditors, c =>
      normalizeCreditor(c.id_credor_externo),
    );

    const creditorIds = [...this.creditorsMap.values()].map(c => c.id_credor);

    const [creditorTeams, userPermissions, userClaims, profiles, teams] = await Promise.all([
      Creditor_Teams.findAll({
        where: {
          creditor_id: creditorIds,
        },
        include: [Teams],
        transaction,
      }),
      User_Permission.findAll({
        where: {
          creditor_id: creditorIds,
        },
        include: [
          {
            model: Permission_Profile,
            attributes: ['id', 'name'],
          },
        ],
        transaction,
      }),
      Atributos_credor.findAll({
        where: {
          credor_id_credor: creditorIds,
          atributo: [UsersClaims.TEAMS_VIEW_PERMISSION, UsersClaims.CREDITORS_VIEW_PERMISSION],
        },
        transaction,
      }),
      Permission_Profile.findAll({
        where: {
          office_id: this.officeId,
        },
        transaction,
      }),
      Teams.findAll({
        where: {
          office_id: this.officeId,
        },
        transaction,
      }),
    ]);

    this.creditorTeamsMap = MapUtils.groupBy<number, Teams>(creditorTeams, 'creditor_id', {
      selectValue: ct => ct.team,
    });
    this.profilesMap = MapUtils.arrayToMapLambda(profiles, p => p.name);
    this.teamsMap = MapUtils.arrayToMapLambda(teams, t => t.name.toUpperCase());
    this.userPermissionsMap = MapUtils.groupBy<number, Permission_Profile>(
      userPermissions,
      'creditor_id',
      { selectValue: p => p.permission_profile },
    );
    this.userClaimsMap = new Map();
    userClaims.forEach(c => {
      const userMap = this.userClaimsMap.get(c.credor_id_credor) ?? new Map();
      const claimArray = userMap.get(c.atributo) ?? [];

      claimArray.push(c);

      userMap.set(c.atributo, claimArray);
      this.userClaimsMap.set(c.credor_id_credor, userMap);
    });
  }

  public async getDiff(): Promise<UserManagementDiffResponse> {
    const creditorsToCreate: Set<string> = new Set();
    const teamsToCreate: Set<string> = new Set();

    this.rows.forEach(row => {
      if (!this.creditorsMap.has(row.user_id)) {
        creditorsToCreate.add(row.user_id);
      }

      const teams = row.team.split(',') || [];
      teams.forEach(team => {
        if (!this.teamsMap.has(team.toUpperCase())) {
          teamsToCreate.add(team.toUpperCase());
        }
      });
    });

    const rowsDiff = this.rows.map(row => {
      const current: Credor | undefined = this.creditorsMap.get(row.user_id);

      let modified = false;

      const rowDiff: Partial<UserManagementDiffRow> = {
        user_id: row.user_id,
      };

      if (this.makeEmailDiff(current, row, rowDiff)) modified = true;
      if (this.makeUserNameDiff(current, row, rowDiff)) modified = true;
      if (this.makeActiveDiff(current, row, rowDiff)) modified = true;
      if (this.makeTeamDiff(current, row, rowDiff)) modified = true;
      if (this.makeProfileDiff(current, row, rowDiff)) modified = true;
      if (this.makePeopleCanSeeDiff(current, row, rowDiff, creditorsToCreate)) modified = true;
      if (this.makeTeamCanSeeDiff(current, row, rowDiff, teamsToCreate)) modified = true;

      rowDiff.status = UserManagementDiffStatus.NEW;
      if (current) {
        rowDiff.status = modified
          ? UserManagementDiffStatus.MODIFIED
          : UserManagementDiffStatus.NOT_MODIFIED;
      }

      return rowDiff as UserManagementDiffRow;
    });

    return { rows: rowsDiff };
  }

  private makeUserNameDiff(
    current: Credor | undefined,
    row: GetUserManagementDateRows['rows'][number],
    diffRow: Partial<UserManagementDiffRow>,
  ): boolean {
    if (!current) {
      diffRow.user_name = {
        new: row.user_name,
      };
      return false;
    }

    const isDiff =
      this.fieldsToUpdate?.user_name !== false && current.nome_credor !== row.user_name;

    diffRow.user_name = isDiff
      ? { current: current.nome_credor, new: row.user_name }
      : { current: current.nome_credor };

    return isDiff;
  }

  private makeEmailDiff(
    current: Credor | undefined,
    row: GetUserManagementDateRows['rows'][number],
    diffRow: Partial<UserManagementDiffRow>,
  ): boolean {
    const email = row.email || null;

    if (!current) {
      diffRow.email = {
        new: email,
      };
      return false;
    }

    const isDiff = this.fieldsToUpdate?.email !== false && current.email !== email;

    diffRow.email = isDiff ? { current: current.email, new: email } : { current: current.email };

    return isDiff;
  }

  private makeActiveDiff(
    current: Credor | undefined,
    row: GetUserManagementDateRows['rows'][number],
    diffRow: Partial<UserManagementDiffRow>,
  ): boolean {
    if (!current) {
      diffRow.active = {
        new: stringAsBool(row.active) ? TRUE_VALUE : FALSE_VALUE,
      };
      return false;
    }

    const currentString = current.ativo ? TRUE_VALUE : FALSE_VALUE;
    const activeBoolean = stringAsBool(row.active);

    const isDiff = this.fieldsToUpdate?.active !== false && current.ativo !== activeBoolean;

    diffRow.active = isDiff
      ? { current: currentString, new: activeBoolean ? TRUE_VALUE : FALSE_VALUE }
      : { current: currentString };

    return isDiff;
  }

  private makeTeamDiff(
    current: Credor | undefined,
    row: GetUserManagementDateRows['rows'][number],
    diffRow: Partial<UserManagementDiffRow>,
  ): boolean {
    const currentTeams = this.creditorTeamsMap.get(current?.id_credor)?.map(t => t.name) || [];
    const newTeams = this.getTeamArray(row.team);

    const normalizedCurrentTeamsMap = MapUtils.arrayToMapLambda(currentTeams, t => t.toUpperCase());
    newTeams.forEach((t, i) => {
      if (!normalizedCurrentTeamsMap.has(t.toUpperCase())) return;

      const realTeamName = normalizedCurrentTeamsMap.get(t.toUpperCase());
      newTeams[i] = realTeamName;
    });

    const { rowDiff, modified } = this.makeArrayDiff(
      currentTeams,
      newTeams,
      this.fieldsToUpdate?.team !== false,
      !current,
    );

    diffRow.team = rowDiff;

    return modified;
  }

  private makeProfileDiff(
    current: Credor,
    row: GetUserManagementDateRows['rows'][number],
    diffRow: Partial<UserManagementDiffRow>,
  ): boolean {
    const currentProfiles = this.userPermissionsMap.get(current?.id_credor)?.map(p => p.name) || [];
    const newProfiles = getArrayFromString(row.profile).filter(p => this.profilesMap.has(p));

    const { rowDiff, modified } = this.makeArrayDiff(
      currentProfiles,
      newProfiles,
      this.fieldsToUpdate?.profile !== false,
      !current,
    );

    diffRow.profile = rowDiff;
    return modified;
  }

  private makePeopleCanSeeDiff(
    current: Credor,
    row: GetUserManagementDateRows['rows'][number],
    diffRow: Partial<UserManagementDiffRow>,
    newCreditors: Set<string>,
  ): boolean {
    const currentPeopleCanSee =
      this.userClaimsMap
        .get(current?.id_credor)
        ?.get(UsersClaims.CREDITORS_VIEW_PERMISSION)
        ?.flatMap(c => JSON.parse(c.valor)) || [];

    const newPeopleCanSee = getArrayFromString(row.people_can_see).filter(
      p => this.creditorsMap.has(p) || newCreditors.has(p),
    );

    const { rowDiff, modified } = this.makeArrayDiff(
      currentPeopleCanSee,
      newPeopleCanSee,
      this.fieldsToUpdate?.people_can_see !== false,
      !current,
    );

    diffRow.people_can_see = rowDiff;

    return modified;
  }

  private makeTeamCanSeeDiff(
    current: Credor,
    row: GetUserManagementDateRows['rows'][number],
    diffRow: Partial<UserManagementDiffRow>,
    newTeams: Set<string>,
  ): boolean {
    const currentTeamsCanSee =
      this.userClaimsMap
        .get(current?.id_credor)
        ?.get(UsersClaims.TEAMS_VIEW_PERMISSION)
        ?.flatMap(c => JSON.parse(c.valor)) || [];

    const newTeamsCanSee = this.getTeamArray(row.team_can_see).filter(
      p => this.teamsMap.has(p.toUpperCase()) || newTeams.has(p.toUpperCase()),
    );

    const { rowDiff, modified } = this.makeArrayDiff(
      currentTeamsCanSee,
      newTeamsCanSee,
      this.fieldsToUpdate?.team_can_see !== false,
      !current,
    );

    diffRow.team_can_see = rowDiff;

    return modified;
  }

  private getTeamArray(teams: string) {
    const array = getArrayFromString(teams);
    const normalizedSet: Set<string> = new Set();
    const teamsArray: string[] = [];

    array.forEach(team => {
      if (normalizedSet.has(team.toUpperCase())) return;

      normalizedSet.add(team.toUpperCase());
      teamsArray.push(team);
    });

    return teamsArray;
  }

  private makeArrayDiff(
    currentArr: string[],
    newArr: string[],
    updateField: boolean,
    isNew: boolean,
  ): { rowDiff: { current?: string; new?: string }; modified: boolean } {
    const currentSet = new Set(currentArr);
    const newSet = new Set(newArr);

    if (isNew) {
      return { rowDiff: { new: Array.from(newSet).join(',') }, modified: false };
    }

    const isEqual = !updateField || SetUtils.equals(currentSet, newSet);

    const rowDiff = isEqual
      ? { current: Array.from(currentSet).join(',') }
      : { current: Array.from(currentSet).join(','), new: Array.from(newSet).join(',') };

    return { rowDiff, modified: !isEqual };
  }

  public async makeUpsert(): Promise<void> {
    const creditorsToUpsert: UserBulkUpsertRequest[] = [];
    const teamsToCreate: Set<string> = new Set();

    const newCreditorTeams: { user_id: string; team_name: string }[] = [];
    const newUserProfiles: { user_id: string; profile_name: string }[] = [];
    const newUserClaims: { user_id: string; atributo: UsersClaims; value: string }[] = [];

    const getNewValue = <T = string>(
      rowDiff: { current?: string; new?: string },
      mapFn: (value: string) => T = (v: string) => v as T,
    ) => {
      return 'new' in rowDiff ? mapFn(rowDiff.new) : mapFn(rowDiff.current);
    };

    const lazyInsertCreditorTeams = (
      creditorId: string,
      teams: { current?: string; new?: string },
    ) => {
      const teamsArray = getNewValue(teams, getArrayFromString);

      teamsArray.forEach(team => {
        if (!this.teamsMap.has(team.toUpperCase())) teamsToCreate.add(team);

        newCreditorTeams.push({
          user_id: creditorId,
          team_name: team,
        });
      });
    };

    const lazyInsertUserProfile = (
      creditorId: string,
      profile: { current?: string; new?: string },
    ) => {
      const profileArray = getNewValue(profile, getArrayFromString);

      profileArray.forEach(p => {
        if (!this.profilesMap.has(p))
          throw new CustomException(BackendErrorCodes.PROFILE_NOT_FOUND, { profile_name: p });

        newUserProfiles.push({
          user_id: creditorId,
          profile_name: p,
        });
      });
    };

    const lazyInsertUserClaim = (
      creditorId: string,
      claim: UsersClaims,
      values: { current?: string; new?: string },
    ) => {
      const valuesArray = getNewValue(values, getArrayFromString);

      newUserClaims.push({
        user_id: creditorId,
        atributo: claim,
        value: JSON.stringify(valuesArray),
      });
    };

    const lazyUpsertCreditor = (row: UserManagementDiffRow) => {
      const active = getNewValue(row.active, stringAsBool);
      const email = getNewValue(row.email);
      const name = getNewValue(row.user_name);

      const credor: UserBulkUpsertRequest = {
        external_creditor_id: row.user_id,
        active,
        email,
        name,
      };

      creditorsToUpsert.push(credor);
    };

    const diff = await this.getDiff();

    for (const row of diff.rows) {
      if (row.status === UserManagementDiffStatus.NOT_MODIFIED) continue;

      lazyUpsertCreditor(row);

      lazyInsertCreditorTeams(row.user_id, row.team);

      lazyInsertUserProfile(row.user_id, row.profile);

      lazyInsertUserClaim(row.user_id, UsersClaims.CREDITORS_VIEW_PERMISSION, row.people_can_see);

      lazyInsertUserClaim(row.user_id, UsersClaims.TEAMS_VIEW_PERMISSION, row.team_can_see);
    }

    await sequelizeConnection.transaction(async transaction =>
      this.commitUsersUpsert(
        creditorsToUpsert,
        teamsToCreate,
        newCreditorTeams,
        newUserProfiles,
        newUserClaims,
        transaction,
      ),
    );
  }

  private async commitUsersUpsert(
    creditorsToUpsert: UserBulkUpsertRequest[],
    teamsToCreate: Set<string>,
    newCreditorTeams: { user_id: string; team_name: string }[],
    newUserProfiles: { user_id: string; profile_name: string }[],
    newUserClaims: { user_id: string; atributo: UsersClaims; value: string }[],
    transaction: Transaction,
  ) {
    const teamNames = Array.from(teamsToCreate);

    const teamsToInsert = teamNames.map(name => ({
      office_id: this.officeId,
      name,
    }));

    await Promise.all([
      Teams.bulkCreate(teamsToInsert, {
        updateOnDuplicate: ['name'],
        transaction,
      }),
      this.upsertUsers(creditorsToUpsert, transaction),
    ]);

    const newTeams = await Teams.findAll({
      where: {
        office_id: this.officeId,
        name: teamNames,
      },
      transaction,
    });

    const newCreditors = await Credor.findAll({
      where: {
        escritorio_id_escritorio: this.officeId,
        id_credor_externo: creditorsToUpsert.map(c => c.external_creditor_id),
      },
      transaction,
    });

    newTeams.forEach(t => this.teamsMap.set(t.name.toUpperCase(), t));
    newCreditors.forEach(c => {
      if (this.creditorsMap.has(c.id_credor_externo)) return;

      this.creditorsMap.set(c.id_credor_externo, c);
    });

    const creditorIds = creditorsToUpsert.map(
      c => this.creditorsMap.get(c.external_creditor_id)!.id_credor,
    );

    const creditorTeamsPromise = Creditor_Teams.destroy({
      where: {
        creditor_id: creditorIds,
      },
      transaction,
    });
    const profilesPromise = User_Permission.destroy({
      where: {
        creditor_id: creditorIds,
      },
      transaction,
    });
    await Promise.all([creditorTeamsPromise, profilesPromise]);

    const userClaims = newUserClaims.map(c => ({
      credor_id_credor: this.creditorsMap.get(c.user_id)!.id_credor,
      atributo: c.atributo,
      valor: c.value,
    }));

    const userTeams = newCreditorTeams.map(ct => ({
      creditor_id: this.creditorsMap.get(ct.user_id)!.id_credor,
      team_id: this.teamsMap.get(ct.team_name.toUpperCase())!.id,
    }));

    const userProfiles = newUserProfiles.map(up => ({
      creditor_id: this.creditorsMap.get(up.user_id)!.id_credor,
      permission_profile_id: this.profilesMap.get(up.profile_name)!.id,
    }));

    await Promise.all([
      Creditor_Teams.bulkCreate(userTeams, { transaction }),
      User_Permission.bulkCreate(userProfiles, { transaction }),
      Atributos_credor.bulkCreate(userClaims, { updateOnDuplicate: ['valor'], transaction }),
    ]);

    const unusedTeams = await this.teamsRepository.getUnusedTeams(this.officeId, transaction);
    await this.teamsRepository.deleteById(
      unusedTeams.map(t => t.id),
      transaction,
    );
  }

  private async upsertUsers(creditorsToUpsert: UserBulkUpsertRequest[], transaction: Transaction) {
    const usersUpsert = new UsersUpsert(
      this.creditorRepository,
      this.userRepository,
      this.onboardingTokensRepository,
      this.notification,
      this.officeAttributesRepository,
      this.configurationEnv,
      this.userIdentifierRepository,
      this.reconcileSignersService,
      this.documentSignerDomain,
      this.officeId,
      creditorsToUpsert,
      {
        name: this.fieldsToUpdate?.user_name !== false,
        email: this.fieldsToUpdate?.email !== false,
        active: this.fieldsToUpdate?.active !== false,
      },
    );

    await usersUpsert.execute(this.loggedUser, transaction);
  }

  public async getCreditorsToReconcile(): Promise<ReconcileSignerRequest[]> {
    const usedUserIds = [...this.creditorsMap.values()].map(c => c.id_credor);

    const [userTeams, userProfiles] = await Promise.all([
      Creditor_Teams.findAll({
        where: {
          creditor_id: usedUserIds,
        },
        include: [Teams],
      }),
      User_Permission.findAll({
        where: {
          creditor_id: usedUserIds,
        },
      }),
    ]);

    const requestProfiles: UserPermissionCreationAttributes[] = [];
    userProfiles.forEach(p => {
      requestProfiles.push({
        creditor_id: p.creditor_id,
        permission_profile_id: p.permission_profile_id,
      });
    });

    const requestTeams: Map<number, Set<string>> = new Map();
    userTeams.forEach(t => {
      const teamsSet = requestTeams.get(t.creditor_id) ?? new Set<string>();
      teamsSet.add(t.team.name);
      requestTeams.set(t.creditor_id, teamsSet);
    });

    const creditorsToReconcile = this.makeCreditorsArrayToReconcile({
      requestProfiles,
      requestTeams,
      creditorsMap: this.creditorsMap,
    });

    return creditorsToReconcile;
  }

  private makeCreditorsArrayToReconcile({
    requestProfiles,
    requestTeams,
    creditorsMap,
  }: {
    requestProfiles: UserPermissionCreationAttributes[];
    requestTeams: Map<number, Set<string>>;
    creditorsMap: Map<string, Credor>;
  }) {
    const creditorsToReconcile = new Map<number, ReconcileSignerRequest>();
    const profilesMapById = MapUtils.groupBy<number, UserPermissionCreationAttributes>(
      requestProfiles,
      'creditor_id',
    );
    creditorsMap.forEach(creditor => {
      const creditorAlreadyAdded = creditorsToReconcile.has(creditor.id_credor);
      if (creditorAlreadyAdded) return;

      const teams = requestTeams.get(creditor.id_credor) || new Set<string>();
      const teamsToReconcile = [...teams.values()];

      const profiles = profilesMapById.get(creditor.id_credor) || [];
      const profilesToReconcile = profiles.map(
        ({ permission_profile_id }) => permission_profile_id,
      );

      creditorsToReconcile.set(
        creditor.id_credor,
        this.reconcileSignersService.buildCreditorToReconcile(
          creditor,
          teamsToReconcile,
          profilesToReconcile,
        ),
      );
    });

    return Array.from(creditorsToReconcile.values());
  }
}
