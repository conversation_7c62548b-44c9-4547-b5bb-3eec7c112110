import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreditorRepository } from '../../repository/creditorRepository';
import { DocumentRepository } from '../../repository/document.repository';
import FileRepository from '../../repository/file/file.repository';
import { Document, DocumentCreationAttributes } from '../../../models/document';
import {
  BackendErrorCodes,
  DocumentApproval,
  DocumentRequest,
  DocumentSource,
  EvaluateDocumentApprovalRequest,
  GetDocumentResponse,
  GetDocumentsSchema,
  GetPaginatedDocumentResponse,
  OBJECT_WORKFLOW_KEY_TAG,
  UpdateDocumentRequest,
  UsersClaims,
} from 'shared-types';
import { CreateDocumentDto } from '../../../dto/createDocument.dto';
import {
  CREATE_DOCUMENTS_INEXISTENT_CREDITORS,
  CREATE_DOCUMENTS_INEXISTENT_FILE_IDS,
} from '../../../controller/document/errors';
import { Credor } from '../../../models/credor';
import { FileDomain, StorageFile } from '../../file.domain';
import { Escritorio } from '../../../models/escritorio';
import JSZip from 'jszip';
import { formatISO } from 'date-fns';
import { File } from '../../../models/file';
import CreditorNotificationService from '../../../services/creditor.notification.service';
import type { Request } from 'express';
import { DocumentTagRepository } from '../../repository/documentTag.repository';
import { Document_tag, DocumentTagAttributes } from '../../../models/document_tag';
import { Sequelize, Transaction } from 'sequelize';
import { sequelizeConnection } from '../../../database.provider';
import { SheetDbApiService } from '../../../services/sheet.db.api.service';
import { GetDocumentResponseDto } from '../../../dto/documents/get.document.response.dto';
import { CustomException } from '../../../utils/error.utils';
import { DocumentQueryBuilder } from '../DocumentWhereBuilder';
import { MapUtils } from '../../../utils/map.utils';
import { AuthenticationContextDto } from '../../../dto/authenticationContextDto';
import { DocumentNotifyService } from '../service/document.notify.service';
import { checkListDocumentsPermission } from '../../../controller/document/permissions';
import { WorkflowObjectInputsService } from '../../../object.workflow/service/workflow.object.inputs.service';

interface DeleteDocumentsParams {
  documentIds: number[];
  officeId: number;
  req: Request;
}

@Injectable()
export class DocumentDomain {
  protected readonly logger = new Logger(DocumentDomain.name);

  constructor(
    private readonly fileRepository: FileRepository,
    private readonly creditorRepository: CreditorRepository,
    private readonly documentRepository: DocumentRepository,
    private readonly documentTagRepository: DocumentTagRepository,
    private readonly fileDomain: FileDomain,
    private readonly creditorNotificationService: CreditorNotificationService,
    private readonly sheetDbApiService: SheetDbApiService,
    private readonly documentNotifySvc: DocumentNotifyService,
    private readonly workflowObjectInputsService: WorkflowObjectInputsService,
  ) {}

  public addDocuments = async (
    officeId: number,
    documentsRequest: DocumentRequest[],
    requester: AuthenticationContextDto,
  ): Promise<Document[]> => {
    const creditorId = requester.getUserForAudit().credor.id_credor;

    const { createdDocuments, creditorsMap } = await sequelizeConnection.transaction(
      async (transaction: Transaction) => {
        const documents = CreateDocumentDto.validate(documentsRequest);
        const requestCreditors = [...new Set(documents.map(doc => doc.creditor_id))];
        const filesIds = documents.map(doc => doc.file_id);

        const files = await this.fileRepository.getFiles(
          {
            office_id: officeId,
            id: filesIds,
          },
          { transaction },
        );

        this.validateFilesFound(files, filesIds);

        const creditors = await this.creditorRepository.getCreditors(
          {
            id_credor_externo: requestCreditors,
            escritorio_id_escritorio: officeId,
          },
          [Escritorio],
          { transaction },
        );

        const creditorsMap = MapUtils.arrayToMap<string, Credor>(creditors, 'id_credor_externo');

        const filesMap = MapUtils.arrayToMap<number, File>(files, 'id');

        const documentsToCreate = documents.map<DocumentCreationAttributes>(doc => {
          const destinedFor = creditorsMap.get(doc.creditor_id);

          if (!destinedFor)
            throw new BadRequestException({
              ...CREATE_DOCUMENTS_INEXISTENT_CREDITORS,
              context: { requested_creditor: doc.creditor_id },
            });

          this.validateTags(doc);

          return {
            date: doc.date,
            created_by: creditorId,
            creditor_id: destinedFor.id_credor,
            file_id: doc.file_id,
            name: doc.name ?? filesMap.get(doc.file_id).name,
            approval: 'PENDING',
            source: doc.source,
          };
        });

        const createdDocs = await this.documentRepository.createDocuments(
          documentsToCreate,
          transaction,
        );

        const allDocsTags: DocumentTagAttributes[] = [];

        for (const [index, doc] of createdDocs.entries()) {
          if (documents[index].tags) {
            const docTagNames = new Set<string>();

            for (const [tagName, tagValue] of Object.entries(documents[index].tags)) {
              docTagNames.add(tagName);

              allDocsTags.push({
                document_id: doc.id,
                tag_name: tagName,
                tag_value: tagValue,
              });
            }
          }
        }

        await this.documentTagRepository.createDocumentTags(allDocsTags, transaction);

        const createdDocuments = await this.documentRepository.getDocuments(
          { id: createdDocs.map(doc => doc.id) },
          [{ model: Credor, as: 'credor' }, { model: Document_tag }],
          undefined,
          transaction,
        );

        return { creditorsMap, createdDocuments };
      },
    );

    await this.documentNotifySvc.notifyUploadedDocuments(officeId, createdDocuments, creditorsMap);

    return createdDocuments;
  };

  private validateFilesFound(files: File[], filesId: number[]) {
    const foundFileIds = files.map(f => f.id);

    filesId.forEach(id => {
      if (!foundFileIds.includes(id))
        throw new BadRequestException({
          ...CREATE_DOCUMENTS_INEXISTENT_FILE_IDS,
          context: { file: id },
        });
    });
  }

  async deleteDocuments({ documentIds, officeId, req }: DeleteDocumentsParams): Promise<void> {
    const documentsToDelete = await this.documentRepository.getDocuments({ id: documentIds }, [
      {
        model: File,
        where: { office_id: officeId },
      },
      { model: Credor, foreignKey: 'creditor_id', as: 'credor' },
    ]);
    if (!documentsToDelete || !documentsToDelete.length) return;

    await checkListDocumentsPermission(
      req,
      documentsToDelete.map(d => d.credor.id_credor_externo),
      'delete',
    );

    await sequelizeConnection.transaction(async (transaction: Transaction) => {
      await this.documentRepository.softDeleteDocuments(
        {
          id: documentIds,
        },
        transaction,
      );
      // when deleting documents, we also need to delete the input_data on workflows
      await this.workflowObjectInputsService.deleteDocumentsFromWorkflows(
        officeId,
        documentIds,
        transaction,
      );
    });
  }

  private async addPlanNameToDocs(office: Escritorio, paginatedDocs: GetPaginatedDocumentResponse) {
    const plans = await this.sheetDbApiService.getCommissionPlans(office.client_id, {
      include_deleted: true,
    });

    const plansMapName = new Map<number, string>(
      plans?.flatMap(plan => plan.payout_periods.map(period => [period.id, plan.name])) ?? [],
    );

    return {
      ...paginatedDocs,
      items: paginatedDocs.items.map(doc => ({
        ...doc,
        plan_name: plansMapName.get(doc.tags.periodId ? Number(doc.tags.periodId) : 0),
      })),
    };
  }

  async getDocuments(
    office: Escritorio,
    {
      source,
      limit,
      page,
      approval,
      end_date,
      order_by,
      order_mode,
      start_date,
      creditor_id,
      filter_date_by,
      payment_status,
      plan_id,
      tag_name,
      tag_value,
      document_id,
    }: GetDocumentsSchema,
  ): Promise<GetPaginatedDocumentResponse> {
    const documentQueryBuilder = new DocumentQueryBuilder();

    const creditors = await this.creditorRepository.getCreditors(
      {
        escritorio_id_escritorio: office.id_escritorio,
        id_credor_externo: creditor_id,
      },
      [],
      { includeDeleted: true },
    );

    const builder = documentQueryBuilder
      .setDocumentIds(document_id)
      .setCreditorIds(creditors)
      .setPaymentStatus(payment_status)
      .setApproval(approval)
      .setSource(source)
      .setDateFilter(filter_date_by, start_date, end_date)
      .setOrder(order_by, order_mode)
      .setPagination(limit, page)
      .setTagName(tag_name)
      .setTagValue(tag_value);

    if (plan_id) {
      const { tagValue } = await this.getPlanTags(office.client_id, plan_id);

      builder.setTagName('periodId');
      builder.setTagValue(tagValue);
    }

    const { documentTagWhere, order, pagination, where } = builder.build();

    const docs = await this.documentRepository.getPaginatedDocuments(
      where,
      [
        { model: Credor, as: 'credor' },
        {
          model: Document_tag,
          where: documentTagWhere,
          required: !!documentTagWhere,
        },
        {
          model: File,
          where: {
            office_id: office.id_escritorio,
          },
        },
      ],
      order,
      pagination,
    );

    const isPaginating = page && limit;

    const paginatedDocs = {
      items: docs.rows.map(doc => new GetDocumentResponseDto(doc)),
      count: isPaginating ? docs.count : undefined,
      total_pages: isPaginating ? Math.ceil(docs.count / limit) : undefined,
    };

    const hasPlanDocs = paginatedDocs.items.some(
      doc => doc.source === DocumentSource.COMMISSION_PLAN,
    );

    if (hasPlanDocs) {
      return this.addPlanNameToDocs(office, paginatedDocs);
    }

    return paginatedDocs;
  }

  public async downloadAllFiles(
    office: Escritorio,
    docs: GetDocumentResponse[],
  ): Promise<{ file: Buffer; count: number; fileName: string }> {
    const uniqueFileIds = new Set<number>();
    docs.forEach(doc => uniqueFileIds.add(doc.file_id));

    const filesMap = new Map<number, StorageFile>();
    const allFilePromises = Array.from(uniqueFileIds).map(async id => {
      const file = await this.fileDomain.get(id, office.id_escritorio, office.client_id);
      filesMap.set(id, file);
      return file;
    });

    const allFiles = await Promise.all(allFilePromises);

    if (docs.length === 1)
      return { file: allFiles[0].buffer, count: docs.length, fileName: docs[0].name };

    const zip = new JSZip();
    docs.forEach(doc => {
      const file = filesMap.get(doc.file_id);

      return zip.file(`${doc.id}-${file.filename}`, file.buffer);
    });
    return {
      file: await zip.generateAsync({ type: 'nodebuffer' }),
      count: allFiles.length,
      fileName: `export-${formatISO(new Date(), { format: 'basic' })}.zip`,
    };
  }

  public async evaluateApproval(
    { approval, reason_for_rejection }: EvaluateDocumentApprovalRequest,
    id: number,
    req: Request,
  ) {
    const document = await this.documentRepository.getDocument({ id }, [
      { model: Credor, as: 'credor' },
    ]);

    if (!document) throw new NotFoundException();

    const isSameOffice =
      req.user.escritorio.id_escritorio === document.credor.escritorio_id_escritorio;
    if (!isSameOffice) throw new NotFoundException();

    const allowedToChange = await req.user.canSeeCreditor(document.credor.id_credor_externo);

    if (!allowedToChange) throw new ForbiddenException();

    await document.update({ approval, reason_for_rejection: reason_for_rejection ?? null });

    await this.notifyDocumentEvaluation({
      document,
      status: approval,
      reasonForRejection: reason_for_rejection,
      user: req.user,
    });
  }

  private async notifyDocumentEvaluation({
    document,
    status,
    reasonForRejection,
    user,
  }: {
    document: Document;
    status: DocumentApproval;
    reasonForRejection: string;
    user: AuthenticationContextDto;
  }): Promise<void> {
    if (status === 'PENDING') return;

    const [disableEmails] = user.findSessionClaim(UsersClaims.DISABLE_EMAIL_DISPATCH);

    const shouldNotifyApproved = status === 'APPROVED' && !disableEmails?.document_approval;
    const shouldNotifyRejected = status === 'DISAPPROVED' && !disableEmails?.document_rejection;

    if (shouldNotifyApproved)
      await this.creditorNotificationService.notifyApprovedDocument(document.credor, document.name);

    if (shouldNotifyRejected)
      await this.creditorNotificationService.notifyRefusedDocument(
        document.credor,
        reasonForRejection ?? '',
      );
  }

  public async updateDocument(id: number, payload: UpdateDocumentRequest): Promise<void> {
    return this.documentRepository.updateDocumentById(id, payload);
  }

  getDocument = this.documentRepository.getDocument;

  private validateTags(document: DocumentRequest): void {
    switch (document.source) {
      case DocumentSource.COMMISSION_PLAN:
        if (!document.tags?.periodId)
          throw new CustomException(BackendErrorCodes.CANNOT_CREATE_PLAN_DOC_WITHOUT_PERIOD_ID);
        break;

      case DocumentSource.PAYROLL:
        if (
          !document.tags?.hash ||
          !document.tags?.payroll_data ||
          !document.tags?.payroll_id ||
          !document.tags?.object_workflow_key
        )
          throw new CustomException(
            BackendErrorCodes.CANNOT_CREATE_PAYROLL_DOC_WITHOUT_PAYROLL_DATA,
          );
        break;

      case DocumentSource.PAYMENT_REQUEST:
        if (!document.tags?.object_workflow_key)
          throw new CustomException(
            BackendErrorCodes.CANNOT_CREATE_PAYMENT_REQUEST_DOC_WITHOUT_PAYROLL_DATA,
          );
        break;

      case DocumentSource.CLOSURE:
        break;

      default:
        throw new CustomException(BackendErrorCodes.DOCUMENT_SOURCE_NOT_SUPPORTED);
    }
  }

  private async getPlanTags(clientId: string, planId?: string) {
    if (!planId) return undefined;

    const plan = await this.sheetDbApiService.getCommissionPlan(clientId, planId);

    return {
      tagValue: plan.payout_periods.map(period => String(period.id)),
    };
  }
}
