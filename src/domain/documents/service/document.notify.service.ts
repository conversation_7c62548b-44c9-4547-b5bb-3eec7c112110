import { Injectable, Logger } from '@nestjs/common';
import { CreditorRepository } from '../../repository/creditorRepository';
import { Document } from '../../../models/document';
import { UsersClaims } from 'shared-types';
import { <PERSON>redor } from '../../../models/credor';
import CreditorNotificationService from '../../../services/creditor.notification.service';
import { AuthenticationContextDto } from '../../../dto/authenticationContextDto';
import { Permission_Profile } from '../../../models/permission_profile';
import { UserPermissionRepository } from '../../repository/user.permission.repository';
import { TeamPermissionRepository } from '../../repository/team.permission.repository';
import { TeamsRepository } from '../../repository/teamsRepository';

@Injectable()
export class DocumentNotifyService {
  protected readonly logger = new Logger(DocumentNotifyService.name);

  constructor(
    private readonly creditorRepository: CreditorRepository,
    private readonly teamsRepository: TeamsRepository,
    private readonly creditorNotificationService: CreditorNotificationService,
    private readonly teamPermissionRepository: TeamPermissionRepository,
    private readonly userPermissionRepository: UserPermissionRepository,
  ) {}

  async notifyUploadedDocuments(
    officeId: number,
    createdDocuments: Document[],
    creditorsMap: Map<string, Credor>,
  ) {
    // owner_creditor -> creditors_to_notify
    const emailsToNotifyMap = new Map<string, string[]>();

    const emailsSearchPromise = createdDocuments.map(async doc => {
      const creditor = creditorsMap.get(doc.credor.id_credor_externo);

      // creditor already on emailsToNotify map
      if (emailsToNotifyMap.has(creditor.id_credor_externo)) return;

      // build auth context for doc owner
      const authCtx = await AuthenticationContextDto.BuildFromCreditor(creditor, null);

      // get document claim from the document owner ctx
      const [claimValue, hasClaim] = authCtx.findSessionClaim(UsersClaims.DOCUMENT_MANAGEMENT);

      if (!hasClaim || !claimValue) return;

      const { profiles_to_notify_on_upload } = claimValue;

      const hasNotifyItems = profiles_to_notify_on_upload && profiles_to_notify_on_upload.length;

      if (!hasNotifyItems) return;

      const usersToNotify = await this.getUsersFromProfileNames(
        officeId,
        profiles_to_notify_on_upload,
      );

      this.logger.debug(
        { usersToNotify },
        `users to notify for ${creditor.id_credor_externo} doc upload`,
      );

      // save all creditors to notify
      emailsToNotifyMap.set(
        creditor.id_credor_externo,
        usersToNotify.map(user => user.id_credor_externo),
      );
    });

    await Promise.all(emailsSearchPromise);

    const userIdsToNotify = Array.from(new Set(Array.from(emailsToNotifyMap.values()).flat()));

    const usersToNotify = await this.creditorRepository.getCreditorsMap({
      officeId,
      externalCreditorIds: userIdsToNotify,
    });

    // map all documents by creditor to notify by date (each notification will be by date, for each creditor)
    const docsPerNotifiedUser = new Map<Credor, Record<string, Document[]>>();
    createdDocuments.map(doc => {
      const docOwner = doc.credor;

      const creditorsToNotify = emailsToNotifyMap.get(docOwner.id_credor_externo);

      creditorsToNotify?.map(idToNotify => {
        const creditor = usersToNotify.get(idToNotify);

        const perDayDocs = docsPerNotifiedUser.get(creditor) ?? {};
        const docs = perDayDocs[doc.date] ?? [];
        docs.push(doc);
        perDayDocs[doc.date] = docs;
        docsPerNotifiedUser.set(creditor, perDayDocs);
      });
    });

    // now that all documents are organized for each userToNotify, count each docOwner documents
    const emailData = new Map<
      Credor,
      { ownerInfo: { documentOwner: string; amount: number }[]; date: string }[]
    >();
    docsPerNotifiedUser.forEach((perDayDocs, creditor) => {
      Object.entries(perDayDocs).map(([date, docs]) => {
        const ownerCount: Record<string, number> = {};

        docs.map(doc => {
          const owner = doc.credor.id_credor_externo;
          ownerCount[owner] = ownerCount[owner] ? ownerCount[owner] + 1 : 1;
        });

        const ownerInfo = Object.entries(ownerCount).map(([owner, amount]) => {
          return { documentOwner: owner, amount };
        });

        const emailDataPerDay = emailData.get(creditor) ?? [];
        emailDataPerDay.push({ ownerInfo, date });
        emailData.set(creditor, emailDataPerDay);
      });
    });

    // flatening the map to send all notifications
    const notifications = Array.from(emailData.entries()).flatMap(([creditor, datesData]) => {
      return datesData.map(data => {
        return { data, creditorToNotify: creditor };
      });
    });

    // send notifications
    const sendPromises = notifications.map(async ({ creditorToNotify, data }) =>
      this.creditorNotificationService.notifyUploadedDocuments(creditorToNotify, data),
    );

    await Promise.all(sendPromises);
  }

  private async getUsersFromProfileNames(officeId: number, profileNames: string[]) {
    const claimProfiles = await Permission_Profile.findAll({
      where: {
        office_id: officeId,
        name: profileNames,
      },
    });

    const promises = claimProfiles.map(profile => this.getUsersFromProfile(officeId, profile.id));

    const creditors = (await Promise.all(promises)).flatMap(c => c.creditors);
    return creditors;
  }

  private async getUsersFromProfile(
    officeId: number,
    profileId: number,
  ): Promise<{ creditors: Credor[] }> {
    const teamIds = await this.teamPermissionRepository.findAllTeamPermission({
      permission_profile_id: profileId,
    });

    const userIds = await this.userPermissionRepository.findAllUserPermission({
      permission_profile_id: profileId,
    });

    const teams = await this.teamsRepository.getTeams({ id: teamIds.map(t => t.team_id) });
    const creditorOnTeams = await this.creditorRepository.getCreditorsOnTeams(
      officeId,
      teams.map(t => t.name),
    );

    const allUserIds = userIds
      .map(u => u.creditor_id)
      .concat(creditorOnTeams.map(c => c.id_credor));

    if (!allUserIds.length) return { creditors: [] };

    const creditors = await this.creditorRepository.getCreditors({
      id_credor: allUserIds,
    });

    return { creditors };
  }
}
