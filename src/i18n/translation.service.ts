import { Injectable } from '@nestjs/common';
import { resolveLocale } from 'shared-types';
import { getTranslations } from './locales';

export const TranslationPrefixes: Record<string, string> = {
  email_creditor_period_notification: 'email.creditor_period_notification',
};

@Injectable()
export class TranslationService {
  private translations: Record<string, any> = {};

  constructor() {
    this.translations = getTranslations();
  }

  translate(lang: string, key: string, args: Record<string, string> = {}): string {
    const selectedLang = resolveLocale(lang);
    const keys = key.split('.');
    let translation: any = this.translations[selectedLang];

    for (const k of keys) {
      translation = translation?.[k];
      if (!translation) return key;
    }

    return translation.replace(/\{\{(.*?)\}\}/g, (_, varName) => args[varName.trim()] || '');
  }

  getBulkTranslation(
    baseKey: string,
    variables?: Record<string, string>,
    language?: string,
  ): Record<string, string> {
    const resolvedLanguage = resolveLocale(language);
    const translationSet = this.getTranslationSet(resolvedLanguage, baseKey);
    if (!translationSet) return {};

    return Object.keys(translationSet).reduce(
      (result, key) => {
        result[key] = this.translate(resolvedLanguage, `${baseKey}.${key}`, variables);
        return result;
      },
      {} as Record<string, string>,
    );
  }

  private getTranslationSet(language: string, baseKey: string): Record<string, string> | null {
    return (
      baseKey
        .split('.')
        .reduce((acc, key) => (acc ? acc[key] : null), this.translations[language]) ?? null
    );
  }
}
