import { z } from 'zod';

const BaseRoleConfig = z.object({
  role: z.string(),
  initial_profiles: z.array(z.string()).optional(),
  initial_teams: z.array(z.string()).optional(),
  create_user: z.boolean().optional().default(false),
});

const AdminRoleConfig = BaseRoleConfig.extend({
  admin_only: z.literal(true),
});

const NonAdminRoleConfigSchema = BaseRoleConfig.extend({
  admin_only: z.literal(false).optional(),
  user_info_matches: z.array(
    z.object({
      claim: z.string(),
      value: z.array(z.string()),
    }),
  ),
  allowed_tenants_matcher: z.string(),
});

export type NonAdminRoleConfig = z.infer<typeof NonAdminRoleConfigSchema>;

export const isRequiredAdminRole = (
  config: RoleConfig,
): config is z.infer<typeof AdminRoleConfig> => {
  return config.admin_only === true;
};

const RoleConfigSchema = z.discriminatedUnion('admin_only', [
  AdminRoleConfig,
  NonAdminRoleConfigSchema,
]);
export type RoleConfig = z.infer<typeof RoleConfigSchema>;

export enum RoleMatchType {
  FIRST_MATCH = 'first_match',
}

export const OrgConfigSchema = z.object({
  role_match_type: z.nativeEnum(RoleMatchType).default(RoleMatchType.FIRST_MATCH),
  roles_config: z.array(RoleConfigSchema),
});

export type OrgConfig = z.infer<typeof OrgConfigSchema>;
