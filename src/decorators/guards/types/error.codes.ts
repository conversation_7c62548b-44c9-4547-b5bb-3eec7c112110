// do not move to shared types, as it is an enum used only for testing purposes
export enum GuardErrors {
  JW<PERSON>_KEY_NOT_FOUND_ERROR,
  TOKEN_EXPIRED_ERROR,
  OIDC_NOT_FOUND,
  JWT_NOT_FOUND,
  JWT_INVALID,
  JWT_INVALID_ISSUER,
  USER_NOT_FOUND,
  INVALID_IP,
  DEBUG_PREVENT_LOGIN,
  ERROR_READ_JWT,
  CREDITOR_NOT_IDENTIFIED,
  NOT_SERVICE_ACCOUNT,
  REQ_JWT_OR_ID_NOT_FOUND,
  MISSING_PROVIDER_ID,
  ORG_HASNT_TENANT,
  ORG_HASNT_TENANT_SELECTED,
  UNSUPPORTED_OIDC_IDENTIFIER_TYPE,
  OIDC_IDENTIFIER_TYPE_REQUIRED,
  USER_HASNT_ALLOWED_TENANT,
  SELECTED_TENANT_NOT_FOUND,
  EMAIL_NOT_ALLOWED,
  EMAIL_IS_REQUIRED_FOR_LOGIN_DEFAULT,
  OFFICE_ID_IS_REQUIRED_IN_OIDC_OPTS,
}
