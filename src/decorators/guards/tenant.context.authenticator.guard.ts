import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import type { Request } from 'express';
import { trace } from '@opentelemetry/api';
import { RouteMetrics } from '../../prometheus.module';
import { ConfigurationEnv } from '../../config/configuration.env';
import { GuardErrors } from './types/error.codes';
import { IMPERSONATE_OFFICE_TENANT_HEADER } from 'shared-types';
import { UserDefaultContext } from '../../auth.contexts/tenant/user.default.context';
import { OrgContext } from '../../auth.contexts/tenant/org.context';
import {
  TenantBaseCtxProps,
  TenantsBaseContext,
} from '../../auth.contexts/tenant/tenant.base.context';
import { UserCustomOidcContext } from '../../auth.contexts/tenant/user.custom.context';
import { CustomerServiceAccountContext } from '../../auth.contexts/tenant/customer.service.account.context';

/**
 * Guard responsible for get allowed tenants, user info for logged user.
 *
 * Usage:
 *  1. Use TenantContextAuthenticatorGuard so that set req.tenants_ctx with the correct context
 *  The {@link TenantAuthenticated} and {@link Authenticated} decorator applies this guard
 */
@Injectable()
export class TenantContextAuthenticatorGuard implements CanActivate {
  private readonly logger = new Logger(TenantContextAuthenticatorGuard.name);
  constructor(
    private readonly metrics: RouteMetrics,
    private readonly configuration: ConfigurationEnv,
  ) {}

  canActivate(context: ExecutionContext): Promise<boolean> {
    const startTime = new Date();
    const tracer = trace.getTracerProvider().getTracer('tenant.authenticator.guard');

    return tracer.startActiveSpan('tenant.authenticator.guard.canActivate', async span => {
      try {
        if (!this.configuration.tokenValidationEnabled) {
          // ONLY FOR TESTS
          return true;
        }

        const req = context.switchToHttp().getRequest<Request>();
        if (!req.jwt || !req.oidc) {
          this.logger.warn('req.jwt and/or req.oidc must be filled out in advance');
          throw new UnauthorizedException(GuardErrors.REQ_JWT_OR_ID_NOT_FOUND);
        }

        const tenantCtx = await this.loadTenantCtx(req);

        req.tenants_ctx = tenantCtx;

        return true;
      } finally {
        const endTime = new Date();
        const takenMs = this.metrics.recordAuthorizerTime(startTime, endTime);

        this.logger.debug({ takenMs }, 'authorizer time');

        span.end();
      }
    });
  }

  private loadTenantCtx(req: Request): Promise<TenantsBaseContext> {
    const { oidc, jwt, headers } = req;
    const requestBySplitcServiceAccount = oidc.checkIfIsServiceAccount(jwt);
    if (requestBySplitcServiceAccount) return;

    const userInfo = oidc.getUserInfo(jwt);
    const selectedTenant = Array.isArray(headers[IMPERSONATE_OFFICE_TENANT_HEADER])
      ? headers[IMPERSONATE_OFFICE_TENANT_HEADER][0]
      : headers[IMPERSONATE_OFFICE_TENANT_HEADER];

    const payload = {
      oidc,
      userInfo,
      tenantId: selectedTenant,
    };
    const requestByCustomerServiceAccount = oidc.checkIfCustomerServiceAccount(
      this.configuration.oauthIssuer,
      jwt,
    );
    if (requestByCustomerServiceAccount) {
      return CustomerServiceAccountContext.Build(payload);
    }

    const requestByUserOrg = !!oidc.org_id;
    if (requestByUserOrg) {
      return OrgContext.BuildFromUser(payload);
    }

    const requestByUserCustomOidcUser = !oidc.org_id && !oidc.default;
    if (requestByUserCustomOidcUser) {
      return UserCustomOidcContext.Build(payload);
    }

    // all oidc default, identify from email
    return UserDefaultContext.Build(payload);
  }
}
