import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { Span, trace } from '@opentelemetry/api';
import type { Request } from 'express';
import {
  IMPERSONATE_USER_HEADER,
  IMPERSONATE_USER_OFFICE_ID_HEADER,
  UsersClaims,
  USER_LOCALE_HEADER,
} from 'shared-types';
import { UserLoader, USER_LOADER_INTERFACE } from '../../domain/types';
import { AuthenticationContextDto } from '../../dto/authenticationContextDto';
import { Credor } from '../../models/credor';
import { Escritorio } from '../../models/escritorio';
import { RouteMetrics } from '../../prometheus.module';
import { GuardErrors } from './types/error.codes';

/**
 * This Guard is responsible for:
 * 1. Loads user's corresponding {@link Credor}, {@link Escritorio} and {@link Atributos_credor} into the request.user object
 * 2. If the Credor object isn't in active state, returns Forbidden
 *
 * ## Usage:
 *  Just use @UseGuards(CreditorAuthenticatorGuard) on the @Controller or handler methods
 *  Or {@link Authenticated} that applies this guard.
 */
@Injectable()
export class CreditorAuthenticatorGuard implements CanActivate {
  private readonly logger = new Logger(CreditorAuthenticatorGuard.name);

  constructor(
    @Inject(USER_LOADER_INTERFACE) private readonly loginDomain: UserLoader,
    private readonly metrics: RouteMetrics,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const tracer = trace.getTracerProvider().getTracer('user authenticator guard');
    return tracer.startActiveSpan('user.authenticator.guard.canActivate', async span =>
      this.canActivateWithTracing(span, context),
    );
  }

  private async canActivateWithTracing(span: Span, context: ExecutionContext): Promise<boolean> {
    const startTime = new Date();
    try {
      const request = context.switchToHttp().getRequest<Request>();
      await this.identifyUser(request);
      this.validateHeaders(request);
      return true;
    } finally {
      const endTime = new Date();
      const takenMs = this.metrics.recordAuthorizerTime(startTime, endTime);

      this.logger.debug({ takenMs }, 'authorizer time');
      span.end();
    }
  }

  private async identifyUser(request: Request): Promise<void> {
    const userAuthenticationContext = await this.loginDomain.loadAuthenticationContext(request);

    if (!userAuthenticationContext || !userAuthenticationContext.credor.ativo) {
      this.logger.debug('Invalid Credor state');
      throw new UnauthorizedException(GuardErrors.CREDITOR_NOT_IDENTIFIED);
    }

    request.user = userAuthenticationContext;

    await this.impersonateUserIfAllowed(request, userAuthenticationContext);

    this.logger.debug({
      hasImpersonationHeader: !!request.headers[IMPERSONATE_USER_HEADER],
      isServiceAccount: userAuthenticationContext.isServiceAccount,
    });
  }

  private validateHeaders(request: Request): void {
    const { headers } = request;

    if (headers[IMPERSONATE_USER_OFFICE_ID_HEADER] && !request.user.isServiceAccount)
      throw new UnauthorizedException(GuardErrors.NOT_SERVICE_ACCOUNT);
  }

  private async impersonateUserIfAllowed(
    request: Request,
    currentUser: AuthenticationContextDto,
  ): Promise<void> {
    const impersonatingOtherUser = request.headers[IMPERSONATE_USER_HEADER];
    const impersonatingUser = Array.isArray(impersonatingOtherUser)
      ? impersonatingOtherUser[0]
      : impersonatingOtherUser;

    if (!impersonatingUser || currentUser.isServiceAccount) return;

    const [allowed, foundClaim] = currentUser.findSessionClaim(
      UsersClaims.ALLOW_USER_IMPERSONATION,
    );

    const canImpersonate = allowed && foundClaim;
    if (!canImpersonate) return; // if current user cant impersonate others, dont load

    const canSeeTargetCreditor = await currentUser.canSeeCreditor(impersonatingUser);
    if (!canSeeTargetCreditor) return; // if logged user cant see the creditor hes trying to impersonate, dont load

    const impersonated = await Credor.findOne({
      where: {
        id_credor_externo: impersonatingUser,
        escritorio_id_escritorio: currentUser.credor.escritorio_id_escritorio,
        ativo: true,
      },
      include: [Escritorio],
    });

    if (!impersonated || !impersonated.ativo) return; // dont impersonate if user doesnt exist or isnt active

    const tryingToImpersonateBackofficeUser = AuthenticationContextDto.HasBackofficeEmailPattern(
      impersonated.email,
    );

    // don't let users impersonate backoffice users
    if (tryingToImpersonateBackofficeUser)
      throw new ForbiddenException('Impersonate backoffice users is not allowed');

    // everything checks out, let him impersonate other user
    const newAuthContext = await AuthenticationContextDto.BuildFromCreditor(
      impersonated,
      currentUser.accessToken,
      currentUser.getProvider(),
      null,
    );
    newAuthContext.setOriginalUser(currentUser);
    request.user = newAuthContext;
  }
}
