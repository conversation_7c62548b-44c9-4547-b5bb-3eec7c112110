import { trace, Span } from '@opentelemetry/api';
import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import type { Request } from 'express';
import { getClientIp } from 'request-ip';
import { ConfigurationEnv } from '../../config/configuration.env';
import { Oidc_Provider } from '../../models/oidc_provider';
import { JwtPart, JwtUtils } from '../../utils/jwt.utils';
import { isIp4InCidrs } from '../../utils/string.utils';
import { SharedMethodsAuthorization } from './shared.methods.authenticator';
import { RouteMetrics } from '../../prometheus.module';
import { GuardErrors } from './types/error.codes';

export const VERIFY_TOKEN_ERRORS = {
  JWKS_KEY_NOT_FOUND_ERROR: 'JwksKeyNotFoundError',
  TOKEN_EXPIRED_ERROR: 'TokenExpiredError',
} as const;

/**
 * This Guard is responsible for:
 * 1. Seeing if the request has an access token. If it doesn't, it returns Forbidden
 * 2. Validates the access token
 * 4. Set req.jwt with access token and req.oidc with logged oidc
 *
 * ## Usage:
 *  Just use {@link Authenticated} that applies this guard.
 *  or {@link OrgAuthenticated} that applies this guard.
 */
@Injectable()
export class AuthenticatorGuard implements CanActivate {
  private readonly logger = new Logger(AuthenticatorGuard.name);

  constructor(
    private readonly shared: SharedMethodsAuthorization,
    private readonly configuration: ConfigurationEnv,
    private readonly metrics: RouteMetrics,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const tracer = trace.getTracerProvider().getTracer('testing');

    return tracer.startActiveSpan('authenticator.guard.canActivate', span =>
      this.canActivateWithTracing(span, context),
    );
  }

  private async canActivateWithTracing(span: Span, context: ExecutionContext): Promise<boolean> {
    const startTime = new Date();

    try {
      const { jwt, request } = this.shared.extractJwtToken(context);
      if (!this.configuration.tokenValidationEnabled) {
        // only for test
        request.jwt = jwt;
        return true;
      }

      const provider = await this.findOidcProvider(jwt);
      this.validateIp(request, provider);

      const isValidAccessToken = provider.validateAccessToken(jwt, this.configuration);
      if (!isValidAccessToken) {
        this.logger.error('invalid access token');
        throw new UnauthorizedException(GuardErrors.JWT_INVALID);
      }

      request.jwt = jwt;
      request.oidc = provider;

      return true;
    } finally {
      const endTime = new Date();
      const takenMs = this.metrics.recordAuthorizerTime(startTime, endTime);

      this.logger.debug({ takenMs }, 'authorizer time');

      span.end();
    }
  }

  private async findOidcProvider(jwt: string): Promise<Oidc_Provider> {
    if (!jwt) {
      this.logger.error('jwt not found');
      throw new UnauthorizedException(GuardErrors.JWT_NOT_FOUND);
    }
    const { iss, aud } = this.readJwtPart(jwt, JwtPart.BODY);
    if (!iss) {
      this.logger.error('cant load provider without the jwt issuer');
      throw new UnauthorizedException(GuardErrors.JWT_INVALID_ISSUER);
    }

    const oidc = await Oidc_Provider.findByIssuer(iss, aud);

    if (!oidc) {
      this.logger.warn({ iss }, 'tried to login without provider');
      throw new UnauthorizedException(GuardErrors.OIDC_NOT_FOUND);
    }

    return oidc;
  }

  private validateIp(request: Request, oidc: Oidc_Provider): void {
    if (!oidc.opts?.src_ip_ranges || oidc.opts.src_ip_ranges.length <= 0) return;
    const currentIp = getClientIp(request);
    if (!isIp4InCidrs(currentIp, oidc.opts.src_ip_ranges)) {
      this.logger.warn(
        { allowed: oidc.opts.src_ip_ranges, currentIp },
        'tried to access from invalid ip address',
      );
      throw new UnauthorizedException(GuardErrors.INVALID_IP);
    }
  }

  private readJwtPart(jwt: string, part: JwtPart) {
    try {
      return JwtUtils.readJwtPart(jwt, part);
    } catch (err) {
      this.logger.warn(err, 'Error reading jwt part');
      throw new UnauthorizedException(GuardErrors.ERROR_READ_JWT);
    }
  }
}
