import { InternalServerErrorException, Logger, UnauthorizedException } from '@nestjs/common';
import { Transaction } from 'sequelize';
import { User } from '../../models/user';
import { Escritorio } from '../../models/escritorio';
import {
  isRequiredAdminRole,
  NonAdminRoleConfig,
  OrgConfigSchema,
} from '../../types/models/organization';
import { GuardErrors } from '../../decorators/guards/types/error.codes';
import { OptsIdentifierType, UserInfo } from '../../models/oidc_provider';
import { Credor } from '../../models/credor';
import { validateRegex } from '../../utils/string.utils';
import { TenantsBaseContext, TenantBaseCtxProps } from './tenant.base.context';
import { normalizeString } from 'shared-types';

type OrgAuthProps = {
  user?: User;
} & TenantBaseCtxProps;

type AllowedCreditors = {
  creditor: Credor;
  exists: boolean;
};
export class OrgContext extends TenantsBaseContext {
  protected readonly logger = new Logger(OrgContext.name);

  private allowedTenantsMatcher?: string;
  private _shouldCreateCreditor?: boolean;
  private _initialTeams?: string[];
  private _initialProfiles?: string[];

  private user: User;

  constructor(props: OrgAuthProps) {
    super(props);
  }

  static isInstance(ctx: TenantsBaseContext): ctx is OrgContext {
    return ctx instanceof OrgContext;
  }

  public get shouldCreateCreditor(): boolean {
    return this._shouldCreateCreditor;
  }

  public get initialProfiles(): string[] {
    return this._initialProfiles;
  }

  public get initialTeams(): string[] {
    return this._initialTeams;
  }

  public get userName() {
    return this.user.name ?? this.userInfo.name ?? '';
  }

  public get isUserOrg(): boolean {
    return true;
  }

  static async BuildFromUser(
    orgAuthProps: OrgAuthProps,
    transaction?: Transaction,
  ): Promise<OrgContext> {
    const userOrgContext = new OrgContext(orgAuthProps);
    await userOrgContext.load(transaction);
    return userOrgContext;
  }

  public getSelectedCreditor(): Credor {
    const selectedCreditor = this.allowedCreditors.find(
      c => c.creditor.escritorio_id_escritorio === Number(this.selectedTenant),
    )?.creditor;
    return selectedCreditor;
  }

  protected async load(transaction?: Transaction): Promise<void> {
    const providerId = this.userInfo.provider_id;
    if (!providerId) {
      this.logger.warn(
        { oidc_id: this.oidc.id, user_info: this.userInfo },
        'provider_id claim is required',
      );
      throw new UnauthorizedException(GuardErrors.MISSING_PROVIDER_ID);
    }

    const user = await User.getUserByOidc(this.oidc, providerId);
    if (!user) {
      this.logger.warn({ oidc_id: this.oidc.id, user_info: this.userInfo }, 'user not found');
      throw new UnauthorizedException(GuardErrors.USER_NOT_FOUND);
    }

    this.user = user;
    this.fillRoleConfig();

    const tenants = await Escritorio.findAll({
      attributes: ['id_escritorio', 'nome_escritorio'],
      where: { org_id: this.user.org_id },
    });

    const tenantsMatched = this.getFilteredTenantsByConfig(tenants);

    const creditors = await this.loadAllowedCreditors(
      tenantsMatched,
      this.userInfo,
      this.oidc.opts?.identifier_type,
      transaction,
    );

    this._allowedTenants = creditors.map(c => ({
      id: c.creditor.escritorio.id_escritorio,
      name: c.creditor.escritorio.nome_escritorio,
    }));
    this.allowedCreditors = creditors;
  }

  private getFilteredTenantsByConfig(tenants: Escritorio[]): Escritorio[] {
    if (this.user.is_admin) return tenants; // ignore filter when user is admin
    if (!this.allowedTenantsMatcher) return [];

    return tenants.filter(tenant => {
      return validateRegex(this.allowedTenantsMatcher, tenant.nome_escritorio);
    });
  }

  private async loadAllowedCreditors(
    tenants: Escritorio[],
    userInfo: UserInfo,
    identifier: OptsIdentifierType,
    transaction?: Transaction,
  ): Promise<AllowedCreditors[]> {
    if (!tenants?.length) return [];

    if (!identifier) {
      this.logger.warn(
        'tried to get creditor from oidc with organization, but identifier_type is not provided',
      );
      throw new UnauthorizedException(GuardErrors.OIDC_IDENTIFIER_TYPE_REQUIRED);
    }

    if (identifier === 'provider_id') {
      this.logger.warn(
        'tried to login from organization, but identifier_type equal provider_id is not supported',
      );
      throw new UnauthorizedException(GuardErrors.UNSUPPORTED_OIDC_IDENTIFIER_TYPE);
    }

    const tenantIds = tenants.map(e => e.id_escritorio);
    const existingCreditors = await Credor.identifyAllowedCreditors(
      tenantIds,
      userInfo,
      identifier,
      {
        transaction,
      },
    );
    const finalCreditorsList: AllowedCreditors[] = existingCreditors.map(c => ({
      creditor: c,
      exists: true,
    }));

    if (!this._shouldCreateCreditor) return finalCreditorsList;

    const existingTenants = new Set<number>(
      finalCreditorsList.map(c => c.creditor.escritorio_id_escritorio),
    );

    tenants.forEach(tenant => {
      const userExists = existingTenants.has(tenant.id_escritorio);
      if (userExists) return;

      const externalCreditorId = userInfo.creditor_id ?? userInfo.provider_id;
      const creditor = Credor.build({
        escritorio_id_escritorio: tenant.id_escritorio,
        escritorio: tenant,
        id_credor_externo: externalCreditorId,
        email: userInfo.email,
        ativo: false,
        nome_credor: userInfo.name ?? externalCreditorId,
      });
      creditor.escritorio = tenant;
      finalCreditorsList.push({
        creditor,
        exists: false,
      });
    });

    return finalCreditorsList;
  }

  private fillRoleConfig(): void {
    if (!this.user.organization?.config) return;

    const {
      success,
      data: config,
      error,
    } = OrgConfigSchema.safeParse(this.user.organization?.config);
    if (!success) {
      this.logger.warn({ config: this.user.organization?.config, error }, 'invalid org config');
      throw new InternalServerErrorException('invalid org configuration');
    }

    const rolesConfig = config.roles_config;

    if (this.user.is_admin) {
      const roleConfig = rolesConfig.find(isRequiredAdminRole);
      this._initialProfiles = roleConfig?.initial_profiles;
      this._initialTeams = roleConfig?.initial_teams;
      this._shouldCreateCreditor = true;
      return;
    }

    let roleConfig: NonAdminRoleConfig;
    for (const conf of rolesConfig) {
      if (isRequiredAdminRole(conf)) continue;

      const matches = conf.user_info_matches.every(match => {
        const claimValue = this.userInfo[match.claim];
        if (!claimValue) return false;
        return match.value.some(
          mathVal => normalizeString(claimValue) === normalizeString(mathVal) || mathVal === '*',
        );
      });
      if (!matches) continue;
      roleConfig = conf;
      break;
    }

    this._initialProfiles = roleConfig?.initial_profiles;
    this._initialTeams = roleConfig?.initial_teams;
    this.allowedTenantsMatcher = roleConfig?.allowed_tenants_matcher;
    this._shouldCreateCreditor = !!roleConfig?.create_user;
  }
}
