import { Escritorio } from '../../models/escritorio';
import { AuthenticationContextDto } from '../../dto/authenticationContextDto';
import { TenantsBaseContext, TenantBaseCtxProps } from './tenant.base.context';
import { Credor } from '../../models/credor';
import { Logger, UnauthorizedException } from '@nestjs/common';
import { GuardErrors } from '../../decorators/guards/types/error.codes';

export class UserDefaultContext extends TenantsBaseContext {
  private readonly logger = new Logger(UserDefaultContext.name);

  constructor(props: TenantBaseCtxProps) {
    super(props);
  }

  static async Build(props: TenantBaseCtxProps): Promise<UserDefaultContext> {
    const userDefaultContext = new UserDefaultContext(props);
    await userDefaultContext.load();
    return userDefaultContext;
  }

  protected async load(): Promise<void> {
    if (!this.userInfo.email) {
      this.logger.warn(
        { oidc_id: this.oidc.id, user_info: this.userInfo },
        'email claim is required',
      );
      throw new UnauthorizedException(GuardErrors.EMAIL_IS_REQUIRED_FOR_LOGIN_DEFAULT);
    }
    const email = this.userInfo.email;

    const isBackoffice = AuthenticationContextDto.HasBackofficeEmailPattern(email);

    const findOptionsBackoffice = {
      where: { email },
      include: [Escritorio],
    };
    const findOptions = {
      where: { email, ativo: true, deleted_at: null },
      include: [
        {
          model: Escritorio,
          where: {
            org_id: null,
          },
        },
      ],
    };
    const creditors = await Credor.findAll(isBackoffice ? findOptionsBackoffice : findOptions);

    this._allowedTenants = creditors.map(c => ({
      id: c.escritorio.id_escritorio,
      name: c.escritorio.nome_escritorio,
    }));

    this.allowedCreditors = creditors.map(c => ({
      creditor: c,
      exists: true,
    }));
  }
}
