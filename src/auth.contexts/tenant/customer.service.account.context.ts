import { <PERSON><PERSON><PERSON> } from '../../models/credor';
import { Logger, UnauthorizedException } from '@nestjs/common';
import { GuardErrors } from '../../decorators/guards/types/error.codes';
import { TenantBaseCtxProps, TenantsBaseContext } from './tenant.base.context';
import { Escritorio } from '../../models/escritorio';

/** Customer service account is login from customer using some cognito service account**/
export class CustomerServiceAccountContext extends TenantsBaseContext {
  private readonly logger = new Logger(CustomerServiceAccountContext.name);

  constructor(props: TenantBaseCtxProps) {
    super(props);
  }

  static async Build(props: TenantBaseCtxProps): Promise<CustomerServiceAccountContext> {
    const ctx = new CustomerServiceAccountContext(props);
    await ctx.load();
    return ctx;
  }

  protected async load(): Promise<void> {
    /* identify creditor from provider_id */
    const userProviderId = this.userInfo.provider_id;
    if (!userProviderId) {
      this.logger.warn(
        { oidc_id: this.oidc.id, user_info: this.userInfo },
        'tried to get creditor from provider_id, but userInfo.provider_id is not provided',
      );
      throw new UnauthorizedException(GuardErrors.MISSING_PROVIDER_ID);
    }

    const creditor = await Credor.findOne({
      where: {
        provider_id: userProviderId,
        deleted_at: null,
        ativo: true,
      },
      include: [Escritorio],
    });

    if (!creditor) {
      this.logger.warn({ oidc_id: this.oidc.id, user_info: this.userInfo }, 'creditor not found');
      throw new UnauthorizedException(GuardErrors.CREDITOR_NOT_IDENTIFIED);
    }

    this._allowedTenants = [
      {
        id: creditor.escritorio.id_escritorio,
        name: creditor.escritorio.nome_escritorio,
      },
    ];

    this.allowedCreditors = [
      {
        creditor: creditor,
        exists: true,
      },
    ];

    this.logger.debug(
      {
        oidc_id: this.oidc.id,
        creditor_id: creditor.id_credor,
      },
      'build customer service account ctx log...',
    );
  }
}
