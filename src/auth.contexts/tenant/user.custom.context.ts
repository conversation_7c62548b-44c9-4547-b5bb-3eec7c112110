import { <PERSON>redor } from '../../models/credor';
import { Logger, UnauthorizedException } from '@nestjs/common';
import { GuardErrors } from '../../decorators/guards/types/error.codes';
import { TenantBaseCtxProps, TenantsBaseContext } from './tenant.base.context';

/** User Custom is login from oidc outside org and is not oidc default  **/
export class UserCustomOidcContext extends TenantsBaseContext {
  private readonly logger = new Logger(UserCustomOidcContext.name);

  constructor(props: TenantBaseCtxProps) {
    super(props);
  }

  static async Build(props: TenantBaseCtxProps): Promise<UserCustomOidcContext> {
    const userCustomOidcContext = new UserCustomOidcContext(props);
    await userCustomOidcContext.load();
    return userCustomOidcContext;
  }

  protected async load(): Promise<void> {
    /* custom oidc outside org, must have opts.office_id setted */
    const officeId = this.oidc.opts?.office_id || this.userInfo.office_id;
    if (!officeId) {
      this.logger.warn(
        { oidc_id: this.oidc.id, user_info: this.userInfo },
        'tried to get creditor from oidc, but opts.office_id is not provided',
      );
      throw new UnauthorizedException(GuardErrors.OFFICE_ID_IS_REQUIRED_IN_OIDC_OPTS);
    }

    const identifierType = this.oidc.opts.identifier_type;

    if (identifierType === 'provider_id') {
      this.logger.warn(
        { oidc_id: this.oidc.id, user_info: this.userInfo },
        'provider_id unsupported oidc identifier type',
      );
      throw new UnauthorizedException(GuardErrors.UNSUPPORTED_OIDC_IDENTIFIER_TYPE);
    }

    const creditors = await Credor.identifyAllowedCreditors(
      [Number(officeId)],
      this.userInfo,
      identifierType,
    );

    this._allowedTenants = creditors.map(c => ({
      id: c.escritorio.id_escritorio,
      name: c.escritorio.nome_escritorio,
    }));

    this.allowedCreditors = creditors.map(c => ({
      creditor: c,
      exists: true,
    }));

    this.logger.debug(
      {
        oidc_id: this.oidc.id,
        identifier_type: this.oidc.opts?.identifier_type,
        creditors_found: creditors.length,
        allowed_tenants: this._allowedTenants,
      },
      'build tenant base ctx log...',
    );
  }
}
