import { Injectable, Logger } from '@nestjs/common';
import { AuthenticationContextDto } from '../../dto/authenticationContextDto';
import { Payroll_Data } from '../../models/payroll_data';
import { Payroll } from '../../models/payroll';
import {
  BackendErrorCodes,
  ClaimSchemaType,
  GetPayrollGroupPermissions,
  GetPayrollResponseGroupings,
  UsersClaims,
} from 'shared-types';
import { Op, Sequelize } from 'sequelize';
import { Payroll_Group } from '../../models/payroll_group';
import { PermissionProfileRepository } from '../../domain/repository/permission.profile.repository';
import { MapUtils } from '../../utils/map.utils';
import { CustomException } from '../../utils/error.utils';
import { stripUndefinedAttributes } from '../../utils/object.utils';

@Injectable()
export class PayrollGroupService {
  private readonly logger = new Logger(PayrollGroupService.name);
  private readonly DEFAULT_PAYROLL_GROUP = {
    id: -1,
    label: 'Grupo padrão',
    payroll_count: 1,
  };

  constructor(private readonly permissionProfileRepository: PermissionProfileRepository) {}

  /**
   * Checks if the requester can see the group based on their permissions.
   * group_slug undefined means the default group.
   */
  async checkIfCanSeeGroup(requester: AuthenticationContextDto, group_slug: string | undefined) {
    const officeId = requester.escritorio.id_escritorio;
    const group = await Payroll_Group.findOne({
      where: {
        office_id: officeId,
        slug: group_slug ?? null,
      },
    });

    const groupsThatCanSee = await this.getGroupsThatCanSee(requester, officeId);

    const checkByDefaultPayrollGroup = group_slug === undefined;
    const groupsMapById = MapUtils.arrayToMap(groupsThatCanSee, 'id');
    const canSeeGroup = groupsMapById.get(
      checkByDefaultPayrollGroup ? this.DEFAULT_PAYROLL_GROUP.id : group.id,
    );
    if (!canSeeGroup) {
      throw new CustomException(BackendErrorCodes.PAYROLL_GROUP_NOT_ALLOWED, {
        slug: group_slug,
      });
    }
  }

  async getPermissions(
    officeId: number,
    group_slug: string,
  ): Promise<GetPayrollGroupPermissions[]> {
    const permissionProfiles =
      await this.permissionProfileRepository.getPermissionProfilesWithFinalPermissions({
        where: { office_id: officeId },
      });

    // iterate checking if the profile has the PAYROLL_GROUP_PERMISSION with this group
    const profilesToReturn: GetPayrollGroupPermissions[] = [];
    permissionProfiles.forEach(permProfile => {
      permProfile.permission_data.forEach(permission => {
        if (permission.name !== UsersClaims.PAYROLL_GROUP_PERMISSIONS) return;
        const parsedValue: ClaimSchemaType<UsersClaims.PAYROLL_GROUP_PERMISSIONS> = JSON.parse(
          permission.value,
        );

        const hasGroupPermission = parsedValue.payroll_group_permissions.find(
          pgp => pgp.payroll_group_slug === group_slug,
        );

        if (!hasGroupPermission) return;

        profilesToReturn.push({
          id: permProfile.id,
          profileName: permProfile.name,
          permissions: parsedValue.payroll_group_permissions,
        });
      });
    });

    return profilesToReturn;
  }

  private formatGroup(group: {
    id: number | null;
    slug: string;
    label: string;
    payrolls?: any[];
    payroll_count?: number;
  }): GetPayrollResponseGroupings {
    if (group.id === null) {
      return this.DEFAULT_PAYROLL_GROUP;
    }

    return {
      id: group.id,
      slug: group.slug,
      label: group.label,
      payroll_count: group.payroll_count ?? group.payrolls?.length ?? 0,
    };
  }

  /**
   * Returns the groups the user can see based on PAYROLL_GROUP_PERMISSIONS claim.
   * If the user is a manager, it returns the full group with all payrolls.
   * It filters out groups where the user is a viewer (no roles) and only returns visible payrolls.
   */
  private getUserVisibleGroups(
    requester: AuthenticationContextDto,
    officeGroups: Payroll_Group[],
  ): GetPayrollResponseGroupings[] {
    const userGroupsPermissions = Payroll_Group.getUserGroupPermissions(requester);
    const groupMapById = MapUtils.arrayToMap<string, Payroll_Group>(officeGroups, 'slug');

    this.logger.debug(
      {
        groupPermissions: [...userGroupsPermissions.values()],
        groupPermissionsSize: userGroupsPermissions.size,
        officeGroups: officeGroups.map(g => g.slug),
      },
      'user group permissions',
    );

    const result: GetPayrollResponseGroupings[] = [];

    userGroupsPermissions.forEach(permission => {
      const group = groupMapById.get(permission.payroll_group_slug);
      if (!group) return;
      const isManager = Payroll_Group.isGranularManager(permission);
      const isViewer = permission.roles.length === 0 && group.payrolls.some(p => p.visible);

      const formattedGroup = this.formatGroup(group);
      // when is viewer, we only return only visible payrolls
      if (isViewer)
        result.push({
          ...formattedGroup,
          payroll_count: group.payrolls.filter(p => p.visible).length,
        });

      if (isManager) result.push(formattedGroup);
    });

    return result;
  }

  /**
   * Returns groups that the user can see based on visible creditors.
   * It excludes groups that are already in excludeGroupIds.
   * This is used to find additional groups that the user can see based on creditors that user can see.
   */
  private async getGroupsByVisibleCreditors(
    requester: AuthenticationContextDto,
    officeId: number,
    excludeGroupIds: number[],
  ): Promise<GetPayrollResponseGroupings[]> {
    const visibleCreditors = await requester.getVisibleCreditors();

    const isAdminOrSeeAll = visibleCreditors === '*';
    const payrollsThatCanSee = (await Payroll.findAll({
      where: stripUndefinedAttributes(
        {
          office_id: officeId,
          visible: true, // only visible payrolls
          group_id: excludeGroupIds.length
            ? {
                [Op.or]: {
                  [Op.notIn]: excludeGroupIds,
                  [Op.is]: null, // include default payrolls
                },
              }
            : undefined,
        },
        { keepSymbols: true },
      ),
      group: ['group_id'],
      attributes: [
        'group_id',
        'payroll_group.slug',
        'payroll_group.label',
        [
          Sequelize.fn('COUNT', Sequelize.fn('DISTINCT', Sequelize.col('Payroll.id'))),
          'total_payroll',
        ],
      ],
      having: Sequelize.where(
        Sequelize.fn('COUNT', Sequelize.fn('DISTINCT', Sequelize.col('Payroll.id'))),
        '>',
        '0',
      ),
      include: [
        {
          model: Payroll_Data,
          attributes: [],
          where: {
            ...(isAdminOrSeeAll ? {} : { creditor_id: [...visibleCreditors] }),
          },
        },
        Payroll_Group,
      ],
      raw: true,
    })) as unknown as {
      group_id: number | null;
      slug: string;
      label: string;
      total_payroll: number;
    }[];

    return payrollsThatCanSee.map(payroll => {
      return this.formatGroup({
        id: payroll.group_id,
        slug: payroll.slug,
        label: payroll.label,
        payroll_count: payroll.total_payroll,
      });
    });
  }

  /**
   * Returns all payroll groups the user can see, based on permissions and creditor visibility.
   */
  async getGroupsThatCanSee(
    requester: AuthenticationContextDto,
    officeId: number,
  ): Promise<GetPayrollResponseGroupings[]> {
    const officeGroups = await Payroll_Group.findAll({
      where: {
        office_id: officeId,
      },
      attributes: ['id', 'label', 'slug'],
      include: [
        {
          model: Payroll,
          required: false, // left join to include groups without payrolls
        },
      ],
    });

    if (Payroll_Group.isFullManager(requester)) {
      return [this.DEFAULT_PAYROLL_GROUP, ...officeGroups.map(group => this.formatGroup(group))];
    }

    const userVisibleGroups = this.getUserVisibleGroups(requester, officeGroups);
    this.logger.debug(
      {
        groups: userVisibleGroups,
      },
      'user visible groups based on group permissions',
    );

    const additionalGroups = await this.getGroupsByVisibleCreditors(
      requester,
      officeId,
      userVisibleGroups.map(g => g.id),
    );

    const hasDefaultGroup = additionalGroups.some(g => !g.slug);

    const [isStatusManager] = requester.findSessionClaim(UsersClaims.PAYROLL_STATUS_MANAGER);
    if (!!isStatusManager && !hasDefaultGroup) {
      additionalGroups.push(this.DEFAULT_PAYROLL_GROUP);
    }

    this.logger.debug(
      {
        groups: additionalGroups,
      },
      'additional groups based on visible creditors',
    );

    return [...userVisibleGroups, ...additionalGroups].sort((a, b) => {
      if (!a.slug) return -1; // default group always first
      if (!b.slug) return 1; // default group always first
      return a.id - b.id;
    });
  }

  async getBySlug(officeId: number, slug: string): Promise<Payroll_Group | null> {
    return Payroll_Group.findOne({
      where: {
        office_id: officeId,
        slug,
      },
    });
  }
}
