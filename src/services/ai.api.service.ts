import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { GaxiosError, GaxiosOptions, GaxiosPromise } from 'gaxios';
import { ConfigurationEnv } from '../config/configuration.env';
import { RequestMetadataClsStore } from '../middleware/cid.middleware';
import { stripUndefinedAttributes } from '../utils/object.utils';
import { GoogleApiService } from './google.api.service';

type QueryParams = Record<string, string | number | number[] | string[]>;

export const timeoutClone = 1000 * 120;

export const paramsToOverride = (
  baseUrl: string,
  pathUrl: string,
  queryParams: QueryParams = {},
): string => {
  const requestUrl = new URL(pathUrl, baseUrl);

  Object.entries(queryParams).forEach(([key, value]) => {
    if (value === undefined) requestUrl.searchParams.delete(key);
    else if (Array.isArray(value)) value.forEach(v => requestUrl.searchParams.append(key, v));
    else requestUrl.searchParams.set(key, String(value));
  });

  return requestUrl.href;
};

@Injectable()
export class AiAPiService {
  private readonly nLogger = new Logger(AiAPiService.name);

  constructor(
    private readonly config: ConfigurationEnv,
    private readonly gservice: GoogleApiService,
    private readonly cls: RequestMetadataClsStore,
  ) {}

  async sendRequest<T>(
    client_id: string,
    uri: string,
    body: any,
    httpMethod: string,
    noBody = false,
    queryParams: QueryParams = {},
    timeout?: number,
  ): Promise<GaxiosPromise<T>> {
    const baseUrl = this.config.aiApiBaseUrl;

    this.nLogger.verbose('getting id token client started');
    const client = await this.gservice.getIdentityTokenClient(this.config.aiApiBaseUrl);
    this.nLogger.verbose('getting id token client finished');
    const requestUrl = paramsToOverride(baseUrl, uri, queryParams);

    const headers = this.generateRequestHeaders(noBody);

    headers['client_id'] = client_id;

    try {
      this.nLogger.debug({ url: requestUrl, headers }, 'OUTGOING REQUEST');
      const res = await client.request<T>({
        url: requestUrl,
        headers,
        method: httpMethod as GaxiosOptions['method'],
        data: httpMethod === 'GET' ? null : body,
        timeout,
      });
      this.nLogger.debug({ data: res.data, status: res.status }, 'INCOMING RESPONSE');
      return res;
    } catch (err) {
      let errStatus = HttpStatus.INTERNAL_SERVER_ERROR;
      if (err instanceof GaxiosError) {
        errStatus = err.status ?? HttpStatus.INTERNAL_SERVER_ERROR;
      }

      this.nLogger.error(err, 'error calling ai api');
      this.nLogger.error({ ...err }, 'error calling ai api');

      throw new HttpException(
        { msg: 'error communicating to ai-api', data: err.response?.data },
        errStatus,
      );
    }
  }

  private generateRequestHeaders(noBody: boolean): Record<string, string | string[]> {

    const headersToSend = {
      'Content-Type': 'application/json',
      'x-correlation-id': this.cls.get('correlationId') ?? null,
    };

    if (noBody) delete headersToSend['Content-Type'];

    return stripUndefinedAttributes(headersToSend);
  }
}
