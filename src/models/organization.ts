import { DataTypes } from 'sequelize';
import { Default } from 'sequelize-typescript';
import { Column, Model, Table } from 'sequelize-typescript';
import { OrgConfig, RoleMatchType } from '../types/models/organization';

export interface OrganizationCreationAttributes {
  email_domain: string;
}

export interface OrganizationAttributes extends OrganizationCreationAttributes {
  id: number;
}

export const TABLE_NAME = 'organization';

@Table({
  underscored: true,
  freezeTableName: true,
  tableName: TABLE_NAME,
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class Organization extends Model<OrganizationAttributes, OrganizationCreationAttributes> {
  public static TABLE_NAME: string = TABLE_NAME;

  @Column({ type: DataTypes.BIGINT, primaryKey: true, autoIncrement: true })
  declare id: number;

  @Column({ type: DataTypes.STRING, unique: true })
  declare email_domain: string;

  @Default({
    role_match_type: RoleMatchType.FIRST_MATCH,
    roles_config: [
      {
        role: 'Default',
        admin_only: false,
        create_user: false,
        user_info_matches: [{ claim: 'provider_id', value: ['*'] }],
        allowed_tenants_matcher: '.*',
      },
    ],
  })
  @Column({ type: DataTypes.JSON, allowNull: false })
  declare config: OrgConfig;

  @Column({ type: DataTypes.DATE(6), allowNull: false, defaultValue: DataTypes.NOW })
  declare created_at: Date;

  @Column({ type: DataTypes.DATE(6) })
  declare updated_at: Date;
}

export const OrganizationProvider = [
  {
    provide: TABLE_NAME,
    useValue: Organization,
  },
];
