import { DataTypes } from 'sequelize';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Model, Table } from 'sequelize-typescript';
import { Organization } from './organization';
import { Org_User_Identifier } from './org_user_identifier';
import { Oidc_Provider, UserInfo } from './oidc_provider';

export const TABLE_NAME = 'user';

export interface UserCreationAttributes {
  org_id: number;
  email?: string;
  name?: string;
  is_admin?: boolean;
}

export interface UserAttributes extends UserCreationAttributes {
  id: number;
  organization?: Organization;
  created_at: Date;
  updated_at?: Date;
}

@Table({
  underscored: true,
  freezeTableName: true,
  tableName: TABLE_NAME,
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
})
export class User extends Model<UserAttributes, UserCreationAttributes> {
  public static TABLE_NAME: string = TABLE_NAME;

  @Column({ type: DataTypes.BIGINT, primaryKey: true, autoIncrement: true })
  declare id: number;

  @Default(false)
  @Column({ type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false })
  declare is_admin: boolean;

  @Column({ type: DataTypes.STRING, unique: true, allowNull: true })
  declare email?: string;

  @Column({ type: DataTypes.STRING, allowNull: true })
  declare name?: string;

  @ForeignKey(() => Organization)
  @Column({ type: DataTypes.BIGINT.UNSIGNED, allowNull: false })
  declare org_id: number;

  @BelongsTo(() => Organization)
  declare organization?: Organization;

  @Column({ type: DataTypes.DATE(6), allowNull: false, defaultValue: DataTypes.NOW })
  declare created_at: Date;

  @Column({ type: DataTypes.DATE(6) })
  declare updated_at: Date;

  static async getUserByOidc(oidc: Oidc_Provider, providerId: string): Promise<User> {
    const userOrganizationIdentifier = await Org_User_Identifier.scope([
      { method: ['withUser', oidc.org_id] },
    ]).findOne({
      where: {
        oidc_id: oidc.id,
        provider_id: providerId,
      },
    });

    return userOrganizationIdentifier?.user;
  }

  static async getOrCreateUserFromOidc(oidc: Oidc_Provider, userInfo: UserInfo): Promise<User> {
    const user = await User.getUserByOidc(oidc, userInfo.provider_id);

    if (user) return user;

    const newUser = await User.create({
      org_id: oidc.org_id,
      email: userInfo.email,
      name: userInfo.name,
    });

    await Org_User_Identifier.create({
      user_id: newUser.id,
      oidc_id: oidc.id,
      provider_id: userInfo.provider_id,
    });

    return newUser;
  }
}

export const UserProvider = [
  {
    provide: TABLE_NAME,
    useValue: User,
  },
];
