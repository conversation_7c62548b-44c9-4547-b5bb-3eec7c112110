import { DataTypes } from 'sequelize';
import { BelongsTo, Column, HasMany, Model, Table } from 'sequelize-typescript';
import { Escritorio } from './escritorio';
import { Payroll } from './payroll';
import { PayrollGroupPermission, PayrollGroupRoles, UsersClaims } from 'shared-types';
import { AuthenticationContextDto } from '../dto/authenticationContextDto';

export const TABLE_NAME = 'payroll_group';

export interface PayrollGroupCreationAttributes {
  office_id: number;
  label: string;
  slug: string;
}

export interface PayrollGroupAttributes extends PayrollGroupCreationAttributes {
  id: number;
}

@Table({
  underscored: true,
  freezeTableName: true,
  tableName: TABLE_NAME,
  paranoid: true,
})
export class Payroll_Group extends Model<PayrollGroupAttributes, PayrollGroupCreationAttributes> {
  public static TABLE_NAME: string = TABLE_NAME;

  @Column({ type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true })
  declare id: number;

  @Column({ type: DataTypes.INTEGER })
  declare office_id: number;

  @Column({ type: DataTypes.STRING })
  declare label: string;

  @Column({ type: DataTypes.STRING })
  declare slug: string;

  @Column({ type: DataTypes.DATE })
  declare created_at: Date;

  @Column({ type: DataTypes.DATE })
  declare updated_at: Date;

  @BelongsTo(() => Escritorio, 'office_id')
  declare escritorio: Escritorio;

  @HasMany(() => Payroll, {
    foreignKey: 'groupId',
    sourceKey: 'id',
  })
  declare payrolls: Payroll[];

  /**
   * Checks if the requester can manage the group based on their permissions.
   */
  static canManageGroup(user: AuthenticationContextDto, group_slug: string | undefined): boolean {
    // if the group_slug is undefined, it means the default group
    if (!group_slug) {
      const [result] = user.findSessionClaim(UsersClaims.PAYROLL_STATUS_MANAGER);
      return !!result;
    }

    const userGroupsPermissions = Payroll_Group.getUserGroupPermissions(user);
    const permission = userGroupsPermissions.get(group_slug);

    return Payroll_Group.isGranularManager(permission) || Payroll_Group.isFullManager(user);
  }

  static isGranularManager(permissions: PayrollGroupPermission) {
    return permissions?.roles?.some(role => role === PayrollGroupRoles.PAYROLL_MANAGER);
  }

  static isFullManager(request: AuthenticationContextDto) {
    const [isManager] = request.findSessionClaim(UsersClaims.PAYROLL_GROUP_MANAGER);
    return !!isManager;
  }

  /**
   * build the user group permissions map based on the requester's claims.
   * This method extracts the payroll group permissions from the PAYROLL_GROUP_PERMISSIONS claim
   */
  static getUserGroupPermissions(
    requester: AuthenticationContextDto,
  ): Map<string, PayrollGroupPermission> {
    const [payrollGroupPermissions] = requester.findSessionClaim(
      UsersClaims.PAYROLL_GROUP_PERMISSIONS,
    );

    // feed with profile permissions
    const userGroupsPermissions = new Map<string, PayrollGroupPermission>();
    payrollGroupPermissions?.payroll_group_permissions.forEach(groupPermission => {
      userGroupsPermissions.set(groupPermission.payroll_group_slug, groupPermission);
    });

    return userGroupsPermissions;
  }
}

export const PayrollGroupProvider = [
  {
    provide: TABLE_NAME,
    useValue: Payroll_Group,
  },
];
