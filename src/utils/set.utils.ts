export class SetUtils {
  public static difference<T>(set1: Set<T>, set2: Set<T>): Set<T> {
    return new Set([...set1].filter(item => !set2.has(item)));
  }

  public static equals<T>(set1: Set<T>, set2: Set<T>): boolean {
    return set1.size === set2.size && [...set1].every(item => set2.has(item));
  }

  public static intersection<T>(set1: Set<T>, set2: Set<T>): Set<T> {
    return new Set([...set1].filter(item => set2.has(item)));
  }

  public static union<T>(set1: Set<T>, set2: Set<T>): Set<T> {
    return new Set([...set1, ...set2]);
  }
}
