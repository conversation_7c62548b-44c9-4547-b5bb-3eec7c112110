import { Body, Controller, Logger, Post, Req } from '@nestjs/common';
import type { Request } from 'express';
import {
  AiChatTokenResponse,
  chatTokenRequestSchema,
  ChatTokenRequestSchemaDto,
} from 'shared-types';
import { Authenticated } from '../decorators/authenticated.decorator';
import { AssistantUiDomain } from '../domain/assistant.ui.domain';
import { ZodValidationPipe } from './pipes/ZodValidationPipe';

@Controller('assistant-ui')
@Authenticated(req => req.params.office_id, null)
export class AssistantUiController {
  constructor(private readonly assistantUiDomain: AssistantUiDomain) {}

  @Post('/tokens')
  async tokens(
    @Body(new ZodValidationPipe(chatTokenRequestSchema)) body: ChatTokenRequestSchemaDto,
    @Req() req: Request,
  ): Promise<AiChatTokenResponse> {
    return this.assistantUiDomain.getToken(body, req.user);
  }
}
