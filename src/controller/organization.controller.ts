import {
  <PERSON>,
  ForbiddenException,
  Get,
  Lo<PERSON>,
  Req,
  Post,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import type { Request } from 'express';
import { TenantAuthenticated } from '../decorators/tenant.authenticated.decorator';
import { OrganizationDomain } from '../domain/organization/organization.domain';
import { OrgContext } from '../auth.contexts/tenant/org.context';

@Controller('/organization')
export class OrganizationController {
  private readonly logger = new Logger(OrganizationController.name);
  constructor(private readonly organizationDomain: OrganizationDomain) {}

  @TenantAuthenticated()
  @HttpCode(HttpStatus.OK)
  @Post('/login')
  async loginIntoTenant(@Req() req: Request): Promise<void> {
    // if needed in more places, add a new guard in @OrgAuthenticated that throws (403) if !oidc.org_id
    if (!OrgContext.isInstance(req.tenants_ctx)) {
      this.logger.warn('logged-in user is not a member of any organization');
      throw new ForbiddenException();
    }
    return this.organizationDomain.loginIntoTenant(req.tenants_ctx);
  }
}
