import { Body, Controller, Delete, Get, Param, Patch, Post, Req } from '@nestjs/common';
import { Authenticated } from '../decorators/authenticated.decorator';
import { OfficeRepository } from '../domain/repository/officeRepository';
import { OutboundWebhookDomain } from '../outbound.webhook/domain/outbound.webhook.domain';
import {
  BackendErrorCodes,
  UsersClaims,
  CreateWebhookRequest,
  CreateWebhookRequestSchema,
  ListWebhooksResponse,
  GetWebhookResponse,
  CreateWebhookResponse,
  UpdateWebhookRequestSchema,
  UpdateWebhookRequest,
  TesterWebhookSchema,
  TesterWebhookRequest,
  TestWebhookResponse,
} from 'shared-types';
import { ZodValidationPipe } from './pipes/ZodValidationPipe';
import { CustomException } from '../utils/error.utils';
import { OutboundWebhookService } from '../services/webhooks/outbound.webhook.service';
import { Request } from 'express';

@Controller('/offices/:officeId/outbound/webhooks')
@Authenticated(req => req.params.officeId, null, UsersClaims.OUTBOUND_WEBHOOKS_MANAGER)
export class OutboundWebhooksController {
  constructor(
    private readonly officeRepository: OfficeRepository,
    private readonly outboundWebhookDomain: OutboundWebhookDomain,
    private readonly outboundWebhookService: OutboundWebhookService,
  ) {}

  @Get()
  async getWebhooks(
    @Param() { officeId: officeId }: { officeId: number },
  ): Promise<ListWebhooksResponse[]> {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR, { officeId });

    return this.outboundWebhookDomain.getWebhooksConfigByClientId(office.client_id);
  }

  @Get('/:id')
  async getWebhookConfig(
    @Param() { officeId, id }: { officeId: number; id: string },
  ): Promise<GetWebhookResponse> {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR, { officeId });

    return this.outboundWebhookDomain.getWebhookConfigById(office.client_id, Number(id));
  }

  @Post()
  async postWebhook(
    @Body(new ZodValidationPipe(CreateWebhookRequestSchema)) req: CreateWebhookRequest,
    @Param() { officeId }: { officeId: number },
  ): Promise<CreateWebhookResponse> {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR, { officeId });

    return this.outboundWebhookDomain.createWebhook({
      clientId: office.client_id,
      config: req,
    });
  }

  @Patch('/:id')
  async updateWebhook(
    @Body(new ZodValidationPipe(UpdateWebhookRequestSchema)) req: UpdateWebhookRequest,
    @Param() { officeId, id }: { officeId: number; id: number },
  ) {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR, { officeId });

    await this.outboundWebhookDomain.updateWebhook({
      id,
      clientId: office.client_id,
      config: req,
    });
  }

  @Delete('/:id')
  async deleteWebhook(@Param() { officeId, id }: { officeId: number; id: number }) {
    const office = await this.officeRepository.getByID(officeId);
    if (!office) throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR, { officeId });

    await this.outboundWebhookDomain.deleteWebhook({ clientId: office.client_id, id });
  }

  @Post('/tester')
  async testerWebhook(
    @Req() req: Request,
    @Body(new ZodValidationPipe(TesterWebhookSchema)) body: TesterWebhookRequest,
  ): Promise<TestWebhookResponse> {
    try {
      return await this.outboundWebhookService.testWebhook(body, req.user.escritorio.id_escritorio);
    } catch (err) {
      console.log(err);
      throw err;
    }
  }
}
