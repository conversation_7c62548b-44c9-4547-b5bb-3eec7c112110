import {
  All,
  BadRequestException,
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  NotFoundException,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  Res,
  Sse,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import type { Request, Response } from 'express';
import { Op } from 'sequelize';
import {
  BackendErrorCodes,
  DatasourceResponse,
  ExtendedUserResponse,
  GetPayrollDatesFilter,
  GetPayrollDatesFilterSchema,
  NotifyCreditorsPeriodRequest,
  PayoutPeriodEvents,
  PayrollDate,
  PayrollDatesResponse,
  PayrollHashResponse,
  TabEvents,
  UserResponse,
  UsersClaims,
  notifyCreditorsPeriodSchema,
} from 'shared-types';
import { Authenticated } from '../decorators/authenticated.decorator';
import { ExternalAuthenticated } from '../decorators/externalAuthenticated.decorator';
import { SyncPreferencesDomain } from '../domain/preferences/sync.preferences.domain';
import { CreditorRepository } from '../domain/repository/creditorRepository';
import { TabSyncDomain } from '../domain/tab_sync/tab.sync.domain';
import { AuthenticationContextDto } from '../dto/authenticationContextDto';
import { CopyPlanRequestDTO } from '../dto/copy.plan.request.dto';
import { NotifyCreditorsInOnePeriodDto } from '../dto/post.notify.creditos.in.one.period.dto';
import { AtributosEscritorio } from '../models/atributosescritorio';
import { Credor } from '../models/credor';
import { Escritorio } from '../models/escritorio';
import { Payroll } from '../models/payroll';
import { Payroll_Data } from '../models/payroll_data';
import CreditorNotificationService from '../services/creditor.notification.service';
import { SheetDbApiService, timeoutClone } from '../services/sheet.db.api.service';
import { SseService } from '../services/sse.service';
import { CustomException } from '../utils/error.utils';
import { MapUtils } from '../utils/map.utils';
import { findClaim } from '../utils/permission.utils';
import { normalizeCreditor } from '../utils/string.utils';
import { ZodValidationPipe } from './pipes/ZodValidationPipe';
import { PayrollGroupService } from '../services/payroll/payroll.group.service';
import { Payroll_Group } from '../models/payroll_group';

@Controller()
export class SheetDbController {
  private readonly logger = new Logger(SheetDbController.name);

  constructor(
    private readonly sheetDbApiService: SheetDbApiService,
    private readonly creditorNotification: CreditorNotificationService,
    private readonly tabSyncDomain: TabSyncDomain,
    private readonly syncPreferencesDomain: SyncPreferencesDomain,
    private readonly creditorRepository: CreditorRepository,
    private readonly sseService: SseService,
    private readonly payrollGroupService: PayrollGroupService,
  ) {}

  @HttpCode(200)
  @Post('/tab/events')
  @ExternalAuthenticated()
  async handleTabEvents(@Body() data: TabEvents): Promise<any> {
    return this.tabSyncDomain.proccessEvent(data);
  }

  @HttpCode(200)
  @Post('/payout_period/events')
  @ExternalAuthenticated()
  async handlePayoutPeriodEvents(@Body() data: PayoutPeriodEvents): Promise<any> {
    return this.tabSyncDomain.processPayoutEvent(data);
  }

  @Get('/offices/:office_id/sheet-db/creditors')
  @Authenticated(req => req.params.office_id, null)
  async getAllCreditorsInSomePeriod(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
    @Query('visible_periods') visiblePeriods: string,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const visibleCreditors = await request.user.getVisibleCreditors();
    const hasAdminAccess = typeof visibleCreditors === 'string' && visibleCreditors === '*';
    const [, hasClaimClosureUnfinishedView] = request.user.findSessionClaim(
      UsersClaims.CLOSURE_UNFINISHED_VIEW,
    );
    const canViewAllPeriods = hasAdminAccess || hasClaimClosureUnfinishedView;

    const sheetDbUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<UserResponse[]>(
      office.client_id,
      sheetDbUri.toString(),
      request.body,
      request.method,
      false,
      canViewAllPeriods ? { visible_periods: visiblePeriods } : { visible_periods: 'true' },
    );

    const creditorsFiltered = hasAdminAccess
      ? res.data
      : res.data.filter(creditor => visibleCreditors.has(normalizeCreditor(creditor.creditor_id)));

    const creditorsOrderedByName = await this.getCreditorsSortedByName(
      creditorsFiltered,
      office_id,
    );

    response.set(res.headers);
    response.status(res.status).send(creditorsOrderedByName);
  }

  @Get('/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:statement_panel_id/creditors')
  @Authenticated(
    req => req.params.office_id,
    null,
    UsersClaims.GENERIC_DATA_IMPORT,
    UsersClaims.PLAN_GROUP_PERMISSIONS,
  )
  async getAllCreditorsInPeriodAndStatement(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const sheetDbUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<UserResponse[]>(
      office.client_id,
      sheetDbUri.toString(),
      request.body,
      request.method,
    );
    const creditorsOrderedByName = await this.getCreditorsSortedByName(res.data, office_id);

    response.set(res.headers);
    response.status(res.status).send(creditorsOrderedByName);
  }

  @HttpCode(200)
  @Post('/offices/:office_id/sheet-db/plan/:commission_plan_id/period/:period_id/notify')
  @Authenticated(req => req.params.office_id, null)
  async notifyCreditors(
    @Req() request: Request,
    @Body(new ZodValidationPipe(notifyCreditorsPeriodSchema)) payload: NotifyCreditorsPeriodRequest,
    @Param('office_id', ParseIntPipe) office_id: number,
    @Param('commission_plan_id', ParseIntPipe) commission_plan_id: number,
    @Param('period_id', ParseIntPipe) period_id: number,
    @Query('statement_panel_id') statement_panel_id?: number,
  ): Promise<NotifyCreditorsInOnePeriodDto> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');
    if (office.id_escritorio !== office_id)
      throw new ForbiddenException('logged user does not belong to office');

    const attributesOffice = await AtributosEscritorio.findAll({
      where: { escritorio_id_escritorio: office_id },
    });
    const officeCtx = AuthenticationContextDto.BuildFromOffice(
      request.user.escritorio,
      attributesOffice,
    );

    const plan = await this.sheetDbApiService.getCommissionPlan(
      request.user.escritorio.client_id,
      commission_plan_id,
    );

    if (!plan) throw new NotFoundException({ commission_plan_id }, 'commission plan not found');

    const { external_creditors_id: creditorIdsInPeriod } =
      await this.sheetDbApiService.getCreditorsInPayoutPeriod(
        request.user.escritorio.client_id,
        period_id,
        statement_panel_id,
      );

    if (creditorIdsInPeriod.length === 0) return new NotifyCreditorsInOnePeriodDto([]);

    const creditorsInPeriod = await this.creditorRepository.getCreditors({
      escritorio_id_escritorio: office_id,
      id_credor_externo: creditorIdsInPeriod,
      ativo: 1,
    });
    const creditorsWithEmail = creditorsInPeriod.filter(creditor => creditor.email);

    if (creditorsWithEmail.length === 0) return new NotifyCreditorsInOnePeriodDto([]);

    await this.creditorNotification.notifyOnePeriod(
      creditorsWithEmail,
      office,
      plan.name,
      officeCtx.officeLocale,
      payload.title,
      payload.message,
    );

    return new NotifyCreditorsInOnePeriodDto(
      creditorsWithEmail.map(creditor => creditor.id_credor_externo),
    );
  }

  private async validateUserRequest(
    request: Request,
    officeId: number,
    requestedExternalCreditorId: string,
  ): Promise<{ clientId: string; canViewAllPeriods: boolean }> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id: officeId }, 'office not found');
    if (!requestedExternalCreditorId)
      throw new BadRequestException('external_creditor_id is required');

    const visibleCreditors = await request.user.getVisibleCreditors();
    const allowGetData = await request.user.canSeeCreditor(requestedExternalCreditorId);
    if (!allowGetData) throw new ForbiddenException('logged user cant see other creditors data');

    const hasAdminAccess = typeof visibleCreditors === 'string' && visibleCreditors === '*';
    const [seeUnfinishedClosures] = request.user.findSessionClaim(
      UsersClaims.CLOSURE_UNFINISHED_VIEW,
    );
    const canViewAllPeriods = hasAdminAccess || seeUnfinishedClosures;
    return { clientId: office.client_id, canViewAllPeriods };
  }

  @Get([
    '/offices/:office_id/sheet-db/plan_groups/statement_panels',
    '/offices/:office_id/sheet-db/plan_groups/creditor/:external_creditor_id',
    '/offices/:office_id/sheet-db/plan_groups/:group_id/periods',
    '/offices/:office_id/sheet-db/plan/creditor/:external_creditor_id',
    '/offices/:office_id/sheet-db/plan/:plan_id/period/creditor/:external_creditor_id',
  ])
  @Authenticated(req => req.params.office_id, null)
  async handleGetUserPlansAndPeriods(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') officeId: number,
    @Param('external_creditor_id') external_creditor_id: string,
    @Query('creditor') creditor: string,
    @Query('visible_periods') visiblePeriods: string,
  ): Promise<any> {
    const externalCreditorId = external_creditor_id || creditor;

    const { clientId, canViewAllPeriods } = await this.validateUserRequest(
      request,
      officeId,
      externalCreditorId,
    );
    const sheetDbUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest(
      clientId,
      sheetDbUri.toString(),
      request.body,
      request.method,
      false,
      canViewAllPeriods ? { visible_periods: visiblePeriods } : { visible_periods: 'true' },
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Post([
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:statement_panel_id/data/filter',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:statement_panel_id/data/:column_id/autocomplete',
  ])
  @Authenticated(req => req.params.office_id, null)
  async handleGetWidgetDataWithFilter(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
    @Query('creditorId') external_creditor_id: string,
    @Query('visible_periods') visiblePeriods: string,
  ): Promise<any> {
    return this.handleGetWidgetData(
      request,
      response,
      office_id,
      external_creditor_id,
      visiblePeriods,
    );
  }

  @Get('/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:statement_panel_id/data')
  @Authenticated(req => req.params.office_id, null)
  async handleGetWidgetData(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') officeId: number,
    @Query('creditorId') externalCreditorId: string,
    @Query('visible_periods') visiblePeriods: string,
  ): Promise<any> {
    const { clientId, canViewAllPeriods } = await this.validateUserRequest(
      request,
      officeId,
      externalCreditorId,
    );

    const sheetDbUri = request.originalUrl.split('/').slice(4).join('/');

    const body = await this.addUserInfoToBody(request.body, officeId, externalCreditorId);

    const res = await this.sheetDbApiService.sendRequest<any>(
      clientId,
      sheetDbUri.toString(),
      body,
      request.method,
      false,
      canViewAllPeriods ? { visible_periods: visiblePeriods } : { visible_periods: 'true' },
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Post(
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:statement_panel_id/preview/filter',
  )
  @Authenticated(req => req.params.office_id, null)
  async handleGetWidgetDataPreview(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') officeId: number,
    @Query('creditorId') externalCreditorId: string,
    @Query('visible_periods') visiblePeriods: string,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id: officeId }, 'office not found');

    const sheetDbUri = request.originalUrl.split('/').slice(4).join('/');

    const body = await this.addUserInfoToBody(request.body, officeId, externalCreditorId);

    const res = await this.sheetDbApiService.sendRequest<any>(
      office.client_id,
      sheetDbUri.toString(),
      body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Get('/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:statement_panel_id/download')
  @Authenticated(req => req.params.office_id, null)
  async handleDownloadWidgetData(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
    @Query('creditorId') external_creditor_id: string,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const allowDownloadWidgetData = await request.user.canSeeCreditor(
      decodeURIComponent(external_creditor_id),
    );
    if (!allowDownloadWidgetData)
      throw new ForbiddenException('logged user cant see other creditors data');

    const sheetDbUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<any>(
      office.client_id,
      sheetDbUri.toString(),
      request.body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Post(
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:statement_panel_id/filter/download',
  )
  @Authenticated(req => req.params.office_id, null)
  async handleDownloadWidgetDataFiltered(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
    @Query('creditorId') external_creditor_id: string,
  ): Promise<any> {
    return this.handleDownloadWidgetData(request, response, office_id, external_creditor_id);
  }

  @HttpCode(200)
  @Post('/offices/:office_id/sheet-db/file')
  @Authenticated(req => req.params.office_id, null)
  @UseInterceptors(FileInterceptor('file'))
  async uploadFileInSheetDb(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const form = new FormData();
    form.append('file', new Blob([file.buffer]), file.originalname);
    const { data, status } = await this.sheetDbApiService.sendRequestMultPartForm(
      office.client_id,
      tabUri,
      form,
    );
    response.status(status).send(data);
  }

  @Post('/offices/:office_id/sheet-db/sync')
  @Authenticated(req => req.params.office_id, null)
  async syncTabs(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    await this.tabSyncDomain.tabSyncsCleanUp(office_id);

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<any>(
      office.client_id,
      tabUri,
      request.body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Delete('/offices/:office_id/sheet-db/lock')
  @Authenticated(req => req.params.office_id, null)
  async destroyLock(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
    @Query()
    { commission_plan_id }: { commission_plan_id: number },
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<any>(
      office.client_id,
      tabUri,
      request.body,
      request.method,
    );

    if (commission_plan_id && res.status === HttpStatus.OK) {
      await this.syncPreferencesDomain.removePlanLock(office.id_escritorio, commission_plan_id);
    }

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Patch(['/offices/:office_id/sheet-db/connector/webhook/:connectorId/config'])
  @Authenticated(req => req.params.office_id, null, UsersClaims.GENERIC_DATA_IMPORT)
  async handleRequestWithoutBody(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');
    this.logger.debug('Request without body');
    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const body = !request.body || !Object.keys(request.body).length ? null : request.body;
    const res = await this.sheetDbApiService.sendRequest<any>(
      office.client_id,
      tabUri,
      body,
      request.method,
      !!body ? false : true,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Get('/offices/:office_id/sheet-db/payroll/dates')
  @Authenticated(req => req.params.office_id, null, UsersClaims.FEATURE_PAYROLL_V2)
  async handlePayrollDateRequest(
    @Req() request: Request,
    @Res() response: Response,
    @Query(new ZodValidationPipe(GetPayrollDatesFilterSchema)) params: GetPayrollDatesFilter,
  ) {
    const office = request.user.escritorio;
    if (!office)
      throw new NotFoundException({ office_id: request.params.office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<PayrollDatesResponse>(
      office.client_id,
      tabUri,
      request.body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status);

    const { payroll_group: group_slug } = params;
    const isManager = Payroll_Group.canManageGroup(request.user, group_slug);
    const [isStatusManager] = request.user.findSessionClaim(UsersClaims.PAYROLL_STATUS_MANAGER);

    const [hasUnfinishedClosureView] = request.user.findSessionClaim(
      UsersClaims.CLOSURE_UNFINISHED_VIEW,
    );

    const getSortedDates = (dates: PayrollDate[]) => {
      return dates.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    };

    const { dates } = res.data;

    if (isStatusManager || isManager) {
      const payrolls = await Payroll.scope({
        method: ['withPayrollGroup', { group_slug }],
      }).findAll({
        where: {
          office_id: office.id_escritorio,
        },
        attributes: ['payout_date', 'id'],
        include: [
          {
            attributes: ['payroll_id'],
            model: Payroll_Data,
            required: true,
          },
        ],
      });

      const allPayrollDates = new Set(payrolls.map(({ payout_date }) => payout_date));
      const allSheetDbDates = new Set(dates.map(({ date }) => date));

      const payrollDates: PayrollDate[] = payrolls.map(({ payout_date: date, id }) => ({
        date,
        excluded_period: !allSheetDbDates.has(date),
        has_payroll: true,
        payroll_id: id,
      }));

      const sheetDbDates = dates
        .map(({ date }) => ({
          date,
          excluded_period: false,
          has_payroll: allPayrollDates.has(date),
        }))
        .filter(({ date }) => !allPayrollDates.has(date));

      const mappedDates = [...payrollDates, ...sheetDbDates];
      const sortedDates = getSortedDates(mappedDates);

      return response.send({ dates: sortedDates });
    }

    const visibleCreditorsMap = await request.user.getVisibleCreditors();
    if (group_slug) {
      await this.payrollGroupService.checkIfCanSeeGroup(request.user, group_slug);
    }

    const canSeeEveryone = (await request.user.canSeeEverything()) || isManager;
    const creditorsWhere = canSeeEveryone
      ? {}
      : {
          creditor_id: {
            [Op.in]: Array.from(visibleCreditorsMap),
          },
        };

    const defaultPayrollWhere = {
      office_id: office.id_escritorio,
    };

    const payrollWhere = hasUnfinishedClosureView
      ? defaultPayrollWhere
      : {
          visible: true,
          ...defaultPayrollWhere,
        };

    const payrollData = await Payroll_Data.findAll({
      attributes: ['payroll_id'],
      where: creditorsWhere,
      include: [
        {
          model: Payroll.scope({
            method: ['withPayrollGroup', { group_slug }],
          }),
          attributes: ['payout_date', 'id'],
          required: true,
          where: payrollWhere,
        },
      ],
      group: ['payroll_id'],
      logging: query => {
        this.logger.debug({ query }, 'getting payroll data');
      },
    });

    const sortedDates = getSortedDates(
      payrollData.map(data => ({
        date: data.payroll.payout_date,
        has_payroll: true,
        payroll_id: data.payroll.id,
      })),
    );

    this.logger.debug(
      { sheetdb_dates: res.data, user_dates: sortedDates, visibleUsers: visibleCreditorsMap },
      'has data payroll data',
    );

    return response.send({ dates: sortedDates });
  }

  @Get('/offices/:office_id/sheet-db/payroll/:payout_date/hash')
  @Authenticated(req => req.params.office_id, null)
  async handlePayrollHashRequest(@Req() request: Request, @Res() response: Response) {
    const office = request.user.escritorio;
    if (!office)
      throw new NotFoundException({ office_id: request.params.office_id }, 'office not found');

    const { payroll_group } = request.query;

    await this.payrollGroupService.checkIfCanSeeGroup(request.user, payroll_group as string);

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<PayrollHashResponse>(
      office.client_id,
      tabUri,
      request.body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Put('/offices/:office_id/sheet-db/datasource')
  @Authenticated(req => req.params.office_id, null, UsersClaims.GENERIC_DATA_IMPORT)
  async createDatasource(@Req() request: Request, @Res() response: Response) {
    const office = request.user.escritorio;
    const credor = request.user.getUserForAudit().credor;
    if (!office)
      throw new NotFoundException({ office_id: request.params.office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<DatasourceResponse>(
      office.client_id,
      tabUri,
      {
        ...request.body,
        requested_by: credor.id_credor_externo,
      },
      request.method,
    );
    res.data.creditor_name = credor.nome_credor;

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  // explicit route /clone on sheet-db not allowed! Office clone on sheet-db should be used through PUT /office route
  @Put('/offices/:office_id/sheet-db/clone')
  @Authenticated(req => req.params.office_id, null)
  async cloneOffice(): Promise<void> {
    throw new ForbiddenException('cant clone office');
  }

  @Put('/offices/:office_id/sheet-db/clone-plan')
  @Authenticated(req => req.params.office_id, null)
  async clonePlan(
    @Req() request: Request,
    @Param('office_id') office_id: number,
    @Body() body: CopyPlanRequestDTO,
  ): Promise<void> {
    if (!request.user.isBackoffice()) throw new ForbiddenException('cant clone plan');
    CopyPlanRequestDTO.validate(body);
    const { plan_id, to_office_id } = body;

    const office = await Escritorio.findOne({
      where: { id_escritorio: office_id },
    });
    const toOffice = await Escritorio.findOne({
      where: { id_escritorio: to_office_id },
    });

    if (!office || !toOffice) {
      this.logger.error(
        { payload: { plan_id, to_office_id, office, to_office: toOffice } },
        'office not found',
      );
      throw new CustomException(BackendErrorCodes.OFFICE_NOT_FOUND_ERROR, {
        office,
        to_office: toOffice,
      });
    }

    await this.sheetDbApiService.sendRequest(
      office.client_id,
      `clone-plan`,
      { plan_id, to_office_id: toOffice.client_id },
      'PUT',
      undefined,
      undefined,
      timeoutClone,
    );
  }

  @Put([
    '/offices/:office_id/sheet-db/datasource/:datasource_id/rows',
    '/offices/:office_id/sheet-db/tab/:tab_id/formula/:formula_id',
    '/offices/:office_id/sheet-db/plan/:plan_id/datasource',
    '/offices/:office_id/sheet-db/plan/:plan_id/datasources',
    '/offices/:office_id/sheet-db/plan/:plan_id/datasource/:datasource_id',
    '/offices/:office_id/sheet-db/plan/:plan_id/panel/:statement_panel_id',
  ])
  @Authenticated(req => req.params.office_id, null)
  async handlePutDatasourceRows(
    @Req() request: Request,
    @Res() response: Response,
    @Body() body: Record<string, unknown>,
    @Param('office_id') office_id: number,
  ) {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest(
      office.client_id,
      tabUri,
      body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Get('/offices/:office_id/sheet-db/tab/:tab_id/datasources')
  @Authenticated(req => req.params.office_id, null)
  async getDataSources(@Req() request: Request, @Res() response: Response) {
    const office = request.user.escritorio;
    const credor = request.user.getUserForAudit().credor;
    if (!office)
      throw new NotFoundException({ office_id: request.params.office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<DatasourceResponse[]>(
      office.client_id,
      tabUri,
      {},
      request.method,
    );
    if (res.status !== HttpStatus.OK) {
      response.set(res.headers);
      response.status(res.status).send(res.data);
      return;
    }

    await SheetDbApiService.fillCreditorsName(office.id_escritorio, res.data);

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Post([
    '/offices/:office_id/sheet-db/tab',
    '/offices/:office_id/sheet-db/tab/:tab_id',
    '/offices/:office_id/sheet-db/tab/:tab_id/column',
    '/offices/:office_id/sheet-db/tab/:tab_id/rows/filter',
    '/offices/:office_id/sheet-db/tab/:tab_id/rows/:column_id/autocomplete',
    '/offices/:office_id/sheet-db/tab/:tab_id/filter/download',
    '/offices/:office_id/sheet-db/tab/:tab_id/formula',
    '/offices/:office_id/sheet-db/tab/:tab_id/rows/:column_id/filter',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/sync_schedule',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/group/sources',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/group',
    '/offices/:office_id/sheet-db/tasks/retry',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/panel',
    '/offices/:office_id/sheet-db/plan/payout_periods',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/payout_period/:payout_period_id/evolve',
  ])
  @Authenticated(req => req.params.office_id, null)
  async handlePostRoutes(
    @Req() request: Request,
    @Res() response: Response,
    @Body() body: Record<string, unknown>,
    @Param('office_id') office_id: number,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest(
      office.client_id,
      tabUri,
      body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Patch([
    '/offices/:office_id/sheet-db/tab/:tab_id',
    '/offices/:office_id/sheet-db/tab/:tab_id/column/:column_id',
    '/offices/:office_id/sheet-db/tab/:tab_id/formula/:formula_id',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/layout',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/layout/sources',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/layout/group/:group_id',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/layout/sources/group/:group_id',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/payout_period/:payout_period_id',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:statement_panel_id',
  ])
  @Authenticated(req => req.params.office_id, null)
  async handlePatchRoutes(
    @Req() request: Request,
    @Res() response: Response,
    @Body() body: Record<string, unknown>,
    @Param('office_id') office_id: number,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest(
      office.client_id,
      tabUri,
      body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Delete([
    '/offices/:office_id/sheet-db/tab/:tab_id',
    '/offices/:office_id/sheet-db/tab/:tab_id/column/:column_id',
    '/offices/:office_id/sheet-db/tab/:tab_id/datasource/:datasource_id',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:statement_panel_id',
  ])
  @Authenticated(req => req.params.office_id, null)
  async handleDeleteTab(
    @Req() request: Request,
    @Res() response: Response,
    @Body() body: Record<string, unknown>,
    @Param('office_id') office_id: number,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest(
      office.client_id,
      tabUri,
      body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Sse('/offices/:office_id/job/status/subscription')
  @Authenticated(req => req.params.office_id, null)
  async jobStatus(
    @Req() request: Request,
    @Param('office_id') office_id: number,
    @Query('commission_plan_id') commission_plan_id?: string,
    @Query('payout_period_id') payout_period_id?: string,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    return this.sseService.subscribeToStatus(
      office.client_id,
      commission_plan_id,
      payout_period_id,
    );
  }

  @Get([
    '/offices/:office_id/sheet-db/file',
    '/offices/:office_id/sheet-db/dataset/tables',
    '/offices/:office_id/sheet-db/file/worksheet',
    '/offices/:office_id/sheet-db/file/worksheet/columns',
    '/offices/:office_id/sheet-db/tab/:tab_id',
    '/offices/:office_id/sheet-db/tab/:tab_id/history',
    '/offices/:office_id/sheet-db/tab/:tab_id/rows',
    '/offices/:office_id/sheet-db/tab/:tab_id/file',
    '/offices/:office_id/sheet-db/tab/:tab_id/download',
    '/offices/:office_id/sheet-db/tab/:tab_id/download/page_size',
    '/offices/:office_id/sheet-db/tab/group/metadata_config',
    '/offices/:office_id/sheet-db/tabs',
    '/offices/:office_id/sheet-db/tabs/context',
    '/offices/:office_id/sheet-db/plan_groups',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/sync_schedule',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/layout',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/panel',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/panel/:panelId',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/payout_period/:payout_period_id',
    '/offices/:office_id/sheet-db/plan/:commission_plan_id/payout_period/:payout_period_id/job/status',
    '/offices/:office_id/sheet-db/datasource/:tab_id',
  ])
  @Authenticated(req => req.params.office_id, null)
  async handleGetRoutes(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest(
      office.client_id,
      tabUri,
      request.body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Get([
    '/offices/:office_id/sheet-db/user_management/dates',
    '/offices/:office_id/sheet-db/user_management/rows',
  ])
  @Authenticated(req => req.params.office_id, null, UsersClaims.USER_CREATE)
  async handleUserManagement(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
  ) {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const requestUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<any>(
      office.client_id,
      requestUri,
      request.body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @All('/offices/:office_id/sheet-db*path')
  @Authenticated(req => req.params.office_id, null, UsersClaims.GENERIC_DATA_IMPORT)
  async handletabRequest(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const [_, claimValue] = findClaim(request.user.claims, 'graph.v2');

    const tabUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<any>(
      office.client_id,
      tabUri,
      request.body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  private async addUserInfoToBody(body: any, officeId: number, externalCreditorId: string) {
    const credor = await this.creditorRepository.obterPorIdExterno(officeId, externalCreditorId);

    if (!credor) return body;

    return {
      ...body,
      user: {
        name: credor.nome_credor,
        email: credor.email,
        teams: credor.teams.map(team => team.name),
      },
    };
  }

  private async getCreditorsSortedByName(
    creditors: UserResponse[],
    office_id: number,
  ): Promise<ExtendedUserResponse[]> {
    const normalizedCreditors = creditors.map(({ creditor_id, ...rest }) => ({
      creditor_id: normalizeCreditor(creditor_id),
      ...rest,
    }));
    const allCreditors = await Credor.findAll({
      attributes: ['id_credor_externo', 'nome_credor'],
      where: {
        escritorio_id_escritorio: office_id,
        id_credor_externo: normalizedCreditors.map(c => c.creditor_id),
      },
    });

    const allCreditorMap = MapUtils.arrayToMapLambda(allCreditors, c => c.id_credor_externo);

    const extendedCreditors = normalizedCreditors.map(user => ({
      ...user,
      creditor_name: allCreditorMap.get(user.creditor_id)?.nome_credor,
    }));

    return extendedCreditors.sort((a, b) => {
      const aName = a.creditor_name?.trim();
      const bName = b.creditor_name?.trim();

      if (!aName || aName > bName) return 1;
      if (!bName || aName < bName) return -1;
      return 0;
    });
  }

  @Post([
    '/offices/:office_id/sheet-db/ai/formulas/autocomplete',
    '/offices/:office_id/sheet-db/ai/chat',
  ])
  async aiApi(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
    @Query('commission_plan_id') commission_plan_id?: string,
    @Query('payout_period_id') payout_period_id?: string,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const sheetDbUri = request.originalUrl.split('/').slice(4).join('/');

    const res = await this.sheetDbApiService.sendRequest<any>(
      office.client_id,
      sheetDbUri,
      request.body,
      request.method,
      false,
      commission_plan_id ? { commission_plan_id, payout_period_id } : {},
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }
}
