import {
  <PERSON>,
  Logger,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import type { Request, Response } from 'express';
import { Authenticated } from '../decorators/authenticated.decorator';
import { AiAPiService } from '../services/ai.api.service';
import { SheetDbApiService } from '../services/sheet.db.api.service';

@Controller('/offices/:office_id/ai-api')
@Authenticated(req => req.params.office_id, null)
export class AiApiController {
  private readonly logger = new Logger(AiApiController.name);

  constructor(
    private readonly aiApiService: AiAPiService,
    private readonly sheetDbService: SheetDbApiService,
  ) {}

  @Post('/formulas/autocomplete')
  async formulaAutocomplete(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const res = await this.aiApiService.sendRequest<any>(
      office.client_id,
      'formulas/autocomplete',
      request.body,
      request.method,
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }

  @Post('/chat')
  async chat(
    @Req() request: Request,
    @Res() response: Response,
    @Param('office_id') office_id: number,
    @Query('commission_plan_id') commission_plan_id?: string,
    @Query('payout_period_id') payout_period_id?: string,
  ): Promise<any> {
    const office = request.user.escritorio;
    if (!office) throw new NotFoundException({ office_id }, 'office not found');

    const res = await this.sheetDbService.sendRequest<any>(
      office.client_id,
      'ai/chat',
      request.body,
      request.method,
      false,
      commission_plan_id ? { commission_plan_id, payout_period_id } : {},
    );

    response.set(res.headers);
    response.status(res.status).send(res.data);
  }
}
