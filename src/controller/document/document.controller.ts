import { File } from '../../models/file';
import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Req,
  Res,
  Delete,
  BadRequestException,
  Patch,
} from '@nestjs/common';
import type { Request, Response } from 'express';
import {
  BackendErrorCodes,
  CreateDocumentResponse,
  DocumentRequest,
  EvaluateDocumentApprovalRequest,
  GetPaginatedDocumentResponse,
  GetDocumentsFilterBy,
  GetDocumentsSchema,
  UpdateDocumentRequest,
  UsersClaims,
  getDocumentsSchema,
} from 'shared-types';
import { Authenticated } from '../../decorators/authenticated.decorator';
import { DocumentDomain } from '../../domain/documents/domain/document.domain';
import { GetDocumentResponseDto } from '../../dto/documents/get.document.response.dto';
import { extensionMimeTypes, getExtensionFromFileName } from '../../utils/string.utils';
import { ParseArrayOrUndefinedPipe } from '../pipes/ParseArrayOrUndefinedPipe';
import { ParseOptionalBoolPipe } from '../pipes/ParseOptionalBoolPipe';
import { checkListDocumentsPermission } from './permissions';
import { CreateDocumentResponseDto } from '../../dto/documents/create.document.response.dto';
import { DELETE_DOCUMENTS_NUMBER_ID } from './errors';
import { Credor } from '../../models/credor';
import { DocumentRepository } from '../../domain/repository/document.repository';
import { ZodValidationPipe } from '../pipes/ZodValidationPipe';
import { evaluateDocumentApprovalSchema, updateDocumentSchema } from './schema';
import { CustomException } from '../../utils/error.utils';

@Controller('/offices/:officeId')
export class DocumentController {
  constructor(
    private readonly documentDomain: DocumentDomain,
    private readonly documentRepository: DocumentRepository,
  ) {}

  @Get('/document')
  @Authenticated(req => req.params.officeId, null, UsersClaims.DOCUMENT_MANAGEMENT)
  async getDocument(
    @Param('officeId') officeId: string,
    @Query('download', ParseOptionalBoolPipe) download: boolean = false,
    @Query(new ZodValidationPipe(getDocumentsSchema)) queryParams: GetDocumentsSchema,
    @Req() req: Request,
    @Res() res: Response<GetPaginatedDocumentResponse>,
  ): Promise<GetPaginatedDocumentResponse> {
    const parsedCreditorParams =
      // has only one creditor to filter
      typeof queryParams.creditor_id === 'string'
        ? [queryParams.creditor_id]
        : queryParams.creditor_id;

    const allowed = await checkListDocumentsPermission(req, parsedCreditorParams, 'view', {
      filterAllowedCreditors: !queryParams.creditor_id?.length,
    });
    const allowedCreditorsToQuery = Array.isArray(allowed) ? allowed : undefined; // if '*', query everyone

    const docs = await this.documentDomain.getDocuments(req.user.escritorio, {
      ...queryParams,
      start_date: queryParams.date ?? queryParams.start_date,
      end_date: queryParams.date ?? queryParams.end_date,
      filter_date_by: queryParams.filter_date_by ?? GetDocumentsFilterBy.CLOSURE_DATE,
      creditor_id: allowedCreditorsToQuery,
    });

    if (!download) {
      res.send(docs);
      return;
    }

    const { file, fileName } = await this.documentDomain.downloadAllFiles(
      req.user.escritorio,
      docs.items,
    );

    const extension = getExtensionFromFileName(fileName);
    const mime = extensionMimeTypes[extension] ?? extensionMimeTypes.default;

    res.set({
      'Content-Type': mime,
      'Content-Disposition': `attachment; filename=${encodeURIComponent(fileName)}`,
      'Content-Length': file.length,
      'x-correlation-id': req.headers['x-correlation-id'],
    });
    res.end(file);
  }

  @Get('/document/:documentId')
  @Authenticated(req => req.params.officeId, null, UsersClaims.DOCUMENT_MANAGEMENT)
  async getSingleDocument(
    @Param('officeId') officeId: string,
    @Param('documentId', ParseIntPipe) documentId: number,
    @Query('download', ParseOptionalBoolPipe) download: boolean,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<GetDocumentResponseDto> {
    const document = await this.documentRepository.getDocument({ id: documentId }, [
      {
        model: File,
        where: { office_id: officeId },
      },
      { model: Credor, as: 'credor' },
    ]);
    if (!document) throw new NotFoundException();

    await checkListDocumentsPermission(req, [document.credor.id_credor_externo], 'view');

    if (!download) {
      res.send(new GetDocumentResponseDto(document));
      return;
    }

    const { file, fileName } = await this.documentDomain.downloadAllFiles(req.user.escritorio, [
      new GetDocumentResponseDto(document),
    ]);

    const extension = getExtensionFromFileName(fileName);
    const mime = extensionMimeTypes[extension.toLocaleLowerCase()] ?? extensionMimeTypes.default;

    res.set({
      'Content-Type': mime,
      'Content-Disposition': `attachment; filename=${encodeURIComponent(fileName)}`,
      'Content-Length': file.length,
      'x-correlation-id': req.headers['x-correlation-id'],
    });
    res.end(file);
  }

  @Delete('/document')
  @Authenticated(req => req.params.officeId, null, UsersClaims.DOCUMENT_MANAGEMENT)
  async deleteDocuments(
    @Param('officeId') officeId: string,
    @Query(
      'document_ids',
      new ParseArrayOrUndefinedPipe({
        parseFn: 'number',
        required: true,
        exceptionFactory: _ => new BadRequestException({ ...DELETE_DOCUMENTS_NUMBER_ID }),
      }),
    )
    documentIds: number[] | undefined,
    @Req() req: Request,
  ): Promise<void> {
    return this.documentDomain.deleteDocuments({
      documentIds,
      officeId: +officeId,
      req,
    });
  }

  @Post('/document')
  @Authenticated(req => req.params.officeId, null, UsersClaims.DOCUMENT_MANAGEMENT)
  async createDocument(
    @Param('officeId') officeId: string,
    @Body() data: DocumentRequest[],
    @Req() req: Request,
  ): Promise<CreateDocumentResponse[]> {
    const parsedData = data.map(item => ({
      ...item,
      creditor_id: decodeURIComponent(item.creditor_id),
    }));

    await checkListDocumentsPermission(
      req,
      parsedData.map(d => d.creditor_id),
      'create',
    );

    const createdDocuments = await this.documentDomain.addDocuments(
      +officeId,
      parsedData,
      req.user,
    );

    return createdDocuments.map(doc => new CreateDocumentResponseDto(doc));
  }

  @Patch('/document/approval/:documentId')
  @Authenticated(req => req.params.officeId, null, UsersClaims.DOCUMENT_MANAGEMENT)
  async evaluateDocumentApproval(
    @Param('officeId') officeId: string,
    @Param('documentId', ParseIntPipe) documentId: number,
    @Body(new ZodValidationPipe(evaluateDocumentApprovalSchema))
    data: EvaluateDocumentApprovalRequest,
    @Req() req: Request,
  ): Promise<void> {
    const [permission, success] = req.user.findSessionClaim(UsersClaims.DOCUMENT_MANAGEMENT);

    if (!success || !permission.approve) {
      throw new CustomException(BackendErrorCodes.DOCUMENT_FORBIDDEN_APPROVAL_EVALUATION);
    }

    await this.documentDomain.evaluateApproval(data, documentId, req);
  }

  @Patch('/document/:documentId')
  @Authenticated(req => req.params.officeId, null, UsersClaims.DOCUMENT_MANAGEMENT)
  async updateDocument(
    @Param('documentId', ParseIntPipe) documentId: number,
    @Body(new ZodValidationPipe(updateDocumentSchema))
    data: UpdateDocumentRequest,
    @Req() req: Request,
  ): Promise<void> {
    const [permission, success] = req.user.findSessionClaim(UsersClaims.DOCUMENT_MANAGEMENT);

    if (!success || !permission.mark_as_paid) {
      throw new CustomException(BackendErrorCodes.DOCUMENT_FORBIDDEN_MARK_AS_PAID);
    }

    await this.documentDomain.updateDocument(documentId, data);
  }
}
