## Testes de OCR de Nota Fiscal

Na pasta `testing_files/` temos alguns exemplos reais de NFs coletadas  
Já no arquivo `nf.ai-spec.ts` está automatizado pra ler cada um dos arquivos que está na pasta e rodar o OCR comparando com o snapshot

- para testar uma nova NF, é só jogar o arquivo na pasta e rodar os testes
- rodando o teste mais de uma vez, vai ser possível verificar inconsistências na extração de alguns campos (porque o snapshot vai ter um valor na primeira run, e um valor diferente nas subsequentes)

## Tunando o prompt e testando se melhorou
* abrir o [`nf.ts`](./nf.ts) e refinar o prompt
* rodar o teste
* verificar se deu muita diferença com os snapshots existentes
* se quiser adicionar um arquivo novo é só jogar na pasta `testing_files`
* rode o teste várias vezes pra verificar as diferenças com o snapshot


## Alguns campos que parecem problemáticos:
- o campo de logradouro, complemento, bairro e endereço é um pouco inconsistente entre as runs (principalmente nas notas de `nf_manaus.pdf`, `nf_saojoseribamar.pdf` e `nf_paraupebas`) tanto pro prestador como pro emissor  
Parece ter a ver com a forma que o endereço tá disposto
- o campo de prefeitura também é um pouco inconsistente, as vezes ele coloca "municipio", ou "prefeitura de"