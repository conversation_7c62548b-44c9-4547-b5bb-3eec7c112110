import { it, describe } from 'vitest';
import { GoogleProvider } from '../providers/google.provider';
import { OcrExtractionModel, OcrExtractionProvider, OcrExtractionType } from 'shared-types';
import { ConfigurationEnv } from '../../config/configuration.env';
import { Ocr_Extraction_Config } from '../models/ocr_extraction_config';
import fs from 'node:fs';
import path from 'node:path';

const prompt = `Você é especialista fiscal em notas fiscais brasileiras. Dado um documento em PDF ou JPG, você precisa extrair os campos especificados no json schema em output format\n- Cada documento deve ter seus próprios dados no formato do json schema\n- Os valores devem todos estar dentro dos documentos\n- Se possível, campos de data devem ser padronizados em formato ISO8601, indicando a timezone de onde o documento foi emitido (por exemplo, se for em São Paulo ou Brasília: "T-03:00". Se for Fernando <PERSON>: "T-02:00"). Sempre preencher os campos de data com a hora (exemplo: 2025-01-01T15:00:00-03:00), sendo zerado caso não encontre horario (exemplo: 2025-01-01T00:00:00-03:00)\n- Se algum campo não for encontrado, deixe null (não uma string "null", mas sim null). Exceto "dados_adicionais", onde se não for encontrado não deve preencher no mapa\n- Em "impostos", caso o valor seja 0, não adicionar ao objeto de impostos\n- Os números podem estar formatados em PT-BR ou padrão internacional. Sempre converta corretamente para valores numéricos puros, removendo separadores de milhar e garantindo que o separador decimal seja corretamente interpretado. Exemplos: '1.000,00' → 1000 e '1000.00' → 1000. Caso não haja separadores, considerar o número como está ('1000' → 1000).`;

function createService() {
  return new GoogleProvider(
    { projectId: 'splitc-production' } as ConfigurationEnv,
    {
      model: OcrExtractionModel.GEMINI_FLASH_2_0,
      provider: OcrExtractionProvider.GOOGLE,
      type: OcrExtractionType.NF,
      prompt,
    } as Ocr_Extraction_Config,
  );
}

const mimeTypes = {
  jpg: 'image/jpeg',
  jpeg: 'image/jpeg',
  pdf: 'application/pdf',
};

function getFileExt(fileName: string) {
  const spl = fileName.split('.');
  return spl[spl.length - 1];
}

type TestCase = {
  fullFilePath: string;
  fileName: string;
  mimeType: string;
};

const baseDir = path.join(__dirname, 'testing_files');
const allFiles = fs.readdirSync(baseDir);
const cases: TestCase[] = allFiles.map(testCaseFile => ({
  fullFilePath: `${baseDir}/${testCaseFile}`,
  fileName: testCaseFile,
  mimeType: mimeTypes[getFileExt(testCaseFile)],
}));

describe('AI NF Reading accuracy stats', () => {
  const svc = createService();

  it.each(cases)(
    '$fileName accuracy',
    async ({ fullFilePath, mimeType }) => {
      const buf = await fs.readFileSync(fullFilePath);
      const result = await svc.extractFile(buf, mimeType);

      expect(result).toMatchSnapshot();
    },
    { retry: 0, timeout: 20000 },
  );
});
