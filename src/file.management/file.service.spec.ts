import { Test } from '@nestjs/testing';
import { SlimFileModule } from './file.module';
import { FileService } from './file.service';
import { File, StorageType } from '@app/models/file';
import { v4 as uuid } from 'uuid';
import { createTestOffice } from '../../test/utils/massa.utils';
import { DatabaseModule } from '@app/database.provider';
import { LocalFileOperator } from './implementations';
import { ConfigurationEnv } from '@app/config/configuration.env';
import { buffer } from 'stream/consumers';

type FileSutResult = {
  file: File;
};

type FileSutInput = {
  fileName?: string;
  storageName?: string;
  storageType?: StorageType;
};

const config = new ConfigurationEnv();

describe('Slim file service', () => {
  let svc: FileService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [SlimFileModule],
    })
      .overrideProvider(ConfigurationEnv)
      .useValue(config)
      .overrideModule(DatabaseModule)
      .useModule(DatabaseModule.register())
      .compile();

    const m = await module.init();
    svc = m.get(FileService);
  });

  afterEach(() => { });

  afterAll(() => { });

  const makeSut = async ({
    fileName,
    storageName,
    storageType,
  }: FileSutInput): Promise<FileSutResult> => {
    const office = await createTestOffice('test');

    const file = await File.create({
      office_id: office.id_escritorio,
      name: fileName ?? 'some_file.txt',
      storage_name: storageName ?? `${office.id_escritorio}_some_file.txt`,
      storage: storageType ?? StorageType.S3,
      checksum: uuid(),
      created_at: new Date(),
      updated_at: new Date(),
    });
    file.escritorio = office;

    return {
      file,
    };
  };

  it('errors when using an invalid storage type', async () => {
    const { file } = await makeSut({ storageType: StorageType.REMOTE_INTEGRATION }); // this isnt covered by slim file module
    expect(() => svc.getOperator(file.storage)).toThrowErrorMatchingInlineSnapshot(
      `[Error: invalid file operator type 4]`,
    );
  });

  it('returns non existing file if file doesnt exist', async () => {
    const { file } = await makeSut({ storageType: StorageType.LOCAL });
    const res = await svc.operatorFromFileId(file.office_id, -1);
    expect(res.exists).toBeFalsy();
    expect(res.operator).toBeNull();
  });

  it('returns non existing file if office id doesnt match', async () => {
    const { file } = await makeSut({ storageType: StorageType.LOCAL });
    const res = await svc.operatorFromFileId(-1, file.id);
    expect(res.exists).toBeFalsy();
    expect(res.operator).toBeNull();
  });

  it('returns an operator if file exists', async () => {
    const { file } = await makeSut({ storageType: StorageType.LOCAL });
    const res = await svc.operatorFromFileId(file.office_id, file.id);
    expect(res.exists).toBeTruthy();
    expect(res.operator).not.toBeNull();
    expect(res.operator).toBeInstanceOf(LocalFileOperator);
  });

  describe('when sending string office_id', () => {
    it('non existing when sending different office client_id', async () => {
      const { file } = await makeSut({ storageType: StorageType.LOCAL });
      const res = await svc.operatorFromFileId(file.escritorio.client_id + 'OIOIOI', file.id);
      expect(res.exists).toBeFalsy();
      expect(res.operator).toBeNull();
    });

    it('existing if client_id matches', async () => {
      const { file } = await makeSut({ storageType: StorageType.LOCAL });
      const res = await svc.operatorFromFileId(file.escritorio.client_id, file.id);
      expect(res.exists).toBeTruthy();
      expect(res.operator).not.toBeNull();
      expect(res.operator).toBeInstanceOf(LocalFileOperator);
    });
  });
  describe('local file operator', () => {
    it('writes and reads a local file', async () => {
      const { file } = await makeSut({
        storageType: StorageType.LOCAL,
        storageName: `${uuid()}-hello.txt`,
      });
      const res = await svc.operatorFromFileId(file.office_id, file.id);
      if (!res.exists) throw new Error('file should be created by SUT');

      const fileContent = 'OI';
      await res.operator.putFile(file, Buffer.from(fileContent, 'utf-8'));

      const readFileRes = await res.operator.getFileBuffer(file);
      const responseContent = await buffer(readFileRes);
      const resStr = responseContent.toString('utf-8');

      expect(resStr).toEqual(fileContent);
    });
  });
});
