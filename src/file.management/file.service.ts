import { Injectable } from '@nestjs/common';
import { FileAttributes, StorageType, File } from '../models/file';
import { Document } from '../models/document';
import { FileOperator } from './implementations/types';
import { AwsFileOperator, GCPFileOperator, LocalFileOperator } from './implementations';
import { Escritorio } from '../models/escritorio';

const GetOperatorNonExistingResponse = {
  exists: false,
  operator: null,
  fileRef: null,
} as const;

type GetOperatorResponse =
  | {
      exists: true;
      operator: FileOperator;
      fileRef: FileAttributes;
    }
  | typeof GetOperatorNonExistingResponse;

export type OfficeIdOrClientId = number | string;

@Injectable()
export class FileService {
  private readonly operators = new Map<StorageType, FileOperator>();
  constructor(
    private readonly awsOperator: AwsFileOperator,
    private readonly gcpOperator: GCPFileOperator,
    private readonly localOperator: LocalFileOperator,
  ) {
    this.operators.set(StorageType.S3, this.awsOperator);
    this.operators.set(StorageType.GCS, this.gcpOperator);
    this.operators.set(StorageType.LOCAL, this.localOperator);
  }

  public async operatorFromFileId(
    officeId: OfficeIdOrClientId,
    fileId: number,
  ): Promise<GetOperatorResponse> {
    const file = await File.findByPk(fileId, {
      include: [{ model: Escritorio, attributes: ['client_id'] }],
    });
    if (!file) return GetOperatorNonExistingResponse;

    return this.operatorFromFile(officeId, file);
  }

  public async operatorFromDocumentId(
    officeId: OfficeIdOrClientId,
    documentId: number,
  ): Promise<GetOperatorResponse> {
    const doc = await Document.findByPk(documentId, {
      attributes: ['id', 'file_id'],
      include: [{ model: File, include: [{ model: Escritorio, attributes: ['client_id'] }] }],
    });

    if (!doc?.file) return GetOperatorNonExistingResponse;
    return this.operatorFromFile(officeId, doc.file);
  }

  public async operatorFromFile(
    officeId: OfficeIdOrClientId,
    file: File,
  ): Promise<GetOperatorResponse> {
    const isFromOffice = await file.isFileFromOffice(officeId);
    if (!isFromOffice) return GetOperatorNonExistingResponse;

    return {
      exists: true,
      fileRef: file,
      operator: this.getOperator(file.storage),
    };
  }

  public getOperator(type: StorageType): FileOperator {
    const op = this.operators.get(type);
    if (!op) throw new Error(`invalid file operator type ${type}`);
    return op;
  }
}
