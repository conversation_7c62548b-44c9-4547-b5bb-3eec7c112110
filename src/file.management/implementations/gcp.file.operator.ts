import { Injectable } from '@nestjs/common';
import { FileOperator, PutFileOpts, PutFileResponse } from './types';
import { Readable } from 'stream';
import { GoogleAuthService } from '../../services/google/auth.service';
import { File, FileAttributes } from '../../models/file';

@Injectable()
export class GCPFileOperator implements FileOperator {
  constructor(private readonly _gcpAuth: GoogleAuthService) {}
  async getFileBuffer(file: File): Promise<Readable> {
    throw new Error('Method not implemented.');
  }
  async putFile(
    fileDef: FileAttributes,
    content: Buffer | Readable,
    opts?: PutFileOpts,
  ): Promise<PutFileResponse> {
    throw new Error('Method not implemented.');
  }
}
