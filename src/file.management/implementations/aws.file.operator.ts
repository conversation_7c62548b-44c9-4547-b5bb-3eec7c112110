import { Injectable } from '@nestjs/common';
import { FileOperator, PutFileOpts, PutFileResponse } from './types';
import { Readable } from 'stream';
import AWSService from '../../services/aws.sdk.service';
import { ConfigurationEnv } from '../../config/configuration.env';
import { File, FileAttributes } from '../../models/file';

@Injectable()
export class AwsFileOperator implements FileOperator {
  constructor(
    private readonly config: ConfigurationEnv,
    private readonly sdk: AWSService,
  ) {}

  async putFile(
    fileDef: FileAttributes,
    content: Buffer | Readable,
    opts?: PutFileOpts,
  ): Promise<PutFileResponse> {
    throw new Error('Method not implemented.');
  }

  async getFileBuffer(file: File): Promise<Readable> {
    return this.sdk.s3
      .getObject({
        Bucket: this.config.sheetFileContainer,
        Key: file.storage_name,
      })
      .createReadStream();
  }
}
