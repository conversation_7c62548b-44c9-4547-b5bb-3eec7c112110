import { Injectable } from '@nestjs/common';
import { FileOperator, PutFileOpts, PutFileResponse } from './types';
import { Readable } from 'stream';
import { ConfigurationEnv } from '../../config/configuration.env';
import { FileAttributes, File } from '../../models/file';
import { createReadStream, createWriteStream, promises as fsPromises, mkdirSync } from 'fs';

@Injectable()
export class LocalFileOperator implements FileOperator {
  private readonly basePath: string;

  constructor(private readonly cfg: ConfigurationEnv) {
    this.basePath = this.cfg.sheetFileContainer;
  }

  async putFile(
    fileDef: FileAttributes,
    content: Buffer | Readable,
    _opts?: PutFileOpts,
  ): Promise<PutFileResponse> {
    this.ensureBaseFolderExists();

    const fullPath = `${this.basePath}/${fileDef.storage_name}`;

    if (Buffer.isBuffer(content)) {
      await fsPromises.writeFile(fullPath, content);
    } else {
      await new Promise<void>((resolve, reject) => {
        const writeStream = createWriteStream(fullPath);
        content.pipe(writeStream);
        content.on('error', reject);
        writeStream.on('finish', () => resolve());
        writeStream.on('error', reject);
      });
    }

    return { file: File.build(fileDef) };
  }

  async getFileBuffer(file: FileAttributes): Promise<Readable> {
    const fullPath = `${this.basePath}/${file.storage_name}`;

    try {
      await fsPromises.access(fullPath);
    } catch {
      throw new Error(`file ${fullPath} not found`);
    }

    return createReadStream(fullPath);
  }

  private ensureBaseFolderExists() {
    return mkdirSync(this.basePath, { recursive: true });
  }
}
