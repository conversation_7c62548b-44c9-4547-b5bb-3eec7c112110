import { Readable } from 'node:stream';
import { File, FileAttributes } from '../../models/file';

export type PutFileResponse = {
  file: File;
};

export type PutFileOpts = {
  tags?: Record<string, string>;
};

export interface FileOperator {
  getFileBuffer(file: FileAttributes): Promise<Readable>;
  // getFile(): Promise<FileDefinition>
  // getSignerUrl(): Promise<string>
  putFile(
    fileDef: FileAttributes,
    content: Buffer | Readable,
    opts?: PutFileOpts,
  ): Promise<PutFileResponse>;
}
