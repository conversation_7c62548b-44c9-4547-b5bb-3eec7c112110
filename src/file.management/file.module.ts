import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule, DatabaseProviders } from '../database.provider';
import { LogModule } from '../log.module';
import { RequestMetadataModule } from '../middleware/cid.middleware';
import { GoogleAuthService } from '../services/google/auth.service';
import { ConfigurationModule } from '../configuration.module';
import { FileService } from './file.service';
import AWSService from '../services/aws.sdk.service';
import { AwsFileOperator, GCPFileOperator, LocalFileOperator } from './implementations';

const moduleProviders = [
  AWSService,
  GoogleAuthService,
  GCPFileOperator,
  AwsFileOperator,
  LocalFileOperator,
  FileService,
];

// maybe later bring the file models here
@Module({
  providers: moduleProviders,
  imports: [
    DatabaseModule,
    ConfigurationModule,
    RequestMetadataModule,
    LogModule.forRootAsync(),
  ],
  exports: [FileService, AwsFileOperator, GCPFileOperator, LocalFileOperator],
})
export class SlimFileModule {
  constructor() { }
}
