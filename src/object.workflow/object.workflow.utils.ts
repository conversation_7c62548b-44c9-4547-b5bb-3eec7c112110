import { ObjectWorkflowType } from 'shared-types';
import { SuccessfulTransition } from '../domain/payroll/status_graph/status.workflow/status.workflow.types';
import { Payroll_Data } from '../models/payroll_data';

export class ObjectWorkflowUtils {
  public static filterAsyncObjectHooks(successfulTransitions: SuccessfulTransition[]) {
    return successfulTransitions
      .filter(({ object_workflow }) => object_workflow.type === ObjectWorkflowType.PAYROLL_DATA)
      .map(({ object_workflow }) => {
        const payrollData = <Payroll_Data>object_workflow.model;
        payrollData.workflow_object = object_workflow;
        return payrollData;
      });
  }
}
