import type { Request } from 'express';
import { Body, Controller, Get, Param, Patch, Put, Query, Req } from '@nestjs/common';
import {
  GetObjectWorkflowResponse,
  GetStatusInfoResponse,
  ObjectWorkflowHistoryResponse,
  ObjectWorkflowStatusFilters,
  objectWorkflowStatusFiltersSchema,
  ObjectWorkflowType,
  ObjectWorkflowUpdateInputsResponse,
  ObjectWorkflowUpdateStatusResponse,
  PossibleWorkflowContexts,
  UpdateWorkflowInputs,
  updateWorkflowInputsDto,
  updateWorkflowStatusUnionSchema,
  UsersClaims,
  WorkflowStatusBulkUpdate,
  WorkflowStatusUpdateUnion,
} from 'shared-types';
import { Authenticated } from '../../decorators/authenticated.decorator';
import { ObjectWorkflowDomain } from '../domain/object.workflow.domain';
import { ZodValidationPipe } from '../../controller/pipes/ZodValidationPipe';

const isUpdateObjectWorkflowStatusBulk = (
  data: WorkflowStatusUpdateUnion,
): data is WorkflowStatusBulkUpdate => {
  return !Array.isArray(data) && 'status_mapping' in data;
};

@Authenticated(
  req => req.params.officeId,
  null,
  UsersClaims.FEATURE_PAYROLL_V2,
  UsersClaims.FEATURE_PAYROLL_REQUESTS,
  UsersClaims.OUTBOUND_WEBHOOKS_MANAGER,
  UsersClaims.PAYROLL_STATUS_MANAGER,
)
@Controller('/offices/:officeId/object/workflow')
export class ObjectWorkflowController {
  constructor(private readonly objectWorkflowDomain: ObjectWorkflowDomain) {}

  @Get('/:objectKey/context')
  async getWorkflowContext(
    @Req() request: Request,
    @Param('officeId') officeId: number,
    @Param('objectKey') objectKey: string,
  ): Promise<PossibleWorkflowContexts> {
    return this.objectWorkflowDomain.getWorkflowContext({
      requester: request.user,
      officeId,
      objectKey,
    });
  }

  @Get('/:objectKey/history')
  async getWorkflowHistory(
    @Req() request: Request,
    @Param('officeId') officeId: number,
    @Param('objectKey') objectKey: string,
  ): Promise<ObjectWorkflowHistoryResponse> {
    return this.objectWorkflowDomain.getWorkflowHistory({
      requester: request.user,
      officeId,
      objectKey: decodeURIComponent(objectKey),
    });
  }

  @Get('/:objectKey')
  async getWorkflow(
    @Req() request: Request,
    @Param('officeId') officeId: number,
    @Param('objectKey') objectKey: string,
  ): Promise<GetObjectWorkflowResponse> {
    return this.objectWorkflowDomain.getWorkflow({
      requester: request.user,
      officeId,
      objectKey,
    });
  }

  @Patch('/status')
  async updateWorkflowStatus(
    @Req() request: Request,
    @Param('officeId') officeId: number,
    @Body(new ZodValidationPipe(updateWorkflowStatusUnionSchema))
    objects: WorkflowStatusUpdateUnion,
    @Query(new ZodValidationPipe(objectWorkflowStatusFiltersSchema))
    query: Partial<ObjectWorkflowStatusFilters>,
  ): Promise<ObjectWorkflowUpdateStatusResponse> {
    if (isUpdateObjectWorkflowStatusBulk(objects)) {
      return this.objectWorkflowDomain.bulkUpdateWorkflowStatus({
        mapping: objects.status_mapping,
        officeId,
        requester: request.user,
        filters: {
          ...query,
          type: query.type ?? ObjectWorkflowType.PAYROLL_DATA,
        },
      });
    }

    return this.objectWorkflowDomain.updateWorkflowStatus({
      objects,
      officeId,
      requester: request.user,
      type: query?.type,
    });
  }

  @Get('/status/info')
  async getWorkflowStatusInfo(
    @Param('officeId') officeId: number,
    @Req() request: Request,
    @Query(new ZodValidationPipe(objectWorkflowStatusFiltersSchema))
    filters: ObjectWorkflowStatusFilters,
  ): Promise<GetStatusInfoResponse> {
    return this.objectWorkflowDomain.getWorkflowStatusInfo({
      officeId,
      requester: request.user,
      filters,
    });
  }

  @Put('/:objectKey/inputs')
  async updateWorkflowInputs(
    @Req() request: Request,
    @Param('officeId') officeId: number,
    @Param('objectKey') objectKey: string,
    @Body(new ZodValidationPipe(updateWorkflowInputsDto)) payload: UpdateWorkflowInputs,
  ): Promise<ObjectWorkflowUpdateInputsResponse> {
    return this.objectWorkflowDomain.updateWorkflowInputs({
      request,
      officeId,
      objectKey,
      payload,
    });
  }
}
