import { Logger } from '@nestjs/common';
import { Transaction } from 'sequelize';
import { Sequelize } from 'sequelize-typescript';
import {
  BackendErrorCodes,
  ObjectWorkflowEventsReasons,
  ObjectWorkflowType,
  PayrollPatchConfig,
  PreInitConfig,
  PreInitConfigOnErrorEnum,
} from 'shared-types';
import { AuthenticationContextDto } from '../../dto/authenticationContextDto';
import { Credor } from '../../models/credor';
import { Object_Workflow, ObjectWorkflowCreationAttributes } from '../../models/object_workflow';
import {
  Object_Workflow_Events,
  ObjectWorkflowEventCreationAttributes,
} from '../../models/object_workflow_events';
import { Payroll_Config } from '../../models/payroll_config';
import { Payroll_Data } from '../../models/payroll_data';
import { SplitcModel } from '../../types/extended-sequelize';
import { CustomException } from '../../utils/error.utils';
import { MapUtils } from '../../utils/map.utils';
import { getModelAttributes } from '../../utils/sequelize';
import { DispatchHooksService } from '../../domain/payroll/hooks/dispatch.hook.service';
import { PayrollDomain } from '../../domain/payroll/payroll.domain';
import { StatusGraph } from '../../domain/payroll/status_graph/status.graph';
import {
  InitWorkflowModel,
  InitWorkflowParams,
  MissingStatuses,
  StatusWorkflowFactoryOpts,
} from '../../domain/payroll/status_graph/status.workflow/status.workflow.types';
import { ConfigurationEnv } from '../../config/configuration.env';
import { sleep } from '../../utils/promise.utils';

export class ObjectWorkflowService {
  private readonly workflow: Payroll_Config;
  private readonly graph: StatusGraph;
  // office id should be get from the instance, not from the requester,
  // because the requester can be null in some cases (like a auto process payroll)
  // and workflow office id is nullable
  private readonly officeId: number;
  private readonly requesterUser: Credor;
  private readonly requester: AuthenticationContextDto;

  private readonly logger = new Logger(PayrollDomain.name);

  constructor(
    private readonly sequelize: Sequelize,
    private readonly dispatchHooksService: DispatchHooksService,
    private readonly config: ConfigurationEnv,
    opts: StatusWorkflowFactoryOpts,
  ) {
    this.workflow = opts.workflow;
    this.officeId = opts.officeId;
    this.graph = opts.workflow.getParsedGraph();
    this.requesterUser = opts.requester.getUserForAudit().credor;
    this.requester = opts.requester;
  }

  /**
   * Initializes a new workflow for an object, setting its initial status and creating the first event.
   *
   * @param {SplitcModel[]} model[] - The model of the object to create a workflow for.
   * @param {ObjectWorkflowEventsReasons} reason - The reason for the workflow creation.
   * @param {Transaction} trx - The   database transaction to ensure atomicity of operations.
   * @returns {Promise<InitFlowResponse>} - A response object containing the ID and status of the newly created workflow.
   */
  public async initWorkflow({
    models,
    reason,
    trx,
  }: InitWorkflowParams): Promise<Object_Workflow[]> {
    const credorsToFind = new Set(models.map(({ model }) => model.getObjectOwnerId()));
    const officeCreditors = await Credor.findAll({
      where: {
        escritorio_id_escritorio: this.officeId,
        id_credor: Array.from(credorsToFind),
      },
      transaction: trx,
    });
    const officeCreditorsMap = MapUtils.arrayToMap<number, Credor>(officeCreditors, 'id_credor');

    const { workflowObjectsToCreate, eventsToCreate } = await this.buildWorkflowObjectsToCreate(
      officeCreditorsMap,
      models,
    );

    this.logger.debug('creating workflow objects');
    await Object_Workflow.bulkCreate(workflowObjectsToCreate, {
      transaction: trx,
      updateOnDuplicate: ['status', 'input_schema', 'updated_at', 'workflow_id', 'external_data'],
    });

    // Fetch the created workflow objects to get correctly IDs
    this.logger.debug('getting created workflow objects');
    const objWorkflows = await Object_Workflow.findAll({
      where: {
        office_id: this.officeId,
        object_key: workflowObjectsToCreate.map(o => o.object_key),
      },
      transaction: trx,
    });

    // Map workflow objects by key and
    // Associate events with workflow objects
    const objectWorkflowByKey = MapUtils.arrayToMap<string, Object_Workflow>(
      objWorkflows,
      'object_key',
    );
    const eventsWithFk = eventsToCreate.map(({ _objectKey, ...event }) => {
      const objWorkflow = objectWorkflowByKey.get(_objectKey);
      if (!objWorkflow) return null;

      const metadata = {
        old_status_name: this.graph.getStatusDef(event.old_status)?.name,
        status_name: this.graph.getStatusDef(event.status)?.name,
      };
      return {
        ...event,
        object_workflow_id: objWorkflow.id,
        reason,
        metadata,
      };
    });

    const filteredEvents = eventsWithFk.filter(Boolean);

    this.logger.debug('creating object workflow events');
    await Object_Workflow_Events.bulkCreate(filteredEvents, { transaction: trx });
    this.logger.debug('created object workflow events');

    return objWorkflows;
  }

  /**
   * this is the same as `initWorkflow`, but instead of creating stuff it returns the object that would be created
   */
  public async dryInitWorkflow(model: SplitcModel): Promise<Object_Workflow> {
    const obj = Object_Workflow.build(this.buildWorkflowObject(model));
    obj.fillWorkflowModel(model);
    obj.workflow = this.workflow;
    obj.creator = this.requesterUser;
    await obj.loadOwner();
    return obj;
  }

  private async buildWorkflowObjectsToCreate(
    officeCreditorsMap: Map<number, Credor>,
    models: InitWorkflowModel[],
  ): Promise<{
    workflowObjectsToCreate: ObjectWorkflowCreationAttributes[];
    eventsToCreate: (ObjectWorkflowEventCreationAttributes & { _objectKey: string })[];
  }> {
    const initialStatus = this.graph.getInitialStatus();
    const { hooks: preInitHooks, on_error: onHookError } = this.graph.getPreInitConfig();

    if (!preInitHooks?.length) {
      const { workflowObjects: workflowObjectsToCreate, events: eventsToCreate } =
        await this.batchBuildWorkflowObjectsAndEvents({
          officeCreditorsMap,
          objects: models,
          initialStatus,
          preInitHooks,
          onHookError,
        });

      return {
        workflowObjectsToCreate,
        eventsToCreate,
      };
    }

    const eventsToCreate: (ObjectWorkflowEventCreationAttributes & { _objectKey: string })[] = [];
    const workflowObjectsToCreate: ObjectWorkflowCreationAttributes[] = [];

    const batches = Math.ceil(models.length / this.config.batchStatusUpdateSize);
    for (let i = 0; i < batches; i++) {
      const start = i * this.config.batchStatusUpdateSize;
      const end = start + this.config.batchStatusUpdateSize;
      const batchObjects = models.slice(start, end);

      const { workflowObjects, events } = await this.batchBuildWorkflowObjectsAndEvents({
        officeCreditorsMap,
        objects: batchObjects,
        initialStatus,
        preInitHooks,
        onHookError,
      });

      workflowObjectsToCreate.push(...workflowObjects);
      eventsToCreate.push(...events);

      if (i < batches - 1) await sleep(this.config.batchStatusMsDelay);
    }

    return {
      workflowObjectsToCreate,
      eventsToCreate,
    };
  }

  private async batchBuildWorkflowObjectsAndEvents({
    officeCreditorsMap,
    objects,
    initialStatus,
    preInitHooks,
    onHookError,
  }: {
    officeCreditorsMap: Map<number, Credor>;
    objects: InitWorkflowModel[];
    initialStatus: string;
    preInitHooks: PreInitConfig['hooks'];
    onHookError: PreInitConfigOnErrorEnum;
  }): Promise<{
    workflowObjects: ObjectWorkflowCreationAttributes[];
    events: (ObjectWorkflowEventCreationAttributes & { _objectKey: string })[];
  }> {
    const eventsToCreate: (ObjectWorkflowEventCreationAttributes & { _objectKey: string })[] = [];
    const workflowObjectsToCreate: ObjectWorkflowCreationAttributes[] = [];

    await Promise.all(
      objects.map(async ({ model, modelHasChanged }) => {
        const existingWorkflowObj = model.workflow_object;

        const objectKey = model.getWorkflowKey();
        const initialEvent = {
          // object key is used to get object workflow id after creation
          _objectKey: objectKey,
          requester: this.requesterUser?.id_credor,
          status: this.graph.getInitialStatus(),
          office_id: this.officeId,
          data: getModelAttributes(model),
          old_status: existingWorkflowObj?.status,
          object_workflow_id: existingWorkflowObj?.id,
        };

        const shouldReturnToInitialStatus = modelHasChanged || !existingWorkflowObj;

        const objectWorkflowData = {
          office_id: this.officeId,
          object_key: objectKey,
          type: model.TABLE_NAME as ObjectWorkflowType,
          status: shouldReturnToInitialStatus ? initialStatus : existingWorkflowObj?.status,
          workflow_id: this.workflow.id,
          created_by: this.requesterUser?.id_credor,
          object_owner: model.getObjectOwnerId() ?? this.requesterUser?.id_credor,
          input_schema: this.workflow.config?.input_schema,
        };

        // Build the object workflow with necessary associations to make dispatch hooks work
        const objectWorkflow = Object_Workflow.build(objectWorkflowData);
        const owner = officeCreditorsMap.get(objectWorkflowData.object_owner);
        objectWorkflow.fillWorkflowModel(model);
        objectWorkflow.workflow = this.workflow;
        objectWorkflow.creator = this.requesterUser;
        objectWorkflow.owner = owner;

        // Fetch preInit hooks
        const { success, response } = await this.dispatchHooksService.dispatchSyncHooks(
          this.requester,
          objectWorkflow,
          preInitHooks,
          [],
        );

        // If the hook fails, we need to check the on_error config
        if (!success) {
          if (onHookError === PreInitConfigOnErrorEnum.ABORT_ALL) {
            throw new CustomException(BackendErrorCodes.PRE_INIT_HOOK_ERROR);
          } else if (onHookError === PreInitConfigOnErrorEnum.NOT_CREATE) {
            return;
          }
        }

        // If the hook is successful, we need to create a valid external_data
        const responseData: unknown[] = success && preInitHooks?.length ? response : [];
        const external_data = this.makeExternalDataWithHookResponse(responseData);

        if (shouldReturnToInitialStatus) eventsToCreate.push(initialEvent);
        workflowObjectsToCreate.push({
          ...objectWorkflowData,
          external_data,
        });
      }),
    );

    return {
      workflowObjects: workflowObjectsToCreate,
      events: eventsToCreate,
    };
  }

  /**
   * Updates some information of a workflow.
   *
   * @param {PayrollPatchConfig} updateConfig - The configuration to update the workflow.
   */
  public async updateWorkflow({
    status_flow_config,
    config,
    name,
    status_normalization,
  }: PayrollPatchConfig): Promise<Payroll_Config> {
    if (name) this.workflow.name = name;

    if (config) this.workflow.config = config;

    await this.sequelize.transaction(async trx => {
      if (status_flow_config) {
        this.workflow.status_flow_config = await this.updateWorkflowStatuses(
          { status_flow_config, status_normalization },
          trx,
        );
      }

      await this.workflow.save({ transaction: trx });
    });

    return this.workflow;
  }

  public getMissingStatuses(
    newGraph: StatusGraph,
    objectWorkflows: Object_Workflow[],
  ): MissingStatuses[] {
    const newGraphStatus = new Set(newGraph.nodes());

    const currentStatusGraph = new Map<string, MissingStatuses>();

    for (const nodeEntry of this.graph.nodeEntries()) {
      currentStatusGraph.set(nodeEntry.node, {
        code: nodeEntry.node,
        name: nodeEntry.attributes.def.name,
      });
    }

    const allStatusInUse = new Set<string>();

    const events = objectWorkflows.flatMap(o => o.events);
    objectWorkflows.forEach(({ status }) => allStatusInUse.add(status));
    events.forEach(({ old_status, status }) => {
      if (old_status) allStatusInUse.add(old_status);
      allStatusInUse.add(status);
    });

    const missingStatus: { code: string; name: string }[] = [];

    Array.from(allStatusInUse).forEach(status => {
      if (newGraphStatus.has(status)) return;
      if (!currentStatusGraph.has(status)) return;

      const nodeDef = currentStatusGraph.get(status);
      missingStatus.push(nodeDef);
    });

    return missingStatus;
  }

  private createGraph({ status_flow_config }: PayrollPatchConfig) {
    try {
      return new StatusGraph(status_flow_config);
    } catch (e) {
      const error = e as Error;
      throw new CustomException(BackendErrorCodes.INVALID_GRAPH, {
        error: error.message,
      });
    }
  }

  private async updateWorkflowStatuses(
    { status_flow_config, status_normalization }: PayrollPatchConfig,
    trx: Transaction,
  ) {
    const oldGraph = this.workflow.getParsedGraph();
    const newGraph = this.createGraph({ status_flow_config });

    const objectsUsingWorkflow = await Object_Workflow.scope([
      'withEvents',
      {
        method: ['withModel', this.officeId, ObjectWorkflowType.PAYROLL_DATA],
      },
    ]).findAll({
      where: { workflow_id: this.workflow.id },
    });
    if (!objectsUsingWorkflow.length) return status_flow_config;

    const statusMap = new Map<string, Object_Workflow[]>();
    objectsUsingWorkflow.forEach(obj => {
      const status = obj.status;
      if (!statusMap.has(status)) statusMap.set(status, []);
      statusMap.get(status)?.push(obj);
    });

    const missingStatus = this.getMissingStatuses(newGraph, objectsUsingWorkflow);
    if (!missingStatus.length) return status_flow_config;

    if (missingStatus.length && !status_normalization)
      throw new CustomException(BackendErrorCodes.STATUS_NORMALIZATION_MUST_BE_PROVIDE);

    this.logger.debug({ missingStatus }, 'missing statuses to update');
    missingStatus.forEach(({ code }) => {
      if (!status_normalization[code])
        throw new CustomException(BackendErrorCodes.STATUS_NORMALIZATION_NOT_FOUND, {
          old_status: code,
        });
    });

    const eventsToCreate: ObjectWorkflowEventCreationAttributes[] = [];
    await Promise.all(
      missingStatus.map(async ({ code }) => {
        const newStatus = status_normalization[code];
        const metadata = {
          old_status_name: oldGraph.getStatusDef(code)?.name,
          status_name: newGraph.getStatusDef(newStatus)?.name,
        };

        const objectWorkflowInStatus = statusMap.get(code);
        objectWorkflowInStatus.forEach(obj => {
          eventsToCreate.push({
            office_id: this.officeId,
            status: newStatus,
            object_workflow_id: obj.id,
            old_status: code,
            requester: this.requesterUser?.id_credor,
            data: getModelAttributes(obj.model),
            reason: ObjectWorkflowEventsReasons.CONFIG_CHANGE,
            metadata,
          });
        });

        await Object_Workflow.update(
          { status: newStatus },
          { where: { status: code, workflow_id: this.workflow.id }, transaction: trx },
        );
      }),
    );

    await Object_Workflow_Events.bulkCreate(eventsToCreate, { transaction: trx });
    return status_flow_config;
  }

  /**
   * @returns the workflow object with initial status
   */
  private buildWorkflowObject(model: SplitcModel): ObjectWorkflowCreationAttributes {
    const key = model.getWorkflowKey();
    return {
      office_id: this.officeId,
      object_key: key,
      type: model.TABLE_NAME as ObjectWorkflowType,
      status: this.graph.getInitialStatus(),
      workflow_id: this.workflow.id,
      created_by: this.requesterUser?.id_credor,
      object_owner: model.getObjectOwnerId() ?? this.requesterUser?.id_credor,
      input_schema: this.workflow.config?.input_schema,
    };
  }

  private makeExternalDataWithHookResponse(response: unknown[]): Record<string, unknown> | null {
    if (!response.length) return null;

    return response.flat().reduce<Record<string, unknown>>((acc, obj, index) => {
      // Merge all records into one
      if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
        acc = { ...acc, ...obj };
        return acc;
      }

      acc[`hook_response_${index}`] = obj;
      return acc;
    }, {});
  }
}
