import type { Request } from 'express';
import { Sequelize } from 'sequelize-typescript';
import { Transaction } from 'sequelize';
import { Object_Workflow } from '../../models/object_workflow';
import { CustomException } from '../../utils/error.utils';
import {
  BackendErrorCodes,
  DOCUMENT_FIELD_INPUT_SCHEMA_ID,
  DOCUMENT_VALIDATION_FIELD_INPUT_SCHEMA_ID,
  OBJECT_WORKFLOW_KEY_TAG,
  ObjectWorkflowEventsReasons,
  ResetConfigActionType,
  UpdateWorkflowInputs,
  UsersClaims,
} from 'shared-types';
import { JsonSchemaUtils } from '../../utils/json.schema.utils';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { getValueByPath } from '../../utils/object.utils';
import { MapUtils } from '../../utils/map.utils';
import { Document, DocumentCreationAttributes } from '../../models/document';
import { File } from '../../models/file';
import { Document_tag } from '../../models/document_tag';
import { AuthenticationContextDto } from '../../dto/authenticationContextDto';
import { DATABASE_PROVIDER } from '../../database.provider';
import { checkListDocumentsPermission } from '../../controller/document/permissions';
import { DocumentNotifyService } from '../../domain/documents/service/document.notify.service';
import { Credor } from '../../models/credor';
import { OcrDomain } from '../../ocr/ocr.domain';
import { ObjectWorkflowRepository } from '../../domain/repository/object.workflow.repository';
import { DispatchHooksService } from '../../domain/payroll/hooks/dispatch.hook.service';
import { ObjectWorkflowUtils } from '../object.workflow.utils';
import { WorkflowObjectStatusService } from './workflow.object.status.service';
import { JSONSchema7 } from 'json-schema';

interface CreatePaymentRequestDocumentsParams {
  objWorkflow: Object_Workflow;
  filesToCreateDocument: Record<string, number[]>;
  trx?: Transaction;
}

@Injectable()
export class WorkflowObjectInputsService {
  private readonly logger: Logger = new Logger(WorkflowObjectInputsService.name);

  private readonly DOCUMENT_INPUT_FIELDS = [
    DOCUMENT_FIELD_INPUT_SCHEMA_ID,
    DOCUMENT_VALIDATION_FIELD_INPUT_SCHEMA_ID,
  ];

  constructor(
    @Inject(DATABASE_PROVIDER)
    private readonly sequelize: Sequelize,
    private readonly documentNotifySvc: DocumentNotifyService,
    private readonly ocrDomain: OcrDomain,
    private readonly objectWorkflowRepository: ObjectWorkflowRepository,
    private readonly workflowStatusTransition: WorkflowObjectStatusService,
    private readonly dispatchHooksService: DispatchHooksService,
  ) {}

  private removeDocumentsByIds(deletingDocumentsIds: number[], workflowDocs: string[]) {
    return workflowDocs.filter(
      workflowDocumentId => deletingDocumentsIds.indexOf(+workflowDocumentId) === -1,
    );
  }

  private getWorkflowsByDocumentIds = async (
    officeId: number,
    documentIds: number[],
    transaction?: Transaction,
  ): Promise<Object_Workflow[]> => {
    const tags = await Document_tag.findAll({
      where: {
        document_id: documentIds,
        tag_name: OBJECT_WORKFLOW_KEY_TAG,
      },
      attributes: ['tag_value'],
      transaction,
    });

    return Object_Workflow.findAll({
      where: {
        object_key: tags.map(tag => tag.tag_value),
        office_id: officeId,
      },
      transaction,
    });
  };

  private async clearDocumentFromWorkflow(
    input_schema: JSONSchema7,
    input_data: Record<string, any>,
    documentIds: number[],
    workflow: Object_Workflow,
    transaction?: Transaction,
  ) {
    const cleardDocuments = {};
    await Promise.all(
      this.DOCUMENT_INPUT_FIELDS.map(async schemaId => {
        const prop = JsonSchemaUtils.findPropertyById(input_schema, schemaId);
        if (!prop) return;

        const documents: string[] = getValueByPath(input_data, prop.path) || [];
        if (!documents.length) return;

        const clearedDocs = this.removeDocumentsByIds(documentIds, documents);

        cleardDocuments[prop.path] = clearedDocs;
      }),
    );

    if (!Object.keys(cleardDocuments).length) return;

    await Object_Workflow.update(
      { input_data: { ...input_data, ...cleardDocuments } },
      {
        where: { id: workflow.id },
        transaction,
      },
    );
  }

  async deleteDocumentsFromWorkflows(
    officeId: number,
    documentIds: number[],
    transaction?: Transaction,
  ) {
    const workflows = await this.getWorkflowsByDocumentIds(officeId, documentIds, transaction);

    await Promise.all(
      workflows.map(async workflow => {
        const { input_schema, input_data } = workflow;
        if (!input_data) return;

        await this.clearDocumentFromWorkflow(
          input_schema,
          input_data,
          documentIds,
          workflow,
          transaction,
        );
      }),
    );
  }

  async validateDocumentsOCR({
    objWorkflow,
    requester,
    trx,
  }: {
    requester: AuthenticationContextDto;
    objWorkflow: Object_Workflow;
    trx?: Transaction;
  }) {
    const { input_data, input_schema } = objWorkflow;
    const ocrValidationData = JsonSchemaUtils.findPropertyById(
      input_schema,
      DOCUMENT_VALIDATION_FIELD_INPUT_SCHEMA_ID,
    );
    const [hasOCRModuleAllowed] = requester.findSessionClaim(UsersClaims.ALLOWED_OCR_MODULE);

    if (!ocrValidationData || !hasOCRModuleAllowed) {
      return {};
    }

    // TODO: fix type
    const validationId = ocrValidationData?.schema['$ocr_validation_config_id'] as
      | number
      | undefined;

    // TODO: fix type
    const documents = (getValueByPath(input_data, ocrValidationData.path) ?? []) as (
      | number
      | string
    )[];

    if (!documents.length) {
      return {};
    }

    const files = await this.objectWorkflowRepository.getFilesByDocuments(documents, trx);

    const validations = await Promise.all(
      files.map(async file =>
        this.ocrDomain.validateExtractionResult({
          officeId: objWorkflow.office_id,
          validationId,
          fileId: file.id,
          context: objWorkflow.makeContext(requester),
          trx,
        }),
      ),
    );

    const successfulValidations = validations.every(validation =>
      validation.results.every(result => result.success),
    );

    return {
      path: ocrValidationData.path,
      validations: {
        [ocrValidationData.path]: validations,
      },
      action: successfulValidations
        ? ResetConfigActionType.ON_INPUT_SUCCESS
        : ResetConfigActionType.ON_INPUT_FAILURE,
    };
  }

  /**
   * Updates the inputs of a workflow.
   *
   * @param {Object_Workflow} objectWorkflow - The workflow object to update.
   * @param {UpdateWorkflowInputs} payload - The new inputs to set and new files.
   * @returns {Promise<void>} - A promise that resolves when the inputs have been updated.
   */
  public async updateInputs(
    request: Request,
    objWorkflow: Object_Workflow,
    payload: UpdateWorkflowInputs,
  ) {
    const { user: requester } = request;
    if (!objWorkflow) throw new CustomException(BackendErrorCodes.OBJECT_WORKFLOW_NOT_FOUND);
    const isAdmin = requester.hasClaims([UsersClaims.PAYROLL_STATUS_MANAGER]);
    const canSeeCreditor = await requester.canSeeCreditor(objWorkflow.owner.id_credor_externo);

    if (!isAdmin && !canSeeCreditor)
      throw new CustomException(BackendErrorCodes.OBJECT_WORKFLOW_REQUESTER_NOT_ALLOWED);

    const graph = objWorkflow.workflow.getParsedGraph();
    const allowInputInsertion = objWorkflow.checkIfAllowInputInsertion();
    if (!allowInputInsertion)
      throw new CustomException(BackendErrorCodes.OBJECT_WORKFLOW_INPUT_INSERTION_NOT_ALLOWED);

    const schema = objWorkflow.input_schema;
    if (!schema)
      throw new CustomException(BackendErrorCodes.OBJECT_WORKFLOW_INPUT_SCHEMA_NOT_FOUND);

    const { documents, validations } = await this.sequelize.transaction(async trx => {
      const documentsCreated = await this.addDocumentsOnInput(requester, payload, objWorkflow, trx);

      const creditors = [objWorkflow.owner.id_credor_externo];
      const { documentsToAdd, documentsToRemove } = this.determineDocumentChanges(
        objWorkflow,
        payload.inputs,
      );

      if (documentsToAdd.length) {
        await checkListDocumentsPermission(request, creditors, 'create');
      }

      if (documentsToRemove.length) {
        await checkListDocumentsPermission(request, creditors, 'delete');
      }

      await this.deleteDocumentsInputRequest(documentsToRemove, objWorkflow.office_id, trx);

      JsonSchemaUtils.getDocumentFieldsProperties(schema, this.DOCUMENT_INPUT_FIELDS).map(
        ({ path, schema: propertySchema }) => {
          if (propertySchema.type !== 'array') return;

          const itemsType = propertySchema.items?.['type'];
          const inputValues = payload.inputs[path] as (number | string)[];

          if (!Array.isArray(inputValues)) return;

          payload.inputs[path] = inputValues.map(value => {
            return itemsType === 'string' ? String(value) : Number(value);
          });
        },
      );

      const { isValid, errors } = JsonSchemaUtils.validateJsonSchema(
        payload.inputs,
        objWorkflow.input_schema,
      );

      if (!isValid)
        throw new CustomException(BackendErrorCodes.OBJECT_WORKFLOW_INVALID_INPUTS, { errors });

      // update the workflow input data prop to create context inside validateDocumentsOCR
      objWorkflow.setDataValue('input_data', payload.inputs);

      const {
        action = undefined,
        validations = undefined,
        path,
      } = await this.validateDocumentsOCR({
        objWorkflow,
        requester,
        trx,
      });

      const hasValidations = validations?.[path].length;
      const resetConfigAction = this.getResetConfigAction({
        documentsToAdd,
        documentsToRemove,
      });

      const hasValidationConfig =
        !!(
          graph.getStatusToReset(objWorkflow.status, ResetConfigActionType.ON_INPUT_FAILURE) ||
          graph.getStatusToReset(objWorkflow.status, ResetConfigActionType.ON_INPUT_SUCCESS)
        ) && hasValidations;

      const newStatus = graph.getStatusToReset(
        objWorkflow.status,
        hasValidationConfig ? action : resetConfigAction,
      );

      if (newStatus && !graph.getStatusDef(newStatus)) {
        throw new CustomException(BackendErrorCodes.OBJECT_WORKFLOW_INVALID_NEXT_STATUS);
      }

      const reason = hasValidationConfig
        ? ObjectWorkflowEventsReasons[action]
        : documentsToAdd.length
          ? ObjectWorkflowEventsReasons.ON_DOCUMENT_UPLOAD
          : ObjectWorkflowEventsReasons.ON_DOCUMENT_DELETE;

      const { success } = await this.workflowStatusTransition.bulkStatusTransition({
        requester,
        objWorkflowsWithStatus: [{ new_status: newStatus, obj_workflow: objWorkflow }],
        reason,
        validateNextStatus: false,
        trx,
      });

      const payrollDataSuccessfullyUpdated = ObjectWorkflowUtils.filterAsyncObjectHooks(success);

      await this.dispatchHooksService.dispatchHooks(payrollDataSuccessfullyUpdated, requester);

      return { documents: documentsCreated, validations };
    });

    const requesterMap = new Map<string, Credor>([
      [objWorkflow.owner.id_credor_externo, objWorkflow.owner],
    ]);

    await this.documentNotifySvc.notifyUploadedDocuments(
      objWorkflow.office_id,
      documents,
      requesterMap,
    );

    return { validations };
  }

  private async addDocumentsOnInput(
    requester: AuthenticationContextDto,
    payload: UpdateWorkflowInputs,
    objWorkflow: Object_Workflow,
    trx: Transaction,
  ): Promise<Document[]> {
    if (!payload.files_to_insert || !Object.keys(payload.files_to_insert)?.length) return [];

    // parse { [input]: [file_id] } to { [input]: [document_id] }
    const { inputsDocuments, documentsCreated } = await this.createPaymentRequestDocuments(
      requester,
      objWorkflow.owner,
      {
        objWorkflow,
        filesToCreateDocument: payload.files_to_insert,
        trx,
      },
    );

    // modify inputs to include the new documents
    Object.entries(inputsDocuments).forEach(([key, documents]) => {
      const inputValue = payload.inputs[key];
      if (Array.isArray(inputValue)) {
        payload.inputs[key] = [...inputValue, ...documents];
        return;
      }
      payload.inputs[key] = documents;
    });

    return documentsCreated;
  }

  private async createPaymentRequestDocuments(
    requester: AuthenticationContextDto,
    objectOwner: Credor,
    data: CreatePaymentRequestDocumentsParams,
  ): Promise<{ inputsDocuments: Record<string, number[]>; documentsCreated: Document[] }> {
    const { objWorkflow, filesToCreateDocument, trx } = data;
    const files = await File.findAll({
      where: {
        office_id: objWorkflow.office_id,
        id: Object.values(filesToCreateDocument).flat(),
      },
      transaction: trx,
    });
    const filesMap = MapUtils.arrayToMap<number, File>(files, 'id');

    const { date, source } = await objWorkflow.model.getDocumentAttributes();
    const inputsDocuments: Record<string, number[]> = {};

    const promises = Object.entries(filesToCreateDocument).map(async ([key, fileIds]) => {
      const documentsToCreate: DocumentCreationAttributes[] = fileIds.flatMap(fileId => {
        const file = filesMap.get(fileId);
        if (!file) return [];

        return [
          {
            created_by: requester.getUserForAudit().credor.id_credor,
            approval: 'PENDING',
            creditor_id: objWorkflow.object_owner,
            source,
            file_id: fileId,
            name: file.name,
            date,
            tags: [
              {
                tag_name: OBJECT_WORKFLOW_KEY_TAG,
                tag_value: objWorkflow.object_key,
              },
            ],
          },
        ];
      });

      const documents = await Promise.all(
        documentsToCreate.map(doc =>
          Document.create(doc, {
            include: [{ model: Document_tag, as: 'tags' }],
            transaction: trx,
          }),
        ),
      );

      // how all documents are created to object owner we populate credor on document using object owner to avoid make one more query after create
      documents.forEach(d => {
        d.credor = objectOwner;
      });

      inputsDocuments[key] = documents.map(d => d.id);

      return documents;
    });

    const documentsCreated = (await Promise.all(promises)).flat();

    return { inputsDocuments, documentsCreated };
  }

  private async deleteDocumentsInputRequest(
    documentsToRemove: string[],
    officeId: number,
    trx: Transaction,
  ) {
    const documentsToBeRemoved = await Document.findAll({
      where: { id: documentsToRemove },
      include: [{ model: File, where: { office_id: officeId } }],
      transaction: trx,
    });

    const deletedDocumentIds = documentsToBeRemoved.map(d => d.id);
    this.logger.debug(
      {
        documentsToRemove,
        deletedDocumentIds,
      },
      'prepares and executes document deletion',
    );
    await Document.destroy({
      where: { id: deletedDocumentIds },
      transaction: trx,
    });
  }

  private determineDocumentChanges(
    objWorkflow: Object_Workflow,
    newInputs: Record<string, unknown>,
  ): { documentsToAdd: string[]; documentsToRemove: string[] } {
    const properties = JsonSchemaUtils.getDocumentFieldsProperties(
      objWorkflow.input_schema,
      this.DOCUMENT_INPUT_FIELDS,
    );
    if (!properties?.length) return { documentsToAdd: [], documentsToRemove: [] };

    const documentsToAdd: string[] = [];
    const documentsToRemove: string[] = [];

    properties.forEach(({ path }) => {
      const newDocumentIds = new Set(getValueByPath<string[]>(newInputs, path));
      const currentDocumentIds = new Set(getValueByPath<string[]>(objWorkflow.input_data, path));

      const toRemove = [...currentDocumentIds].filter(x => !newDocumentIds.has(x));
      const toAdd = [...newDocumentIds].filter(x => !currentDocumentIds.has(x));
      documentsToAdd.push(...toAdd);
      documentsToRemove.push(...toRemove);
    });

    this.logger.debug({ documentsToRemove, documentsToAdd }, 'determining document changes');

    return { documentsToAdd, documentsToRemove };
  }

  private getResetConfigAction({
    documentsToAdd,
    documentsToRemove,
  }: {
    documentsToAdd: string[];
    documentsToRemove: string[];
  }): ResetConfigActionType {
    this.logger.debug(
      { documentsToAdd, documentsToRemove },
      'determining reset action based on document changes',
    );
    if (!documentsToAdd.length && !documentsToRemove.length) return;

    return documentsToAdd.length
      ? ResetConfigActionType.ON_DOCUMENT_UPLOAD
      : ResetConfigActionType.ON_DOCUMENT_DELETE;
  }
}
