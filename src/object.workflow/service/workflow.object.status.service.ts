import { Sequelize } from 'sequelize-typescript';
import { Object_Workflow } from '../../models/object_workflow';
import { Object_Workflow_Events } from '../../models/object_workflow_events';
import { AuthenticationContextDto } from '../../dto/authenticationContextDto';
import { CustomException } from '../../utils/error.utils';
import { BackendErrorCodes, ObjectWorkflowEventsReasons } from 'shared-types';
import { ObjectWorkflowWithTargetStatus } from '../../domain/payroll/status_graph/status.workflow/status.workflow.types';
import {
  BulkStatusTransitionResult,
  FailedTransition,
  SuccessfulTransition,
} from '../../domain/payroll/status_graph/status.workflow/status.workflow.types';
import { getModelAttributes } from '../../utils/sequelize';
import { StatusWorkflowEvaluator } from '../../domain/payroll/status_graph/status.workflow/status.workflow.evaluator';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { DATABASE_PROVIDER } from '../../database.provider';
import { Transaction } from 'sequelize';
import { DispatchHooksService } from '../../domain/payroll/hooks/dispatch.hook.service';
import { merge } from 'allof-merge';

export interface ObjectToChange {
  objWorkflow: Object_Workflow;
  object_data: Record<string, unknown>;
  new_status: string;
  hookResponse: Record<string, unknown>[];
  input_schema?: Record<string, string>;
  input_data?: Record<string, any>;
}

@Injectable()
export class WorkflowObjectStatusService {
  private readonly logger = new Logger(WorkflowObjectStatusService.name);

  constructor(
    @Inject(DATABASE_PROVIDER) private readonly sequelize: Sequelize,
    private readonly dispatchHooksService: DispatchHooksService,
    private readonly statusWorkflowEvaluator: StatusWorkflowEvaluator,
  ) {}

  /**
   * Moves multiple objects to a new status in bulk, handling workflows and events.
   *
   * @param {ObjectWorkflowToUpdateStatus[]} objects - The objects to transition, including their keys, new status, context, and model.
   * @returns {Promise<ProcessResult>} - A result object containing successful transitions and failures.
   */
  public async bulkStatusTransition({
    requester,
    objWorkflowsWithStatus,
    reason,
    trx,
    validateNextStatus = true,
  }: {
    requester: AuthenticationContextDto;
    objWorkflowsWithStatus: ObjectWorkflowWithTargetStatus[];
    reason: ObjectWorkflowEventsReasons;
    trx?: Transaction;
    validateNextStatus?: boolean;
  }): Promise<BulkStatusTransitionResult> {
    const { validNextStatus, invalidNextStatus } =
      await this.statusWorkflowEvaluator.evaluateAvailableNextStatus(
        requester,
        objWorkflowsWithStatus,
      );

    if (validateNextStatus && !validNextStatus.length)
      return { success: [], failures: invalidNextStatus };

    const { successfulDispatched, failedToDispatch } = await this.dispatchHooks(
      requester,
      validateNextStatus ? validNextStatus : objWorkflowsWithStatus,
    );

    const success = await this.updateWorkflowAndCreateEvents({
      requester,
      toChange: successfulDispatched,
      reason,
      trx,
    });

    return {
      success,
      failures: invalidNextStatus.concat(failedToDispatch),
    };
  }

  private async dispatchHooks(
    requester: AuthenticationContextDto,
    objects: ObjectWorkflowWithTargetStatus[],
  ): Promise<{
    failedToDispatch: FailedTransition[];
    successfulDispatched: ObjectToChange[];
  }> {
    const failedToDispatch: FailedTransition[] = [];

    const promises = await Promise.allSettled(
      objects.map(async ({ obj_workflow, new_status, hook_inputs }) => {
        if (!obj_workflow) {
          failedToDispatch.push({
            error: new CustomException(BackendErrorCodes.OBJECT_WORKFLOW_NOT_FOUND),
            object_workflow: obj_workflow,
          });
          return;
        }

        const inputData =
          hook_inputs?.length > 0
            ? hook_inputs.reduce((acc, input) => ({ ...acc, ...(input.input || {}) }), {})
            : {};

        const mergedInputData = { ...obj_workflow.input_data, ...inputData };

        // Assign the merged input data to `obj_workflow` so it can be accessed within the hooks context
        obj_workflow.input_data = mergedInputData;

        const graph = obj_workflow.workflow.getParsedGraph();
        const hookEdges = graph.tryGetEdgeHooks(obj_workflow.status, new_status);
        const { success, response } = await this.dispatchHooksService.dispatchSyncHooks(
          requester,
          obj_workflow,
          hookEdges,
          hook_inputs,
        );
        if (!success) {
          failedToDispatch.push({
            error: new CustomException(BackendErrorCodes.OBJECT_WORKFLOW_ERROR_CALLING_HOOK, {
              errors: response,
            }),
            object_workflow: obj_workflow,
          });
          return;
        }

        const edgeInputSchemas = hookEdges.map(({ input_schema }) => input_schema).filter(Boolean);
        const mergedSchemas = merge({
          allOf: edgeInputSchemas.concat(obj_workflow.input_schema),
        });

        return {
          objWorkflow: obj_workflow,
          object_data: getModelAttributes(obj_workflow.model),
          new_status,
          input_schema: mergedSchemas,
          input_data: mergedInputData,
          hookResponse: success ? response : [],
        };
      }),
    );

    const successfulDispatched = promises
      .filter(
        (p): p is PromiseFulfilledResult<Required<ObjectToChange>> =>
          p.status === 'fulfilled' && !!p.value,
      )
      .map(p => p.value as Required<ObjectToChange>);

    return { failedToDispatch, successfulDispatched };
  }

  private mergeExternalDataWithHookResponse(
    externalData: Record<string, unknown> = {},
    hookResponse: Record<string, unknown>[] = [],
  ): Record<string, unknown> {
    const hookResponseMerged = hookResponse.flat().reduce((acc, res) => ({ ...acc, ...res }), {});
    return { ...externalData, ...hookResponseMerged };
  }

  public async updateWorkflowAndCreateEvents({
    requester,
    toChange,
    reason,
    trx,
  }: {
    requester: AuthenticationContextDto;
    toChange: ObjectToChange[];
    reason: ObjectWorkflowEventsReasons;
    trx?: Transaction;
  }): Promise<SuccessfulTransition[]> {
    const requesterUser = requester.getUserForAudit().credor;

    const eventsToCreate = toChange
      // should not create events for the same status
      .filter(({ new_status, objWorkflow }) => new_status && objWorkflow.status !== new_status)
      .map(obj => {
        const graph = obj.objWorkflow.workflow.getParsedGraph();
        const metadata = {
          old_status_name: graph.getStatusDef(obj.objWorkflow.status)?.name,
          status_name: graph.getStatusDef(obj.new_status)?.name,
        };

        return {
          office_id: requester.escritorio.id_escritorio,
          status: obj.new_status,
          object_workflow_id: obj.objWorkflow.id,
          old_status: obj.objWorkflow.status,
          requester: requesterUser?.id_credor,
          input_data: obj.input_data,
          input_schema: obj.input_schema,
          data: obj.object_data,
          reason,
          metadata,
        };
      });

    const objectsToChange = toChange.map(
      ({ objWorkflow, new_status, hookResponse = [], input_schema, input_data }) => {
        return {
          ...getModelAttributes(objWorkflow),
          external_data: this.mergeExternalDataWithHookResponse(
            objWorkflow.external_data,
            hookResponse,
          ),
          input_data: input_data ? input_data : objWorkflow.input_data,
          input_schema: input_schema ? input_schema : objWorkflow.input_schema,
          status: new_status ? new_status : objWorkflow.status,
        };
      },
    );

    const fn = async (transaction: Transaction) => {
      await Promise.all([
        Object_Workflow.bulkCreate(objectsToChange, {
          transaction,
          updateOnDuplicate: [
            'status',
            'external_data',
            'input_schema',
            'input_data',
            'updated_at',
          ],
        }),
        Object_Workflow_Events.bulkCreate(eventsToCreate, { transaction }),
      ]);

      return toChange.map(({ objWorkflow, new_status }) => {
        objWorkflow.status = new_status;
        return {
          object_workflow: objWorkflow,
          new_status: new_status,
        };
      });
    };

    return trx ? fn(trx) : this.sequelize.transaction(fn);
  }
}
