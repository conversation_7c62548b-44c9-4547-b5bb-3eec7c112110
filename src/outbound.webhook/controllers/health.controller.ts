import {
  Body,
  Controller,
  Get,
  Inject,
  InternalServerErrorException,
  Logger,
  Post,
} from '@nestjs/common';
import { QueryTypes, Sequelize } from 'sequelize';
import { HandlerOutboundWebhooks } from '@outboundWb/handler.outbound.webhook';
import { DATABASE_PROVIDER } from '../../database.provider';

@Controller('outbound-webhook')
export class HealthController {
  private readonly logger = new Logger(HealthController.name);

  constructor(
    @Inject(DATABASE_PROVIDER) private readonly sequelize: Sequelize,
    private readonly outboundWebhookConsumer: HandlerOutboundWebhooks,
  ) {}

  @Get('/health')
  async get(): Promise<{ status: string }> {
    await this.sequelize.query('select 1 = 1;', { type: QueryTypes.SELECT }).catch(e => {
      throw new InternalServerErrorException({ status: 'DOWN', message: e });
    });

    this.outboundWebhookConsumer.checkSubscriptionHealth();

    return { status: 'UP' };
  }

  @Post('/alert')
  async postAlert(@Body() alertPayload: any): Promise<any> {
    const message = '<PERSON><PERSON> called POST';
    this.logger.warn(alertPayload, message);

    return { status: message, alertPayload };
  }
}
