export function getOkStatus(
  statusCode: number,
  wasFetchSuccessful: boolean,
  successfulStatusCodes: string[] = [],
): boolean {
  if (!successfulStatusCodes.length) {
    return wasFetchSuccessful;
  }

  const codeStr = statusCode.toString();

  return successfulStatusCodes.some(pattern => {
    const regexPattern = pattern.replace(/x/g, '\\d');
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(codeStr);
  });
}
