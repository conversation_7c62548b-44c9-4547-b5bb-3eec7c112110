import { OfficeIdOrClientId } from '../../../file.management/file.service';

export interface ContextTransformer {
  officeId: OfficeIdOrClientId;
  /**
   * The body with credentials redacted.
   * This is used to ensure that sensitive information is not exposed in logs or error messages.
   */
  bodyRedacted?: BodyInit;
}

export type PayloadTransformerFn = (
  payload: BodyInit,
  headers: HeadersInit,
  context: ContextTransformer,
) => Promise<{ body: BodyInit; headers: HeadersInit }>;

export type TransformationTypes = 'MultipartFormData';
