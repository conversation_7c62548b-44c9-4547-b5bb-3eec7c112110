import { buffer } from 'stream/consumers';
import { FileService } from '../../../file.management/file.service';
import { ContextTransformer, PayloadTransformerFn } from './types';
import { Logger } from '@nestjs/common';
import { extensionMimeTypes, getExtensionFromFileName } from '../../../utils/string.utils';
import { BUFFER_PREFIX } from 'shared-types';

const MULTIPART_METADATA_MIME = 'multipart/form-data';

function headersToInit(headers: Headers): HeadersInit {
  return Object.fromEntries(headers.entries());
}

type FormTransformerDeps = {
  logger: Logger;
  fileService: FileService;
};

function parseBufferFile(bufferValue: string): {
  id: number;
  type: 'document' | 'file' | 'invalid';
  fileNameOverride?: string;
} {
  const queryString = bufferValue.replaceAll(BUFFER_PREFIX, '');
  const params = new URLSearchParams(queryString);
  const fileNameOverride = params.get('fileName') ?? undefined;

  if (params.has('documentId')) {
    const id = Number(params.get('documentId'));
    if (Number.isNaN(id)) return { id: 0, type: 'invalid' };
    return {
      id,
      type: 'document',
      fileNameOverride,
    };
  }

  if (params.has('fileId')) {
    const id = Number(params.get('fileId'));
    if (Number.isNaN(id)) return { id: 0, type: 'invalid' };
    return { id, type: 'file', fileNameOverride };
  }

  return { id: 0, type: 'invalid' };
}

export const FormTransformer =
  ({ logger, fileService }: FormTransformerDeps): PayloadTransformerFn =>
  async (payload: BodyInit, headers: HeadersInit, { officeId }: ContextTransformer) => {
    const parsedHeaders = new Headers(headers);
    if (parsedHeaders.get('content-type') !== MULTIPART_METADATA_MIME)
      return { body: payload, headers: headers };

    const jsonBody = JSON.parse(String(payload));

    const form = new FormData();

    logger.debug('assembling formdata request');

    for (const key in jsonBody) {
      const value = jsonBody[key];

      if (typeof value === 'object') {
        // can also be array, should we support it?
        logger.error('cant parse nested json');
        throw new Error('cant make form data with nested json structures (yet)');
      }

      if (typeof value === 'undefined') {
        continue;
      }

      if (String(value).startsWith(BUFFER_PREFIX)) {
        const { type, id, fileNameOverride } = parseBufferFile(String(value));
        if (type === 'invalid') continue; // if buffer pattern cant be parsed, ignore field

        logger.debug({ officeId, id }, 'buffer indicator found, will try to search for file');

        const op =
          type === 'document'
            ? await fileService.operatorFromDocumentId(officeId, id)
            : await fileService.operatorFromFileId(officeId, id);

        if (!op.exists) continue; // couldnt find document
        const readable = await op.operator.getFileBuffer(op.fileRef);
        const buf = await buffer(readable); // reads entire file into memory, couldnt find a better way
        const { name: fileName } = op.fileRef;
        const fileExt = getExtensionFromFileName(fileName.toLocaleLowerCase());
        const mime = extensionMimeTypes[fileExt] ?? extensionMimeTypes.default;

        logger.debug(
          { len: buf.length, mime, fName: fileName, fileNameOverride },
          `adding a file to the form field ${key}`,
        );

        form.append(
          key,
          new Blob([buf], { type: mime }),
          fileNameOverride ? `${fileNameOverride}.${fileExt}` : fileName,
        );

        continue;
      }

      form.append(key, String(value));
    }

    parsedHeaders.delete('content-type');
    const newHeaders = headersToInit(parsedHeaders);

    return { body: form, headers: newHeaders };
  };
