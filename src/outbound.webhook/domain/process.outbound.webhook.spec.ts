import { Logger } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { OutboundWebhookAppModule } from '@outboundWb/outbound.webhook.module';
import { ProcessOutboundWebhook } from './process.outbound.webhook';
import { DocumentSource, OutboundWebhookEventsType, OutboundWebhookStatus } from 'shared-types';
import { Outbound_webhook_config } from '@outboundWb/models/outbound_webhook_config';
import { v4 as uuid } from 'uuid';
import { Outbound_webhook_data } from '@outboundWb/models/outbound_webhook_data';
import { setupServer } from 'msw/node';
import { rest } from 'msw';
import { vi } from 'vitest';
import { GooglePubSubService } from '../../services/google/pubsub.service';
import { parseMultipartFormData } from '../../../test/utils/file.utils';
import { createTestOffice } from '../../../test/utils/massa.utils';
import { FileService } from '@app/file.management/file.service';
import { FileAttributes, StorageType } from '@app/models/file';
import { Document } from '@app/models/document';
import { formatISO } from 'date-fns';
import { DatabaseModule } from '@app/database.provider';
import { extensionMimeTypes } from '@app/utils/string.utils';

const createTestOfficeId = uuid;

describe('Get tokens endpoint', () => {
  const server = setupServer();
  let svc: ProcessOutboundWebhook;
  let fileSvc: FileService;

  const mockPubSub = {
    sendMessage: vi.fn().mockResolvedValue(undefined),
  };

  beforeAll(async () => {
    server.listen({ onUnhandledRequest: 'error' });

    const module = await Test.createTestingModule({
      imports: [OutboundWebhookAppModule.forRoot()],
    })
      .overrideProvider(GooglePubSubService)
      .useValue(mockPubSub)
      .overrideModule(DatabaseModule)
      .useModule(DatabaseModule.register())
      .compile();

    const m = await module.init();
    svc = m.get(ProcessOutboundWebhook);
    fileSvc = m.get(FileService);
  });

  afterEach(() => {
    server.resetHandlers();
    mockPubSub.sendMessage.mockClear();
  });

  afterAll(() => {
    server.close();
  });

  type WebhookSut = {
    context?: unknown;
    urlTemplate?: string;
    headerTemplate?: string;
    bodyTemplate?: string;
    method?: string;
    officeId?: string;
  };
  type WebhookSutRet = { config: Outbound_webhook_config; data: Outbound_webhook_data };

  describe('webhook payload transformer', () => {
    const makeSut = async ({
      context,
      urlTemplate,
      headerTemplate,
      bodyTemplate,
      method,
      officeId,
    }: WebhookSut): Promise<WebhookSutRet> => {
      const office = officeId ?? createTestOfficeId();
      const config = await Outbound_webhook_config.create({
        client_id: office,
        name: 'Test_webhook',
        url: urlTemplate ?? 'https://test.api.splitc.com.br',
        method: method ?? 'POST',
        auth: { type: 'none' },
        header_template: headerTemplate ?? '',
        body_template: bodyTemplate ?? '',
      });

      const data = await Outbound_webhook_data.create({
        webhook_config_id: config.id,
        status: OutboundWebhookStatus.PENDING,
        response_status_code: null,
        tries: 0,
        context: context,
        rendered: {},
      });
      data.outboundWebhookConfig = config;
      return { data, config };
    };

    it('doesnt mess with payload if its not multipart-formadata', async () => {
      const { config, data } = await makeSut({
        headerTemplate: `{
            "content-type": "application/json"
        }`,
        bodyTemplate: `
        {
            "name": "test",
            "file_1": "buffer:::documentId=123"
        }
        `,
        context: {},
        urlTemplate: 'https://webhook.site/2bcc1028-8e8c-483c-b419-a3c1f2349211',
      });

      let realOutboundRequestBody;
      server.use(
        rest[config.method.toLocaleLowerCase()](config.url, (req, res, ctx) => {
          realOutboundRequestBody = req.body;
          return res(ctx.body('ok'), ctx.status(200));
        }),
      );

      await svc.processWebhook({
        client_id: config.client_id,
        webhook_data_id: data.id,
        type: OutboundWebhookEventsType.POST_WEBHOOK,
        response_extractor: [],
        successful_status_code: [],
      });

      expect(realOutboundRequestBody).toEqual(JSON.parse(config.body_template));
    });

    describe('form metadata transformer', () => {
      it('fails if rendered payload isnt a json', async () => {
        const { config, data } = await makeSut({
          headerTemplate: `{
            "content-type": "multipart/form-data"
        }`,
          bodyTemplate: `
          <xml>hello</xml>
        `,
          context: {},
          urlTemplate: 'https://webhook.site/2bcc1028-8e8c-483c-b419-a3c1f2349211',
        });

        const capturedRequests = [];
        server.use(
          rest[config.method.toLocaleLowerCase()](config.url, (req, res, ctx) => {
            capturedRequests.push(req);
            return res(ctx.body('ok'), ctx.status(200));
          }),
        );

        await expect(
          svc.processWebhook({
            client_id: config.client_id,
            webhook_data_id: data.id,
            type: OutboundWebhookEventsType.POST_WEBHOOK,
            response_extractor: [],
            successful_status_code: [],
          }),
        ).rejects.toThrowErrorMatchingInlineSnapshot(`[CustomException: Json inválido]`);

        expect(capturedRequests).toHaveLength(0); // shouldnt make the outbound call
      });

      it('fails if rendered body has a nested json field', async () => {
        const { config, data } = await makeSut({
          headerTemplate: `{
            "content-type": "multipart/form-data"
        }`,
          bodyTemplate: `
          <xml>hello</xml>
        `,
          context: {},
          urlTemplate: 'https://webhook.site/2bcc1028-8e8c-483c-b419-a3c1f2349211',
        });

        const capturedRequests = [];
        server.use(
          rest[config.method.toLocaleLowerCase()](config.url, (req, res, ctx) => {
            capturedRequests.push(req);
            return res(ctx.body('ok'), ctx.status(200));
          }),
        );

        await expect(
          svc.processWebhook({
            client_id: config.client_id,
            webhook_data_id: data.id,
            type: OutboundWebhookEventsType.POST_WEBHOOK,
            response_extractor: [],
            successful_status_code: [],
          }),
        ).rejects.toThrowErrorMatchingInlineSnapshot(`[CustomException: Json inválido]`);

        expect(capturedRequests).toHaveLength(0); // shouldnt make the outbound call
      });

      it('sends multipart form data with fields', async () => {
        const { config, data } = await makeSut({
          headerTemplate: `{
            "content-type": "multipart/form-data"
        }`,
          bodyTemplate: `
        {
            "name": "test",
            "another_form_field": "teste"
        }
        `,
          context: {},
          urlTemplate: 'https://webhook.site/2bcc1028-8e8c-483c-b419-a3c1f2349211',
        });

        const capturedRequests = [];
        server.use(
          rest[config.method.toLocaleLowerCase()](config.url, (req, res, ctx) => {
            capturedRequests.push(req);
            return res(ctx.body('ok'), ctx.status(200));
          }),
        );

        await svc.processWebhook({
          client_id: config.client_id,
          webhook_data_id: data.id,
          type: OutboundWebhookEventsType.POST_WEBHOOK,
          response_extractor: [],
          successful_status_code: [],
        });

        expect(capturedRequests).toHaveLength(1); // should make the outbound call

        const request = capturedRequests[0];
        const formData = await parseMultipartFormData(request);

        expect((formData as { fields: unknown }).fields).toEqual(
          expect.objectContaining({
            name: 'test',
            another_form_field: 'teste',
          }),
        );
      });

      it('should override filename when using documentBuffer', async () => {
        const office = await createTestOffice();
        const op = fileSvc.getOperator(StorageType.LOCAL);
        const extension = '.txt';
        const fileName = `${uuid()}_${extension}`;
        const fileNameOverride = `myBadAssFileName`;

        const fileDef: FileAttributes = {
          id: undefined,
          office_id: office.id_escritorio,
          name: fileName,
          storage_name: fileName,
          checksum: '',
          storage: StorageType.LOCAL,
          created_at: new Date(),
          updated_at: new Date(),
        };

        const uploadedFileContent = 'OI';
        const pfRes = await op.putFile(fileDef, Buffer.from(uploadedFileContent, 'utf-8'));
        await pfRes.file.save();

        const doc = await Document.create({
          date: formatISO(new Date(), {
            representation: 'date',
          }),
          file_id: pfRes.file.id,
          name: 'testinho',
          approval: 'APPROVED',
          creditor_id: 1,
          source: DocumentSource.PAYROLL,
          created_by: 1,
        });

        const { config, data } = await makeSut({
          officeId: office.client_id,
          headerTemplate: `{
            "content-type": "multipart/form-data"
        }`,
          bodyTemplate: `
        {
            "file": "{{ documentBuffer ${doc.id} '${fileNameOverride}' }}"
        }
        `,
          context: {},
          urlTemplate: 'https://webhook.site/2bcc1028-8e8c-483c-b419-a3c1f2349211',
        });

        const capturedRequests = [];
        server.use(
          rest[config.method.toLocaleLowerCase()](config.url, (req, res, ctx) => {
            capturedRequests.push(req);
            return res(ctx.body('ok'), ctx.status(200));
          }),
        );

        await svc.processWebhook({
          client_id: config.client_id,
          webhook_data_id: data.id,
          type: OutboundWebhookEventsType.POST_WEBHOOK,
          response_extractor: [],
          successful_status_code: [],
        });

        expect(capturedRequests).toHaveLength(1); // should make the outbound call

        const request = capturedRequests[0];
        const formData = (await parseMultipartFormData(request)) as {
          files: Record<string, any>;
        };

        expect(formData.files.file.filename).toEqual(`${fileNameOverride}${extension}`);
      });

      it('sends a file stream as a form-metadata and checks if the file is correct', async () => {
        const office = await createTestOffice();
        const op = fileSvc.getOperator(StorageType.LOCAL);
        const fileName = `${uuid()}_.txt`;
        const fileDef: FileAttributes = {
          id: undefined,
          office_id: office.id_escritorio,
          name: fileName,
          storage_name: fileName,
          checksum: '',
          storage: StorageType.LOCAL,
          created_at: new Date(),
          updated_at: new Date(),
        };

        const uploadedFileContent = 'OI';
        const pfRes = await op.putFile(fileDef, Buffer.from(uploadedFileContent, 'utf-8'));
        await pfRes.file.save();

        const doc = await Document.create({
          date: formatISO(new Date(), {
            representation: 'date',
          }),
          file_id: pfRes.file.id,
          name: 'testinho',
          approval: 'APPROVED',
          creditor_id: 1,
          source: DocumentSource.PAYROLL,
          created_by: 1,
        });

        const { config, data } = await makeSut({
          officeId: office.client_id,
          headerTemplate: `{
            "content-type": "multipart/form-data"
        }`,
          bodyTemplate: `
        {
            "file": "{{ documentBuffer ${doc.id} }}",
            "another_form_field": "teste"
        }
        `,
          context: {},
          urlTemplate: 'https://webhook.site/2bcc1028-8e8c-483c-b419-a3c1f2349211',
        });

        const capturedRequests = [];
        server.use(
          rest[config.method.toLocaleLowerCase()](config.url, (req, res, ctx) => {
            capturedRequests.push(req);
            return res(ctx.body('ok'), ctx.status(200));
          }),
        );

        await svc.processWebhook({
          client_id: config.client_id,
          webhook_data_id: data.id,
          type: OutboundWebhookEventsType.POST_WEBHOOK,
          response_extractor: [],
          successful_status_code: [],
        });

        expect(capturedRequests).toHaveLength(1); // should make the outbound call

        const request = capturedRequests[0];
        const formData = (await parseMultipartFormData(request)) as {
          fields: unknown;
          files: Record<string, any>;
        };

        expect(formData.fields).toEqual(
          expect.objectContaining({
            another_form_field: 'teste',
          }),
        );

        expect(formData.files.file.filename).toEqual(fileName);
        const fileContent = Buffer.from(formData.files.file.buffer, 'utf-8').toString('utf-8');
        expect(fileContent).toEqual(uploadedFileContent);
      });

      it('sends the correct mimetype based on the file extension', async () => {
        const office = await createTestOffice();
        const op = fileSvc.getOperator(StorageType.LOCAL);
        const fileName = `${uuid()}_.pdf`;
        const fileDef: FileAttributes = {
          id: undefined,
          office_id: office.id_escritorio,
          name: fileName,
          storage_name: fileName,
          checksum: '',
          storage: StorageType.LOCAL,
          created_at: new Date(),
          updated_at: new Date(),
        };

        const uploadedFileContent = 'OI';
        const pfRes = await op.putFile(fileDef, Buffer.from(uploadedFileContent, 'utf-8'));
        await pfRes.file.save();

        const doc = await Document.create({
          date: formatISO(new Date(), {
            representation: 'date',
          }),
          file_id: pfRes.file.id,
          name: 'testinho',
          approval: 'APPROVED',
          creditor_id: 1,
          source: DocumentSource.PAYROLL,
          created_by: 1,
        });

        const { config, data } = await makeSut({
          officeId: office.client_id,
          headerTemplate: `{
            "content-type": "multipart/form-data"
        }`,
          bodyTemplate: `
        {
            "file": "{{ documentBuffer ${doc.id} }}",
            "another_form_field": "teste"
        }
        `,
          context: {},
          urlTemplate: 'https://webhook.site/2bcc1028-8e8c-483c-b419-a3c1f2349211',
        });

        const capturedRequests = [];
        server.use(
          rest[config.method.toLocaleLowerCase()](config.url, (req, res, ctx) => {
            capturedRequests.push(req);
            return res(ctx.body('ok'), ctx.status(200));
          }),
        );

        await svc.processWebhook({
          client_id: config.client_id,
          webhook_data_id: data.id,
          type: OutboundWebhookEventsType.POST_WEBHOOK,
          response_extractor: [],
          successful_status_code: [],
        });

        expect(capturedRequests).toHaveLength(1); // should make the outbound call

        const request = capturedRequests[0];
        const formData = (await parseMultipartFormData(request)) as {
          fields: unknown;
          files: Record<string, any>;
        };

        expect(formData.fields).toEqual(
          expect.objectContaining({
            another_form_field: 'teste',
          }),
        );

        const fileContent = Buffer.from(formData.files.file.buffer, 'utf-8').toString('utf-8');
        expect(fileContent).toEqual(uploadedFileContent);
        expect(formData.files.file.mimetype).toEqual(extensionMimeTypes.pdf);
      });

      it('sends application/octet-stream if file name indicates no type', async () => {
        const office = await createTestOffice();
        const op = fileSvc.getOperator(StorageType.LOCAL);
        const fileName = `${uuid()}_`;
        const fileDef: FileAttributes = {
          id: undefined,
          office_id: office.id_escritorio,
          name: fileName,
          storage_name: fileName,
          checksum: '',
          storage: StorageType.LOCAL,
          created_at: new Date(),
          updated_at: new Date(),
        };

        const uploadedFileContent = 'OI';
        const pfRes = await op.putFile(fileDef, Buffer.from(uploadedFileContent, 'utf-8'));
        await pfRes.file.save();

        const doc = await Document.create({
          date: formatISO(new Date(), {
            representation: 'date',
          }),
          file_id: pfRes.file.id,
          name: 'testinho',
          approval: 'APPROVED',
          creditor_id: 1,
          source: DocumentSource.PAYROLL,
          created_by: 1,
        });

        const { config, data } = await makeSut({
          officeId: office.client_id,
          headerTemplate: `{
            "content-type": "multipart/form-data"
        }`,
          bodyTemplate: `
        {
            "file": "{{ documentBuffer ${doc.id} }}",
            "another_form_field": "teste"
        }
        `,
          context: {},
          urlTemplate: 'https://webhook.site/2bcc1028-8e8c-483c-b419-a3c1f2349211',
        });

        const capturedRequests = [];
        server.use(
          rest[config.method.toLocaleLowerCase()](config.url, (req, res, ctx) => {
            capturedRequests.push(req);
            return res(ctx.body('ok'), ctx.status(200));
          }),
        );

        await svc.processWebhook({
          client_id: config.client_id,
          webhook_data_id: data.id,
          type: OutboundWebhookEventsType.POST_WEBHOOK,
          response_extractor: [],
          successful_status_code: [],
        });

        expect(capturedRequests).toHaveLength(1); // should make the outbound call

        const request = capturedRequests[0];
        const formData = (await parseMultipartFormData(request)) as {
          fields: unknown;
          files: Record<string, any>;
        };

        expect(formData.fields).toEqual(
          expect.objectContaining({
            another_form_field: 'teste',
          }),
        );

        const fileContent = Buffer.from(formData.files.file.buffer, 'utf-8').toString('utf-8');
        expect(fileContent).toEqual(uploadedFileContent);
        expect(formData.files.file.mimetype).toEqual(extensionMimeTypes.default);
      });

      it('converts to multipart form even if header template casing is upper', async () => {
        const { config, data } = await makeSut({
          headerTemplate: `{
            "CONTENT-type": "multipart/form-data"
        }`,
          bodyTemplate: `
        {
            "name": "test",
            "another_form_field": "teste"
        }
        `,
          context: {},
          urlTemplate: 'https://webhook.site/2bcc1028-8e8c-483c-b419-a3c1f2349211',
        });

        const capturedRequests = [];
        server.use(
          rest[config.method.toLocaleLowerCase()](config.url, (req, res, ctx) => {
            capturedRequests.push(req);
            return res(ctx.body('ok'), ctx.status(200));
          }),
        );

        await svc.processWebhook({
          client_id: config.client_id,
          webhook_data_id: data.id,
          type: OutboundWebhookEventsType.POST_WEBHOOK,
          response_extractor: [],
          successful_status_code: [],
        });

        expect(capturedRequests).toHaveLength(1); // should make the outbound call

        const request = capturedRequests[0];
        const formData = await parseMultipartFormData(request);

        expect((formData as { fields: unknown }).fields).toEqual(
          expect.objectContaining({
            name: 'test',
            another_form_field: 'teste',
          }),
        );
      });
    });
  });
});
