import { Injectable, Logger } from '@nestjs/common';
import {
  ContextTransformer,
  FormTransformer,
  PayloadTransformerFn,
  TransformationTypes,
} from './transformers';
import { FileService } from '../../file.management/file.service';

@Injectable()
export class WebhookPayloadTransformer {
  private readonly logger = new Logger(WebhookPayloadTransformer.name);

  constructor(private readonly fileService: FileService) {}

  private readonly _availableTransformations: {
    name: TransformationTypes;
    fn: PayloadTransformerFn;
  }[] = [
    {
      name: 'MultipartFormData',
      fn: FormTransformer({ logger: this.logger, fileService: this.fileService }),
    },
  ];

  public async transformBody(
    body: BodyInit,
    headers: HeadersInit,
    context: ContextTransformer,
  ): Promise<{ body: BodyInit; headers: HeadersInit }> {
    let mutBody = body;
    let mutHeaders = headers;
    for (const { fn, name } of this._availableTransformations) {
      try {
        const { body, headers } = await fn(mutBody, mutHeaders, context);
        mutBody = body;
        mutHeaders = headers;
      } catch (err) {
        this.logger.error(err, `error transforming webhook payload with transformer ${name}`);
        throw err;
      }
    }
    return { body: mutBody, headers: mutHeaders };
  }
}
