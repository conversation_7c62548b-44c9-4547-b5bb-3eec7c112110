import { BadRequestException } from '@nestjs/common';
import { ProcessOutboundWebhook } from '@outboundWb/domain/process.outbound.webhook';
import { WebhookOtherAuth } from 'shared-types';
import { Outbound_webhook_config } from '../../../models/outbound_webhook_config';
import {
  AuthCredentials,
  BaseOutboundWebhookAuth,
  OutboundWebhookAuthParams,
} from '../base.outbound.webhook.auth';

export class OtherOutboundWebhookAuth extends BaseOutboundWebhookAuth<WebhookOtherAuth> {
  constructor(
    auth: OutboundWebhookAuthParams,
    private readonly processOutboundWebhook: ProcessOutboundWebhook,
  ) {
    super(auth);
  }

  public async getCredentials() {
    const outboundWebhook = await Outbound_webhook_config.findOne({
      where: {
        id: this.auth.auth_config_id,
      },
    });

    if (!outboundWebhook) {
      throw new BadRequestException(
        `Outbound webhook config with id ${this.auth.auth_config_id} not found`,
      );
    }

    const { hookResponse } = await this.processOutboundWebhook.dispatchWebhook(
      outboundWebhook,
      outboundWebhook.client_id,
    );

    return hookResponse.data;
  }

  public fillCredentialsOnHeaders(
    headerTemplateRendered: string,
    _credentials: AuthCredentials,
  ): Headers {
    return JSON.parse(headerTemplateRendered);
  }
}
