#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

RED=$'\e[0;31m'
NC=$'\e[0m'

if grep -rnw 'test/' -e 'describe\.only' -e 'it\.only'; then
  echo "${RED} please remove .only"
  exit 1 
fi

if grep -rnw 'src/' -e 'describe\.only' -e 'it\.only'; then
  echo "${RED} please remove .only"
  exit 1 
fi

if grep -rnw 'src/' -e 'import.* from .\@app.*' --exclude '*.spec.ts'; then
  echo "${RED} please use relative imports instead of @app"
  exit 1 
fi

exit 0

